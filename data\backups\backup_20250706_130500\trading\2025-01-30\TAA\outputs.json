[{"timestamp": "2025-07-05T23:13:55.779206", "output_id": "output_20250705_231355_73ad9051", "input_id": "input_20250705_231351_fc4326cd", "prompt_id": "prompt_20250705_231351_b670862f", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal": "neutral", "cross": "no_significant_cross"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:13:55.779206", "processing_time": 3.946479, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal": "neutral", "cross": "no_significant_cross"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:13:55.779206", "processing_time": 3.946479, "llm_used": true}, "processing_time": 3.946479, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 3.946479}}, {"timestamp": "2025-07-05T23:14:33.646145", "output_id": "output_20250705_231433_33ad528c", "input_id": "input_20250705_231429_b3469e01", "prompt_id": "prompt_20250705_231430_87454524", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:33.645145", "processing_time": 3.789072, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:33.645145", "processing_time": 3.789072, "llm_used": true}, "processing_time": 3.789072, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 7.735551}}, {"timestamp": "2025-07-05T23:14:34.625844", "output_id": "output_20250705_231434_35308cce", "input_id": "input_20250705_231429_492aca37", "prompt_id": "prompt_20250705_231429_058f9cd7", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 55, "analysis": "Neutral RSI level indicates that there is no strong overbought or oversold condition."}, "MACD": {"signal_line": 0, "analysis": "MACD signal line is close to the zero line, suggesting a neutral trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, indicating a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:34.624848", "processing_time": 4.795903, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 55, "analysis": "Neutral RSI level indicates that there is no strong overbought or oversold condition."}, "MACD": {"signal_line": 0, "analysis": "MACD signal line is close to the zero line, suggesting a neutral trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, indicating a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:34.624848", "processing_time": 4.795903, "llm_used": true}, "processing_time": 4.795903, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 12.531454}}, {"timestamp": "2025-07-05T23:14:35.260337", "output_id": "output_20250705_231435_769ed088", "input_id": "input_20250705_231430_2c8ad53a", "prompt_id": "prompt_20250705_231430_e8435ef9", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 60, "trend": "upward"}, "MACD": {"signal_line": 0.0, "histogram": {"bullish": true, "cross_above": false}}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 150.0, "trend": "upward"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:35.260337", "processing_time": 5.373724, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 60, "trend": "upward"}, "MACD": {"signal_line": 0.0, "histogram": {"bullish": true, "cross_above": false}}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 150.0, "trend": "upward"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:35.260337", "processing_time": 5.373724, "llm_used": true}, "processing_time": 5.373724, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 17.905178}}, {"timestamp": "2025-07-05T23:14:35.358417", "output_id": "output_20250705_231435_3b0a059c", "input_id": "input_20250705_231429_6f5f8cba", "prompt_id": "prompt_20250705_231429_ceef34b1", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 56, "analysis": "RSI is above 50, indicating a neutral to slightly bullish market."}, "MACD": {"signal_line": 0, "analysis": "MACD line is close to the signal line, suggesting a lack of strong momentum in either direction."}, "Moving_Average": {"50_day_MA": 150, "200_day_MA": 160, "analysis": "The stock is currently below its 50-day and 200-day moving averages, indicating a potential long-term bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:35.358417", "processing_time": 5.587106, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 56, "analysis": "RSI is above 50, indicating a neutral to slightly bullish market."}, "MACD": {"signal_line": 0, "analysis": "MACD line is close to the signal line, suggesting a lack of strong momentum in either direction."}, "Moving_Average": {"50_day_MA": 150, "200_day_MA": 160, "analysis": "The stock is currently below its 50-day and 200-day moving averages, indicating a potential long-term bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:35.358417", "processing_time": 5.587106, "llm_used": true}, "processing_time": 5.587106, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 23.492283999999998}}, {"timestamp": "2025-07-05T23:14:36.383947", "output_id": "output_20250705_231436_59213729", "input_id": "input_20250705_231430_9fa6175e", "prompt_id": "prompt_20250705_231430_f9a130ff", "raw_response": {"analysis_date": "2025-01-30", "trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 130.0, "resistance_level": 135.0, "indicators": {"RSI": {"current_value": 60, "signal": "neutral"}, "MACD": {"signal": "neutral", "cross": "none"}, "Moving_Averages": {"50_day_MA": 132.5, "200_day_MA": 125.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:36.383947", "processing_time": 6.358031, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-30", "trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 130.0, "resistance_level": 135.0, "indicators": {"RSI": {"current_value": 60, "signal": "neutral"}, "MACD": {"signal": "neutral", "cross": "none"}, "Moving_Averages": {"50_day_MA": 132.5, "200_day_MA": 125.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:36.383947", "processing_time": 6.358031, "llm_used": true}, "processing_time": 6.358031, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 29.850315}}, {"timestamp": "2025-07-05T23:14:38.578476", "output_id": "output_20250705_231438_91cae8a2", "input_id": "input_20250705_231430_c73f8e4c", "prompt_id": "prompt_20250705_231430_0fc94f16", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 56, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:38.578476", "processing_time": 8.662247, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 56, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:38.578476", "processing_time": 8.662247, "llm_used": true}, "processing_time": 8.662247, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 38.512562}}, {"timestamp": "2025-07-05T23:14:39.631696", "output_id": "output_20250705_231439_bc8b2bdf", "input_id": "input_20250705_231435_cac31d93", "prompt_id": "prompt_20250705_231435_eade1f68", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "above 50-day and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:39.631696", "processing_time": 3.956004, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "above 50-day and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:39.631696", "processing_time": 3.956004, "llm_used": true}, "processing_time": 3.956004, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 42.468566}}, {"timestamp": "2025-07-05T23:14:39.740276", "output_id": "output_20250705_231439_81306431", "input_id": "input_20250705_231430_c16587d4", "prompt_id": "prompt_20250705_231430_58efa998", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "Neutral - The RSI is neither overbought nor oversold, indicating a stable market condition."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "Neutral - The MACD line is close to the signal line with a slight downward trend, suggesting a lack of strong momentum."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 165.0, "analysis": "Neutral - The stock is currently below the 50-day moving average but above the 200-day moving average, indicating a slightly bearish short-term trend but a strong bullish long-term trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:39.740276", "processing_time": 9.736369, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "Neutral - The RSI is neither overbought nor oversold, indicating a stable market condition."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "Neutral - The MACD line is close to the signal line with a slight downward trend, suggesting a lack of strong momentum."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 165.0, "analysis": "Neutral - The stock is currently below the 50-day moving average but above the 200-day moving average, indicating a slightly bearish short-term trend but a strong bullish long-term trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:39.740276", "processing_time": 9.736369, "llm_used": true}, "processing_time": 9.736369, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 52.204935000000006}}, {"timestamp": "2025-07-05T23:14:40.158921", "output_id": "output_20250705_231440_1df5ab1b", "input_id": "input_20250705_231434_6595cb3a", "prompt_id": "prompt_20250705_231434_5d110d4b", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.02, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:40.158921", "processing_time": 5.298404, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.02, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:40.158921", "processing_time": 5.298404, "llm_used": true}, "processing_time": 5.298404, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 57.503339000000004}}, {"timestamp": "2025-07-05T23:14:40.506156", "output_id": "output_20250705_231440_e9fe0abd", "input_id": "input_20250705_231435_b8da945c", "prompt_id": "prompt_20250705_231435_89ed6038", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 153.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:40.506156", "processing_time": 4.966462, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 153.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:40.506156", "processing_time": 4.966462, "llm_used": true}, "processing_time": 4.966462, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 62.469801000000004}}, {"timestamp": "2025-07-05T23:14:40.598013", "output_id": "output_20250705_231440_21105343", "input_id": "input_20250705_231435_ff4f3691", "prompt_id": "prompt_20250705_231435_e86c251d", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is above 50, indicating a neutral to slightly bullish trend."}, "MACD": {"signal_line": 0.05, "analysis": "The MACD is close to zero, suggesting a neutral trend."}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 160.0, "analysis": "The stock is currently trading below its 50-day and 200-day moving averages, which could indicate a bearish trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:40.598013", "processing_time": 4.849768, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is above 50, indicating a neutral to slightly bullish trend."}, "MACD": {"signal_line": 0.05, "analysis": "The MACD is close to zero, suggesting a neutral trend."}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 160.0, "analysis": "The stock is currently trading below its 50-day and 200-day moving averages, which could indicate a bearish trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:40.598013", "processing_time": 4.849768, "llm_used": true}, "processing_time": 4.849768, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 67.319569}}, {"timestamp": "2025-07-05T23:14:41.519632", "output_id": "output_20250705_231441_f62ee65e", "input_id": "input_20250705_231436_f82ea5bf", "prompt_id": "prompt_20250705_231436_d16b2015", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating that the stock is in a state of accumulation."}, "MACD": {"signal_line": 0.02, "analysis": "MACD is positive and above the signal line, suggesting upward momentum."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:41.519632", "processing_time": 4.67231, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating that the stock is in a state of accumulation."}, "MACD": {"signal_line": 0.02, "analysis": "MACD is positive and above the signal line, suggesting upward momentum."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:41.519632", "processing_time": 4.67231, "llm_used": true}, "processing_time": 4.67231, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 71.991879}}, {"timestamp": "2025-07-05T23:14:42.657568", "output_id": "output_20250705_231442_577816f3", "input_id": "input_20250705_231435_f9702e97", "prompt_id": "prompt_20250705_231435_219381d2", "raw_response": {"trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 120.0, "signal": "above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:42.657568", "processing_time": 7.0314, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 120.0, "signal": "above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:42.657568", "processing_time": 7.0314, "llm_used": true}, "processing_time": 7.0314, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 79.023279}}, {"timestamp": "2025-07-05T23:14:42.691409", "output_id": "output_20250705_231442_7abfdb99", "input_id": "input_20250705_231437_b67f7a75", "prompt_id": "prompt_20250705_231437_715bb2bc", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:42.685893", "processing_time": 4.75932, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:42.685893", "processing_time": 4.75932, "llm_used": true}, "processing_time": 4.75932, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 83.782599}}, {"timestamp": "2025-07-05T23:14:42.758070", "output_id": "output_20250705_231442_72d19e2e", "input_id": "input_20250705_231430_c9ab2535", "prompt_id": "prompt_20250705_231430_91c75fcf", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.4, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 57, "analysis": "RSI is neutral, indicating no overbought or oversold conditions."}, "MACD": {"current_value": "0.00", "analysis": "MACD line is close to the signal line, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, indicating a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:42.758070", "processing_time": 12.801737, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.4, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 57, "analysis": "RSI is neutral, indicating no overbought or oversold conditions."}, "MACD": {"current_value": "0.00", "analysis": "MACD line is close to the signal line, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, indicating a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:42.758070", "processing_time": 12.801737, "llm_used": true}, "processing_time": 12.801737, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 96.58433600000001}}, {"timestamp": "2025-07-05T23:14:43.612776", "output_id": "output_20250705_231443_8427b308", "input_id": "input_20250705_231438_778adeb1", "prompt_id": "prompt_20250705_231438_dc4e14a2", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI is above 70, indicating the stock is in an overbought condition, but the trend is still bullish."}, "MACD": {"current_value": "positive", "comment": "MACD line is above the signal line, suggesting bullish momentum."}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 160.0, "comment": "Stock price above both 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:43.612776", "processing_time": 5.233372, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI is above 70, indicating the stock is in an overbought condition, but the trend is still bullish."}, "MACD": {"current_value": "positive", "comment": "MACD line is above the signal line, suggesting bullish momentum."}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 160.0, "comment": "Stock price above both 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:43.612776", "processing_time": 5.233372, "llm_used": true}, "processing_time": 5.233372, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 101.81770800000001}}, {"timestamp": "2025-07-05T23:14:45.710269", "output_id": "output_20250705_231445_305e82cc", "input_id": "input_20250705_231438_991c22ac", "prompt_id": "prompt_20250705_231438_192601f1", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:45.710269", "processing_time": 7.456946, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:45.710269", "processing_time": 7.456946, "llm_used": true}, "processing_time": 7.456946, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 109.27465400000001}}, {"timestamp": "2025-07-05T23:14:45.998326", "output_id": "output_20250705_231445_9971fa13", "input_id": "input_20250705_231435_ca3cfa81", "prompt_id": "prompt_20250705_231435_3f11d832", "raw_response": {"trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 68, "analysis": "The RSI is above 70, indicating that the stock is in an overbought state, but the trend is still bullish."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD signal line is slightly positive, suggesting that the trend is still bullish and there might be a continuation of the upward movement."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "analysis": "The 50-day moving average is above the 200-day moving average, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:45.998326", "processing_time": 10.969197, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 68, "analysis": "The RSI is above 70, indicating that the stock is in an overbought state, but the trend is still bullish."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD signal line is slightly positive, suggesting that the trend is still bullish and there might be a continuation of the upward movement."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "analysis": "The 50-day moving average is above the 200-day moving average, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:45.998326", "processing_time": 10.969197, "llm_used": true}, "processing_time": 10.969197, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 120.243851}}, {"timestamp": "2025-07-05T23:14:52.695499", "output_id": "output_20250705_231452_4bfef0d9", "input_id": "input_20250705_231444_d0e311ba", "prompt_id": "prompt_20250705_231444_59795f58", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.01, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:52.695499", "processing_time": 7.956655, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.01, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:14:52.695499", "processing_time": 7.956655, "llm_used": true}, "processing_time": 7.956655, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 128.20050600000002}}, {"timestamp": "2025-07-05T23:15:00.377983", "output_id": "output_20250705_231500_daa971c1", "input_id": "input_20250705_231451_69f109f7", "prompt_id": "prompt_20250705_231451_1ca80a4f", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 60, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:00.377983", "processing_time": 8.402739, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 60, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:00.377983", "processing_time": 8.402739, "llm_used": true}, "processing_time": 8.402739, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 136.60324500000002}}, {"timestamp": "2025-07-05T23:15:04.230395", "output_id": "output_20250705_231504_a0729914", "input_id": "input_20250705_231457_f6aa8466", "prompt_id": "prompt_20250705_231457_a5ed5bea", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "interpretation": "indicating a neutral trend"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "MACD is close to zero, suggesting a lack of strong momentum in either direction"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "interpretation": "50-day MA is slightly below the 200-day MA, suggesting a slight bearish bias"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:04.230395", "processing_time": 6.451033, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "interpretation": "indicating a neutral trend"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "MACD is close to zero, suggesting a lack of strong momentum in either direction"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "interpretation": "50-day MA is slightly below the 200-day MA, suggesting a slight bearish bias"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:04.230395", "processing_time": 6.451033, "llm_used": true}, "processing_time": 6.451033, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 143.054278}}, {"timestamp": "2025-07-05T23:15:04.835476", "output_id": "output_20250705_231504_e2691b3d", "input_id": "input_20250705_231459_d67879f1", "prompt_id": "prompt_20250705_231459_50614a8f", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:04.835476", "processing_time": 5.416209, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:04.835476", "processing_time": 5.416209, "llm_used": true}, "processing_time": 5.416209, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 148.47048700000002}}, {"timestamp": "2025-07-05T23:15:05.930317", "output_id": "output_20250705_231505_b70fa9af", "input_id": "input_20250705_231501_3fc3ecac", "prompt_id": "prompt_20250705_231501_b6cf8a62", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:05.930317", "processing_time": 4.709183, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:05.930317", "processing_time": 4.709183, "llm_used": true}, "processing_time": 4.709183, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 153.17967000000002}}, {"timestamp": "2025-07-05T23:15:05.982456", "output_id": "output_20250705_231505_e8244c11", "input_id": "input_20250705_231459_7e7bc4ca", "prompt_id": "prompt_20250705_231459_2f3dcbcd", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:05.974921", "processing_time": 6.222266, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:05.974921", "processing_time": 6.222266, "llm_used": true}, "processing_time": 6.222266, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 159.401936}}, {"timestamp": "2025-07-05T23:15:07.975992", "output_id": "output_20250705_231507_897cdb06", "input_id": "input_20250705_231457_be70a734", "prompt_id": "prompt_20250705_231457_e1cfb100", "raw_response": {"trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:07.975992", "processing_time": 10.947264, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:07.975992", "processing_time": 10.947264, "llm_used": true}, "processing_time": 10.947264, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 170.3492}}, {"timestamp": "2025-07-05T23:15:08.324682", "output_id": "output_20250705_231508_6826e619", "input_id": "input_20250705_231501_28c98bcc", "prompt_id": "prompt_20250705_231501_570a2c76", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 61, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:08.324682", "processing_time": 6.729901, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 61, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:08.324682", "processing_time": 6.729901, "llm_used": true}, "processing_time": 6.729901, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 177.079101}}, {"timestamp": "2025-07-05T23:15:10.999270", "output_id": "output_20250705_231510_434d49e2", "input_id": "input_20250705_231506_3b2ad0a7", "prompt_id": "prompt_20250705_231506_06d22504", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 130.0, "resistance_level": 140.0, "indicators": {"RSI": {"current_value": 56.7, "interpretation": "indicating a stable market"}, "MACD": {"signal_line": 0.02, "interpretation": "suggesting no strong momentum"}, "Moving_Average": {"50_day_MA": 135.0, "200_day_MA": 145.0, "interpretation": "price trading above both MAs, but close to 200-day MA"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:10.999270", "processing_time": 4.672567, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 130.0, "resistance_level": 140.0, "indicators": {"RSI": {"current_value": 56.7, "interpretation": "indicating a stable market"}, "MACD": {"signal_line": 0.02, "interpretation": "suggesting no strong momentum"}, "Moving_Average": {"50_day_MA": 135.0, "200_day_MA": 145.0, "interpretation": "price trading above both MAs, but close to 200-day MA"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:10.999270", "processing_time": 4.672567, "llm_used": true}, "processing_time": 4.672567, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 181.751668}}, {"timestamp": "2025-07-05T23:15:12.185023", "output_id": "output_20250705_231512_3ab6ae5e", "input_id": "input_20250705_231507_3725d2bb", "prompt_id": "prompt_20250705_231507_6b951f1d", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 62, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": -0.02}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:12.185023", "processing_time": 4.833392, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 62, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": -0.02}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:12.185023", "processing_time": 4.833392, "llm_used": true}, "processing_time": 4.833392, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 186.58506}}, {"timestamp": "2025-07-05T23:15:12.993183", "output_id": "output_20250705_231512_0d5ae43b", "input_id": "input_20250705_231505_936402f2", "prompt_id": "prompt_20250705_231505_edc74e7e", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.01, "analysis": "MACD is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:12.993183", "processing_time": 7.384028, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.01, "analysis": "MACD is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:12.993183", "processing_time": 7.384028, "llm_used": true}, "processing_time": 7.384028, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 193.969088}}, {"timestamp": "2025-07-05T23:15:14.332140", "output_id": "output_20250705_231514_8676967f", "input_id": "input_20250705_231505_2dc5d81c", "prompt_id": "prompt_20250705_231505_15a30afe", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "interpretation": "MACD is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:14.332140", "processing_time": 9.321703, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "interpretation": "MACD is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:14.332140", "processing_time": 9.321703, "llm_used": true}, "processing_time": 9.321703, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 203.290791}}, {"timestamp": "2025-07-05T23:15:14.804019", "output_id": "output_20250705_231514_cdd72397", "input_id": "input_20250705_231510_9b40c82e", "prompt_id": "prompt_20250705_231510_60c14d4d", "raw_response": {"trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 130.0, "resistance_level": 140.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "RSI is slightly above neutral (50), indicating slight upward momentum."}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "MACD lines are close to zero, suggesting no clear trend direction."}, "Moving_Average": {"50_day_MA": 135.0, "200_day_MA": 150.0, "interpretation": "Stock price is currently below the 50-day and 200-day moving averages, suggesting a potential downward trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:14.804019", "processing_time": 4.745157, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 130.0, "resistance_level": 140.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "RSI is slightly above neutral (50), indicating slight upward momentum."}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "MACD lines are close to zero, suggesting no clear trend direction."}, "Moving_Average": {"50_day_MA": 135.0, "200_day_MA": 150.0, "interpretation": "Stock price is currently below the 50-day and 200-day moving averages, suggesting a potential downward trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:14.804019", "processing_time": 4.745157, "llm_used": true}, "processing_time": 4.745157, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 208.03594800000002}}, {"timestamp": "2025-07-05T23:15:15.033815", "output_id": "output_20250705_231515_dc4722df", "input_id": "input_20250705_231508_4ed32a75", "prompt_id": "prompt_20250705_231508_324ce69d", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 54, "interpretation": "Neutral - The RSI is neither overbought nor oversold, suggesting that the stock is neither too strong nor too weak."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "Neutral - The MACD signal line is close to zero, indicating that there is no strong trend and the stock is in a consolidating phase."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Neutral - The stock is currently trading around its 50-day moving average but below its 200-day moving average, suggesting a slight bearish bias in the long term."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:15.032820", "processing_time": 6.889955, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 54, "interpretation": "Neutral - The RSI is neither overbought nor oversold, suggesting that the stock is neither too strong nor too weak."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "Neutral - The MACD signal line is close to zero, indicating that there is no strong trend and the stock is in a consolidating phase."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Neutral - The stock is currently trading around its 50-day moving average but below its 200-day moving average, suggesting a slight bearish bias in the long term."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:15:15.032820", "processing_time": 6.889955, "llm_used": true}, "processing_time": 6.889955, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 214.925903}}]