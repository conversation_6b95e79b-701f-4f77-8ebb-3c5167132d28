#!/usr/bin/env python3
"""
增强Shapley值OPRP工作流程综合测试套件

本测试套件提供完整的14天双轨实验周期测试，包括：
1. Shapley值计算准确性验证
2. 提示词优化效果测试
3. A/B测试功能验证
4. 迭代计算逻辑测试
5. 端到端工作流程测试

作者: AI Assistant
创建时间: 2025-07-06
"""

import os
import sys
import json
import unittest
import tempfile
import shutil
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

try:
    from contribution_assessment.enhanced_weekly_opro_manager import EnhancedWeeklyOPROManager, EnhancedOPROConfig
    from contribution_assessment.iterative_shapley_calculator import IterativeShapleyCalculator, IterativeCalculationConfig
    from contribution_assessment.dual_track_experiment_system import DualTrackExperimentSystem
    from data.ab_testing_framework import ABTestingFramework, ABTestConfig
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行测试")
    sys.exit(1)

class TestEnhancedShapleyOPRPWorkflow(unittest.TestCase):
    """增强Shapley值OPRP工作流程测试类"""
    
    def setUp(self):
        """测试前设置"""
        # 创建临时目录
        self.test_dir = tempfile.mkdtemp()
        self.test_data_dir = Path(self.test_dir) / "test_data"
        self.test_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试配置
        self.test_config = {
            "enhanced_shapley_oprp": {
                "cycle_length_days": 14,
                "optimization_phase_days": 7,
                "validation_phase_days": 7,
                "underperforming_threshold": 0.3,
                "statistical_significance_level": 0.05
            },
            "use_only_winning_data": True,
            "min_data_points_per_agent": 3,
            "data_quality_threshold": 0.6,
            "enable_historical_tracking": True,
            "max_historical_weeks": 8,
            "storage": {
                "comprehensive_storage": {
                    "base_dir": str(self.test_data_dir),
                    "enable_database": False
                }
            }
        }
        
        # 测试智能体列表
        self.test_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
        
        # 创建测试日志记录器
        self.logger = self._create_test_logger()
        
        # 初始化测试组件
        self._setup_test_components()
    
    def tearDown(self):
        """测试后清理"""
        try:
            shutil.rmtree(self.test_dir)
        except Exception as e:
            print(f"清理测试目录失败: {e}")
    
    def _create_test_logger(self) -> logging.Logger:
        """创建测试日志记录器"""
        logger = logging.getLogger(f"test_{__name__}")
        logger.setLevel(logging.DEBUG)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _setup_test_components(self):
        """设置测试组件"""
        try:
            # 初始化增强周期管理器
            self.enhanced_manager = EnhancedWeeklyOPROManager(
                config=self.test_config,
                base_data_dir=str(self.test_data_dir),
                logger=self.logger
            )
            
            # 初始化迭代Shapley计算器
            iterative_config = IterativeCalculationConfig(
                use_only_winning_data=True,
                min_data_points_per_agent=3,
                data_quality_threshold=0.6
            )
            
            self.iterative_calculator = IterativeShapleyCalculator(
                base_data_dir=str(self.test_data_dir),
                config=iterative_config,
                logger=self.logger
            )
            
            # 初始化双轨实验系统
            self.dual_track_system = DualTrackExperimentSystem(
                base_data_dir=str(self.test_data_dir),
                logger=self.logger
            )
            
        except Exception as e:
            self.logger.error(f"设置测试组件失败: {e}")
            raise
    
    def test_iterative_shapley_calculator_initialization(self):
        """测试迭代Shapley计算器初始化"""
        self.assertIsNotNone(self.iterative_calculator)
        self.assertEqual(self.iterative_calculator.config.use_only_winning_data, True)
        self.assertEqual(self.iterative_calculator.config.min_data_points_per_agent, 3)
        
        # 检查目录结构
        expected_dirs = [
            "iterative_shapley",
            "experiment_data_records",
            "winning_data_cache",
            "historical_shapley_values",
            "data_quality_reports"
        ]
        
        for dir_name in expected_dirs:
            dir_path = self.test_data_dir / dir_name
            self.assertTrue(dir_path.exists(), f"目录 {dir_name} 应该存在")
    
    def test_experiment_results_registration(self):
        """测试实验结果注册"""
        # 创建模拟实验结果
        experiment_id = "test_experiment_001"
        ab_test_results = {
            "NAA": {
                "agent_selections": {
                    "NAA": {
                        "selected_prompt": "optimized",
                        "reason": "Better performance",
                        "statistical_data": {"p_value": 0.03, "effect_size": 0.15},
                        "prompt_content": "Optimized NAA prompt"
                    }
                }
            },
            "TAA": {
                "agent_selections": {
                    "TAA": {
                        "selected_prompt": "original",
                        "reason": "No significant improvement",
                        "statistical_data": {"p_value": 0.12, "effect_size": 0.05},
                        "prompt_content": "Original TAA prompt"
                    }
                }
            }
        }
        
        performance_data = {
            "NAA": {
                "sharpe_ratio": 1.25,
                "total_return": 0.08,
                "volatility": 0.15,
                "max_drawdown": -0.05
            },
            "TAA": {
                "sharpe_ratio": 0.95,
                "total_return": 0.06,
                "volatility": 0.18,
                "max_drawdown": -0.08
            }
        }
        
        # 注册实验结果
        success = self.iterative_calculator.register_experiment_results(
            experiment_id=experiment_id,
            ab_test_results=ab_test_results,
            performance_data=performance_data
        )
        
        self.assertTrue(success, "实验结果注册应该成功")
        
        # 验证数据是否正确存储
        self.assertIn(experiment_id, self.iterative_calculator.experiment_data_records)
        
        records = self.iterative_calculator.experiment_data_records[experiment_id]
        self.assertEqual(len(records), 2, "应该有2条实验记录")
        
        # 验证获胜数据缓存
        naa_cache = self.iterative_calculator.winning_data_cache.get("NAA", [])
        self.assertEqual(len(naa_cache), 1, "NAA应该有1条获胜记录")
        self.assertTrue(naa_cache[0].is_winning, "NAA记录应该标记为获胜")
        
        taa_cache = self.iterative_calculator.winning_data_cache.get("TAA", [])
        # TAA选择了原始提示词，如果use_only_winning_data=True，则不应该在缓存中
        # 但如果use_only_winning_data=False，则应该在缓存中
        if self.iterative_calculator.config.use_only_winning_data:
            self.assertEqual(len(taa_cache), 0, "TAA不应该有获胜记录（选择了原始提示词）")
        else:
            self.assertEqual(len(taa_cache), 1, "TAA应该有1条记录")
    
    def test_iterative_shapley_calculation(self):
        """测试迭代Shapley值计算"""
        # 首先注册一些实验结果
        self._register_sample_experiment_results()
        
        # 执行迭代Shapley计算
        result = self.iterative_calculator.calculate_iterative_shapley_values(
            target_agents=["NAA", "TAA", "FAA"],
            use_cached_data=True
        )
        
        self.assertTrue(result.get("success"), "迭代Shapley计算应该成功")
        self.assertIn("shapley_values", result)
        self.assertIn("calculation_metadata", result)
        self.assertIn("data_quality_report", result)
        
        shapley_values = result["shapley_values"]
        self.assertIsInstance(shapley_values, dict)
        
        # 验证所有目标智能体都有Shapley值
        for agent in ["NAA", "TAA", "FAA"]:
            self.assertIn(agent, shapley_values)
            self.assertIsInstance(shapley_values[agent], (int, float))
    
    def test_data_quality_assessment(self):
        """测试数据质量评估"""
        # 注册一些测试数据
        self._register_sample_experiment_results()
        
        # 执行数据质量评估
        quality_report = self.iterative_calculator.get_data_quality_report(
            target_agents=["NAA", "TAA", "FAA"]
        )
        
        self.assertIn("agent_data_coverage", quality_report)
        self.assertIn("data_freshness", quality_report)
        self.assertIn("data_consistency", quality_report)
        self.assertIn("overall_quality", quality_report)
        self.assertIn("detailed_statistics", quality_report)
        
        # 验证质量分数在合理范围内
        overall_quality = quality_report["overall_quality"]
        self.assertGreaterEqual(overall_quality, 0.0)
        self.assertLessEqual(overall_quality, 1.0)
    
    def test_dual_track_experiment_system(self):
        """测试双轨实验系统"""
        # 创建双轨实验
        experiment_config = {
            "experiment_name": "test_dual_track",
            "target_agents": ["NAA", "TAA"],
            "duration_days": 7,
            "original_prompts": {
                "NAA": "Original NAA prompt",
                "TAA": "Original TAA prompt"
            },
            "optimized_prompts": {
                "NAA": "Optimized NAA prompt",
                "TAA": "Optimized TAA prompt"
            }
        }
        
        experiment_id = self.dual_track_system.create_dual_track_experiment(experiment_config)
        self.assertIsNotNone(experiment_id, "双轨实验创建应该成功")
        
        # 模拟记录每日数据
        for day in range(1, 8):
            daily_data = {
                "original_track": {
                    "NAA": {"return": 0.01 * day, "sharpe_ratio": 0.8 + 0.1 * day},
                    "TAA": {"return": 0.008 * day, "sharpe_ratio": 0.7 + 0.08 * day}
                },
                "optimized_track": {
                    "NAA": {"return": 0.012 * day, "sharpe_ratio": 0.9 + 0.12 * day},
                    "TAA": {"return": 0.009 * day, "sharpe_ratio": 0.75 + 0.09 * day}
                }
            }
            
            success = self.dual_track_system.record_daily_experiment_data(
                experiment_id, str(day), daily_data
            )
            self.assertTrue(success, f"第{day}天数据记录应该成功")
        
        # 计算实验指标
        metrics = self.dual_track_system.calculate_experiment_metrics(experiment_id)
        self.assertIsNotNone(metrics, "实验指标计算应该成功")
        self.assertIn("original_track_metrics", metrics)
        self.assertIn("optimized_track_metrics", metrics)
        
        # 比较轨道性能
        comparison = self.dual_track_system.compare_track_performance(experiment_id)
        self.assertIsNotNone(comparison, "轨道性能比较应该成功")
        self.assertIn("performance_comparison", comparison)
        self.assertIn("statistical_analysis", comparison)
    
    def _register_sample_experiment_results(self):
        """注册示例实验结果"""
        experiments = [
            {
                "experiment_id": "sample_exp_001",
                "ab_test_results": {
                    "NAA": {
                        "agent_selections": {
                            "NAA": {
                                "selected_prompt": "optimized",
                                "reason": "Significant improvement",
                                "statistical_data": {"p_value": 0.02, "effect_size": 0.2},
                                "prompt_content": "Optimized NAA prompt v1"
                            }
                        }
                    },
                    "TAA": {
                        "agent_selections": {
                            "TAA": {
                                "selected_prompt": "optimized",
                                "reason": "Better performance",
                                "statistical_data": {"p_value": 0.04, "effect_size": 0.15},
                                "prompt_content": "Optimized TAA prompt v1"
                            }
                        }
                    },
                    "FAA": {
                        "agent_selections": {
                            "FAA": {
                                "selected_prompt": "original",
                                "reason": "No improvement",
                                "statistical_data": {"p_value": 0.15, "effect_size": 0.03},
                                "prompt_content": "Original FAA prompt"
                            }
                        }
                    }
                },
                "performance_data": {
                    "NAA": {"sharpe_ratio": 1.3, "total_return": 0.09, "volatility": 0.14},
                    "TAA": {"sharpe_ratio": 1.1, "total_return": 0.07, "volatility": 0.16},
                    "FAA": {"sharpe_ratio": 0.8, "total_return": 0.05, "volatility": 0.20}
                }
            },
            {
                "experiment_id": "sample_exp_002",
                "ab_test_results": {
                    "NAA": {
                        "agent_selections": {
                            "NAA": {
                                "selected_prompt": "optimized",
                                "reason": "Consistent improvement",
                                "statistical_data": {"p_value": 0.01, "effect_size": 0.25},
                                "prompt_content": "Optimized NAA prompt v2"
                            }
                        }
                    },
                    "TAA": {
                        "agent_selections": {
                            "TAA": {
                                "selected_prompt": "original",
                                "reason": "Mixed results",
                                "statistical_data": {"p_value": 0.08, "effect_size": 0.08},
                                "prompt_content": "Original TAA prompt"
                            }
                        }
                    },
                    "FAA": {
                        "agent_selections": {
                            "FAA": {
                                "selected_prompt": "optimized",
                                "reason": "Marginal improvement",
                                "statistical_data": {"p_value": 0.045, "effect_size": 0.12},
                                "prompt_content": "Optimized FAA prompt v1"
                            }
                        }
                    }
                },
                "performance_data": {
                    "NAA": {"sharpe_ratio": 1.4, "total_return": 0.10, "volatility": 0.13},
                    "TAA": {"sharpe_ratio": 0.9, "total_return": 0.06, "volatility": 0.18},
                    "FAA": {"sharpe_ratio": 0.95, "total_return": 0.065, "volatility": 0.19}
                }
            }
        ]
        
        for exp in experiments:
            success = self.iterative_calculator.register_experiment_results(
                experiment_id=exp["experiment_id"],
                ab_test_results=exp["ab_test_results"],
                performance_data=exp["performance_data"]
            )
            self.assertTrue(success, f"实验 {exp['experiment_id']} 注册应该成功")

    def test_enhanced_weekly_opro_manager_initialization(self):
        """测试增强周期管理器初始化"""
        self.assertIsNotNone(self.enhanced_manager)
        self.assertIsNotNone(self.enhanced_manager.iterative_shapley_calculator)
        self.assertIsNotNone(self.enhanced_manager.dual_track_system)
        self.assertIsNotNone(self.enhanced_manager.ab_testing_framework)

        # 验证配置
        config = self.enhanced_manager.enhanced_config
        self.assertEqual(config.cycle_length_days, 14)
        self.assertEqual(config.optimization_phase_days, 7)
        self.assertEqual(config.validation_phase_days, 7)
        self.assertEqual(config.underperforming_threshold, 0.3)

    def test_underperforming_agents_identification(self):
        """测试低表现智能体识别"""
        # 创建模拟Shapley结果
        shapley_results = {
            "shapley_values": {
                "NAA": 0.25,
                "TAA": 0.20,
                "FAA": 0.15,
                "BOA": 0.12,
                "BeOA": 0.10,
                "NOA": 0.08,
                "TRA": 0.05
            }
        }

        # 识别低表现智能体（底部30%）
        underperforming = self.enhanced_manager._identify_underperforming_agents(shapley_results)

        # 验证结果
        self.assertIsInstance(underperforming, list)
        expected_count = max(1, int(len(self.test_agents) * 0.3))  # 至少1个
        self.assertEqual(len(underperforming), expected_count)

        # 验证是否选择了最低的智能体
        expected_underperforming = ["TRA", "NOA"]  # 最低的两个
        self.assertEqual(underperforming, expected_underperforming)

    def test_cycle_phase_management(self):
        """测试周期阶段管理"""
        # 模拟开始新周期
        current_date = datetime.now().strftime("%Y-%m-%d")

        # 测试优化阶段
        phase = self.enhanced_manager._determine_cycle_phase(current_date)

        if phase:
            self.assertIn(phase.phase_name, ["optimization", "validation"])
            self.assertGreaterEqual(phase.day_in_phase, 1)
            self.assertLessEqual(phase.day_in_phase, phase.total_phase_days)

    def test_experiment_results_registration_integration(self):
        """测试实验结果注册集成"""
        # 创建模拟实验数据
        experiment_data = {
            "experiment_id": "integration_test_001",
            "agents": ["NAA", "TAA"],
            "duration": 7
        }

        performance_comparison = {
            "track_comparisons": {
                "NAA_original_vs_optimized": {
                    "metrics": {
                        "sharpe_ratio": 1.2,
                        "total_return": 0.08,
                        "volatility": 0.15,
                        "max_drawdown": -0.06
                    }
                },
                "TAA_original_vs_optimized": {
                    "metrics": {
                        "sharpe_ratio": 0.9,
                        "total_return": 0.05,
                        "volatility": 0.18,
                        "max_drawdown": -0.08
                    }
                }
            }
        }

        prompt_selection_result = {
            "agent_selections": {
                "NAA": {
                    "selected_prompt": "optimized",
                    "reason": "Better Sharpe ratio",
                    "statistical_data": {"p_value": 0.02, "effect_size": 0.18},
                    "prompt_content": "Optimized NAA prompt"
                },
                "TAA": {
                    "selected_prompt": "original",
                    "reason": "No significant improvement",
                    "statistical_data": {"p_value": 0.12, "effect_size": 0.05},
                    "prompt_content": "Original TAA prompt"
                }
            }
        }

        # 测试注册方法
        success = self.enhanced_manager._register_experiment_results_to_iterative_calculator(
            experiment_data, performance_comparison, prompt_selection_result
        )

        self.assertTrue(success, "实验结果注册应该成功")

        # 验证数据是否正确注册到迭代计算器
        self.assertGreater(len(self.enhanced_manager.iterative_shapley_calculator.experiment_data_records), 0)

    def test_full_14_day_cycle_simulation(self):
        """测试完整14天周期模拟"""
        self.logger.info("开始14天完整周期模拟测试...")

        # 模拟14天的数据
        cycle_start = datetime.now()
        daily_results = {}

        for day in range(1, 15):  # 14天
            current_date = (cycle_start + timedelta(days=day-1)).strftime("%Y-%m-%d")

            # 模拟每日交易结果
            daily_results[current_date] = {
                "agents": {
                    agent: {
                        "return": 0.001 * day + 0.002 * hash(agent) % 10 / 1000,
                        "sharpe_ratio": 0.8 + 0.1 * day / 14 + 0.2 * hash(agent) % 10 / 100,
                        "trades": day + hash(agent) % 5
                    }
                    for agent in self.test_agents
                },
                "market_conditions": {
                    "volatility": 0.15 + 0.05 * (day % 7) / 7,
                    "trend": "bullish" if day % 3 == 0 else "neutral"
                }
            }

        # 模拟周期执行
        cycle_results = []

        for day in range(1, 15):
            current_date = (cycle_start + timedelta(days=day-1)).strftime("%Y-%m-%d")

            # 获取当前阶段
            phase = self.enhanced_manager._determine_cycle_phase(current_date)

            if phase:
                if phase.phase_name == "optimization":
                    # 在优化阶段的最后一天执行Shapley分析
                    if phase.is_complete:
                        self.logger.info(f"第{day}天: 优化阶段完成，执行Shapley分析...")

                        # 模拟Shapley分析结果
                        mock_shapley_result = {
                            "success": True,
                            "shapley_values": {
                                agent: 0.1 + 0.05 * hash(agent) % 10 / 10
                                for agent in self.test_agents
                            },
                            "calculation_metadata": {
                                "data_source": "simulated_test",
                                "calculation_time": current_date
                            }
                        }

                        # 识别低表现智能体
                        underperforming = self.enhanced_manager._identify_underperforming_agents(
                            mock_shapley_result
                        )

                        cycle_results.append({
                            "day": day,
                            "phase": "optimization_complete",
                            "shapley_analysis": mock_shapley_result,
                            "underperforming_agents": underperforming
                        })

                elif phase.phase_name == "validation":
                    self.logger.info(f"第{day}天: 验证阶段进行中...")

                    if phase.is_complete:
                        self.logger.info(f"第{day}天: 验证阶段完成，周期结束")

                        cycle_results.append({
                            "day": day,
                            "phase": "validation_complete",
                            "cycle_complete": True
                        })

        # 验证周期结果
        self.assertGreater(len(cycle_results), 0, "应该有周期结果")

        # 验证是否有优化阶段完成的记录
        optimization_complete = any(r.get("phase") == "optimization_complete" for r in cycle_results)
        self.assertTrue(optimization_complete, "应该有优化阶段完成的记录")

        # 验证是否有验证阶段完成的记录
        validation_complete = any(r.get("phase") == "validation_complete" for r in cycle_results)
        self.assertTrue(validation_complete, "应该有验证阶段完成的记录")

        self.logger.info("14天完整周期模拟测试完成")

if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestEnhancedShapleyOPRPWorkflow)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    print(f"\n{'='*60}")
    print(f"测试摘要:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    print(f"{'='*60}")
