# 增强Shapley值OPRP工作流程操作指南

## 概述

本指南详细介绍了增强Shapley值OPRP（Optimization by PROmpting with Preference）工作流程的操作方法，包括系统配置、执行命令、结果解读和故障排除。

### 系统架构概览

增强Shapley值OPRP系统采用14天双轨实验架构：
- **第1-7天（优化阶段）**：执行Shapley值分析，识别低表现智能体，应用OPRP优化
- **第8-14天（验证阶段）**：运行双轨A/B测试，比较原始vs优化提示词性能
- **迭代优化**：仅使用获胜实验数据进行后续Shapley分析

## 快速开始

### 1. 环境准备

```bash
# 确保Python环境
python --version  # 需要Python 3.8+

# 安装依赖
pip install -r requirements.txt

# 验证安装
python -c "import numpy, scipy, pandas; print('依赖安装成功')"
```

### 2. 基本配置

创建配置文件 `config/enhanced_shapley_oprp_config.json`：

```json
{
  "enhanced_shapley_oprp": {
    "cycle_length_days": 14,
    "optimization_phase_days": 7,
    "validation_phase_days": 7,
    "underperforming_threshold": 0.3,
    "statistical_significance_level": 0.05
  },
  "use_only_winning_data": true,
  "min_data_points_per_agent": 5,
  "data_quality_threshold": 0.8,
  "enable_historical_tracking": true,
  "max_historical_weeks": 12,
  "storage": {
    "comprehensive_storage": {
      "base_dir": "data/trading",
      "enable_database": false
    }
  }
}
```

### 3. 启动系统

```python
from contribution_assessment.enhanced_weekly_opro_manager import EnhancedWeeklyOPROManager
import json

# 加载配置
with open('config/enhanced_shapley_oprp_config.json', 'r') as f:
    config = json.load(f)

# 初始化管理器
manager = EnhancedWeeklyOPROManager(
    config=config,
    base_data_dir="data/trading"
)

# 执行日常周期
current_date = "2025-07-06"
target_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
daily_results = {}  # 从交易系统获取

result = manager.execute_daily_cycle(current_date, target_agents, daily_results)
print(f"周期执行结果: {result['status']}")
```

## 详细操作指南

### 系统初始化

#### 1. 创建增强周期管理器

```python
from contribution_assessment.enhanced_weekly_opro_manager import EnhancedWeeklyOPROManager

manager = EnhancedWeeklyOPROManager(
    config=config,
    base_data_dir="data/trading",
    logger=your_logger  # 可选
)
```

#### 2. 验证系统组件

```python
# 检查核心组件
assert manager.iterative_shapley_calculator is not None
assert manager.dual_track_system is not None
assert manager.ab_testing_framework is not None

print("✅ 系统组件初始化成功")
```

### 日常操作流程

#### 1. 执行日常周期

```python
# 准备每日数据
daily_results = {
    "agents": {
        "NAA": {"return": 0.012, "sharpe_ratio": 1.25, "trades": 15},
        "TAA": {"return": 0.008, "sharpe_ratio": 0.95, "trades": 12},
        # ... 其他智能体
    },
    "market_conditions": {
        "volatility": 0.18,
        "trend": "bullish"
    }
}

# 执行周期
result = manager.execute_daily_cycle(
    current_date="2025-07-06",
    target_agents=["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"],
    daily_results=daily_results
)

# 处理结果
if result["status"] == "optimization_complete":
    print(f"🎯 优化阶段完成，识别到低表现智能体: {result['underperforming_agents']}")
elif result["status"] == "validation_complete":
    print(f"✅ 验证阶段完成，周期结束")
    print(f"获胜提示词: {result.get('winning_prompts', {})}")
```

#### 2. 手动触发Shapley分析

```python
# 使用迭代Shapley计算器
shapley_result = manager.iterative_shapley_calculator.calculate_iterative_shapley_values(
    target_agents=["NAA", "TAA", "FAA"],
    use_cached_data=True
)

if shapley_result["success"]:
    print("Shapley值计算结果:")
    for agent, value in shapley_result["shapley_values"].items():
        print(f"  {agent}: {value:.4f}")
else:
    print(f"❌ Shapley计算失败: {shapley_result.get('error')}")
```

#### 3. 创建双轨实验

```python
# 配置实验
experiment_config = {
    "experiment_name": "manual_test_experiment",
    "target_agents": ["NAA", "TAA"],
    "duration_days": 7,
    "original_prompts": {
        "NAA": "你是一个新闻分析智能体...",
        "TAA": "你是一个技术分析智能体..."
    },
    "optimized_prompts": {
        "NAA": "你是一个高级新闻分析智能体，专注于...",
        "TAA": "你是一个专业技术分析智能体，擅长..."
    }
}

# 创建实验
experiment_id = manager.dual_track_system.create_dual_track_experiment(experiment_config)
print(f"实验创建成功: {experiment_id}")

# 记录每日数据
for day in range(1, 8):
    daily_data = {
        "original_track": {
            "NAA": {"return": 0.01, "sharpe_ratio": 0.9},
            "TAA": {"return": 0.008, "sharpe_ratio": 0.8}
        },
        "optimized_track": {
            "NAA": {"return": 0.012, "sharpe_ratio": 1.1},
            "TAA": {"return": 0.009, "sharpe_ratio": 0.85}
        }
    }
    
    manager.dual_track_system.record_daily_experiment_data(
        experiment_id, day, daily_data
    )

# 分析结果
comparison = manager.dual_track_system.compare_track_performance(experiment_id)
print(f"实验结果: {comparison}")
```

### 数据管理

#### 1. 查看数据质量报告

```python
quality_report = manager.iterative_shapley_calculator.get_data_quality_report(
    target_agents=["NAA", "TAA", "FAA"]
)

print(f"数据质量评分: {quality_report['overall_quality']:.2f}")
print(f"数据覆盖率: {quality_report['agent_data_coverage']}")
print(f"数据新鲜度: {quality_report['data_freshness']}")
```

#### 2. 清理历史数据

```python
# 清理过期的实验数据（保留最近12周）
cleaned_count = manager.iterative_shapley_calculator.cleanup_historical_data(
    max_weeks_to_keep=12
)
print(f"清理了 {cleaned_count} 条过期记录")
```

#### 3. 导出实验结果

```python
# 导出特定实验的详细数据
experiment_data = manager.iterative_shapley_calculator.export_experiment_data(
    experiment_id="specific_experiment_id",
    include_raw_data=True
)

# 保存到文件
import json
with open(f"exports/experiment_{experiment_id}.json", 'w') as f:
    json.dump(experiment_data, f, indent=2)
```

## 配置参数详解

### 核心配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `cycle_length_days` | int | 14 | 完整周期长度（天） |
| `optimization_phase_days` | int | 7 | 优化阶段长度（天） |
| `validation_phase_days` | int | 7 | 验证阶段长度（天） |
| `underperforming_threshold` | float | 0.3 | 低表现智能体阈值（底部30%） |
| `statistical_significance_level` | float | 0.05 | 统计显著性水平 |

### 迭代Shapley配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `use_only_winning_data` | bool | true | 是否仅使用获胜实验数据 |
| `min_data_points_per_agent` | int | 5 | 每个智能体最小数据点数 |
| `data_quality_threshold` | float | 0.8 | 数据质量阈值 |
| `enable_historical_tracking` | bool | true | 是否启用历史跟踪 |
| `max_historical_weeks` | int | 12 | 最大历史保留周数 |

### 存储配置

```json
{
  "storage": {
    "comprehensive_storage": {
      "base_dir": "data/trading",
      "enable_database": false,
      "backup_enabled": true,
      "compression_enabled": true
    }
  }
}
```

## 结果解读

### 1. 周期执行结果

```python
# 优化阶段完成
{
  "status": "optimization_complete",
  "phase": "optimization",
  "shapley_analysis": {
    "success": true,
    "shapley_values": {"NAA": 0.25, "TAA": 0.20, ...},
    "calculation_metadata": {...}
  },
  "underperforming_agents": ["TRA", "NOA"],
  "optimization_result": {...},
  "experiment_config": {...},
  "next_phase": "validation"
}

# 验证阶段完成
{
  "status": "validation_complete",
  "phase": "validation",
  "experiment_results": {...},
  "performance_comparison": {...},
  "winning_prompts": {
    "TRA": "optimized",
    "NOA": "original"
  },
  "cycle_complete": true
}
```

### 2. Shapley值解读

- **正值**：智能体对整体性能有正贡献
- **负值**：智能体对整体性能有负影响
- **相对大小**：值越大，贡献越大
- **阈值判断**：底部20-30%的智能体被标记为低表现

### 3. A/B测试结果

```python
{
  "performance_comparison": {
    "track_comparisons": {
      "agent_original_vs_optimized": {
        "metrics": {
          "sharpe_ratio": 1.2,
          "total_return": 0.08,
          "volatility": 0.15,
          "max_drawdown": -0.06
        },
        "statistical_significance": {
          "p_value": 0.03,
          "is_significant": true,
          "effect_size": 0.18
        }
      }
    }
  },
  "recommendations": {
    "agent_id": {
      "selected_prompt": "optimized",
      "reason": "显著性能提升",
      "confidence": 0.95
    }
  }
}
```

## 故障排除

### 常见问题

#### 1. Shapley计算失败

**症状**：`shapley_result["success"] == False`

**可能原因**：
- 数据质量不足
- 缓存数据过少
- 智能体数据缺失

**解决方案**：
```python
# 检查数据质量
quality_report = manager.iterative_shapley_calculator.get_data_quality_report(target_agents)
if quality_report["overall_quality"] < 0.8:
    print("数据质量不足，需要更多历史数据")

# 降级到传统Shapley分析
traditional_result = manager.shapley_trigger.trigger_shapley_analysis(
    week_number=week_number,
    force_trigger=True
)
```

#### 2. 实验创建失败

**症状**：`create_dual_track_experiment` 返回 `None`

**可能原因**：
- 配置参数无效
- 存储目录权限问题
- 智能体列表为空

**解决方案**：
```python
# 验证配置
required_keys = ["experiment_name", "target_agents", "duration_days"]
for key in required_keys:
    assert key in experiment_config, f"缺少必需参数: {key}"

# 检查存储目录
import os
os.makedirs("data/trading/dual_track_experiments", exist_ok=True)
```

#### 3. 数据质量警告

**症状**：数据质量评分低于阈值

**解决方案**：
```python
# 增加数据收集
manager.iterative_shapley_calculator.config.min_data_points_per_agent = 3

# 降低质量阈值（临时）
manager.iterative_shapley_calculator.config.data_quality_threshold = 0.6

# 清理异常数据
manager.iterative_shapley_calculator.cleanup_outlier_data(z_score_threshold=3.0)
```

### 日志分析

#### 启用详细日志

```python
import logging

# 设置日志级别
logging.getLogger("EnhancedWeeklyOPROManager").setLevel(logging.DEBUG)
logging.getLogger("IterativeShapleyCalculator").setLevel(logging.DEBUG)

# 添加文件日志
handler = logging.FileHandler("logs/enhanced_shapley_oprp.log")
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)

logger = logging.getLogger("EnhancedWeeklyOPROManager")
logger.addHandler(handler)
```

#### 关键日志模式

```
# 正常操作
INFO - 🚀 开始执行日常周期
INFO - 📊 执行优化阶段 - 第7/7天
INFO - 🎯 优化阶段完成，开始Shapley分析和OPRP优化...
INFO - ✅ 迭代Shapley计算成功

# 警告信息
WARNING - 迭代Shapley计算失败，尝试传统Shapley分析...
WARNING - 数据质量不足，当前评分: 0.65

# 错误信息
ERROR - ❌ 传统Shapley分析也失败，降级到传统优化方法
ERROR - ❌ 实验结果注册失败: experiment_001
```

## 性能优化

### 1. 缓存优化

```python
# 预加载常用数据
manager.iterative_shapley_calculator.preload_agent_data(
    target_agents=["NAA", "TAA", "FAA"],
    weeks_back=4
)

# 启用数据压缩
manager.iterative_shapley_calculator.enable_data_compression(True)
```

### 2. 并行处理

```python
# 启用多线程Shapley计算
manager.iterative_shapley_calculator.config.enable_parallel_calculation = True
manager.iterative_shapley_calculator.config.max_workers = 4
```

### 3. 内存管理

```python
# 定期清理内存缓存
manager.iterative_shapley_calculator.clear_memory_cache()

# 设置最大缓存大小
manager.iterative_shapley_calculator.config.max_cache_size_mb = 512
```

## 维护指南

### 定期维护任务

#### 每周维护

```bash
# 运行数据质量检查
python scripts/weekly_data_quality_check.py

# 清理临时文件
python scripts/cleanup_temp_files.py

# 备份重要数据
python scripts/backup_experiment_data.py
```

#### 每月维护

```bash
# 归档历史数据
python scripts/archive_historical_data.py --months-back=3

# 性能基准测试
python scripts/performance_benchmark.py

# 生成月度报告
python scripts/generate_monthly_report.py
```

### 系统监控

#### 关键指标监控

```python
# 监控脚本示例
def monitor_system_health():
    metrics = {
        "data_quality_score": manager.iterative_shapley_calculator.get_overall_data_quality(),
        "active_experiments": len(manager.dual_track_system.get_active_experiments()),
        "cache_hit_rate": manager.iterative_shapley_calculator.get_cache_statistics()["hit_rate"],
        "memory_usage_mb": manager.get_memory_usage()
    }
    
    # 检查阈值
    if metrics["data_quality_score"] < 0.7:
        send_alert("数据质量低于阈值")
    
    if metrics["memory_usage_mb"] > 1024:
        send_alert("内存使用过高")
    
    return metrics
```

### 升级指南

#### 版本兼容性检查

```python
def check_compatibility():
    """检查系统兼容性"""
    import sys
    
    # Python版本检查
    if sys.version_info < (3, 8):
        raise RuntimeError("需要Python 3.8或更高版本")
    
    # 依赖版本检查
    import numpy, scipy, pandas
    print(f"NumPy: {numpy.__version__}")
    print(f"SciPy: {scipy.__version__}")
    print(f"Pandas: {pandas.__version__}")
    
    # 数据格式兼容性
    manager.iterative_shapley_calculator.validate_data_format()
    
    print("✅ 兼容性检查通过")
```

---

## 联系支持

如遇到问题，请提供以下信息：
1. 错误日志（最近100行）
2. 系统配置文件
3. 数据质量报告
4. 复现步骤

技术支持：请参考项目README或提交Issue到代码仓库。
