[{"timestamp": "2025-07-06T14:08:09.661568", "output_id": "output_20250706_140809_1d15449e", "input_id": "input_20250706_140805_5e0899b7", "prompt_id": "prompt_20250706_140805_0a53c0cb", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 60, "analysis": "RSI is above 50, suggesting a neutral trend."}, "MACD": {"value": "0.01", "analysis": "MACD is close to zero, indicating a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 148.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, which suggests a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:09.657557", "processing_time": 4.598662, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 60, "analysis": "RSI is above 50, suggesting a neutral trend."}, "MACD": {"value": "0.01", "analysis": "MACD is close to zero, indicating a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 148.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, which suggests a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:09.657557", "processing_time": 4.598662, "llm_used": true}, "processing_time": 4.598662, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 10.242757000000001}}, {"timestamp": "2025-07-06T14:08:10.001444", "output_id": "output_20250706_140810_8a951a56", "input_id": "input_20250706_140805_9b6fd1ae", "prompt_id": "prompt_20250706_140806_9940acf5", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "crossover": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.001444", "processing_time": 4.766939, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "crossover": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.001444", "processing_time": 4.766939, "llm_used": true}, "processing_time": 4.766939, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 15.009696000000002}}, {"timestamp": "2025-07-06T14:08:10.031603", "output_id": "output_20250706_140810_25d8c7e0", "input_id": "input_20250706_140805_4a2edec7", "prompt_id": "prompt_20250706_140806_b80b9cf3", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.025096", "processing_time": 4.700671, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.025096", "processing_time": 4.700671, "llm_used": true}, "processing_time": 4.700671, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 19.710367}}, {"timestamp": "2025-07-06T14:08:10.040195", "output_id": "output_20250706_140810_58cb121a", "input_id": "input_20250706_140805_1cd40c64", "prompt_id": "prompt_20250706_140806_d046c685", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.040195", "processing_time": 4.716296, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.040195", "processing_time": 4.716296, "llm_used": true}, "processing_time": 4.716296, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 24.426663}}, {"timestamp": "2025-07-06T14:08:10.536213", "output_id": "output_20250706_140810_5564780a", "input_id": "input_20250706_140805_a433b618", "prompt_id": "prompt_20250706_140806_f9b06d29", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.536213", "processing_time": 5.318672, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.536213", "processing_time": 5.318672, "llm_used": true}, "processing_time": 5.318672, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 29.745335}}, {"timestamp": "2025-07-06T14:08:10.811624", "output_id": "output_20250706_140810_e954adaf", "input_id": "input_20250706_140805_d03ad0da", "prompt_id": "prompt_20250706_140806_9bc91a2a", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the asset may be overbought, but the overall trend is bullish."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "The MACD signal line is above the MACD line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.811624", "processing_time": 5.644474, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the asset may be overbought, but the overall trend is bullish."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "The MACD signal line is above the MACD line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:10.811624", "processing_time": 5.644474, "llm_used": true}, "processing_time": 5.644474, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 35.389809}}, {"timestamp": "2025-07-06T14:08:11.220005", "output_id": "output_20250706_140811_24d61926", "input_id": "input_20250706_140805_1c1b7df4", "prompt_id": "prompt_20250706_140805_5c7bbe80", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions."}, "MACD": {"signal_line": 0, "histogram": {"current_value": 0.5, "trend": "positive"}, "analysis": "MACD histogram is positive, suggesting a bullish trend."}, "Moving_Average": {"short_term_MA": 160.0, "long_term_MA": 150.0, "analysis": "The stock is trading above both the short-term and long-term moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:11.220005", "processing_time": 6.092384, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions."}, "MACD": {"signal_line": 0, "histogram": {"current_value": 0.5, "trend": "positive"}, "analysis": "MACD histogram is positive, suggesting a bullish trend."}, "Moving_Average": {"short_term_MA": 160.0, "long_term_MA": 150.0, "analysis": "The stock is trading above both the short-term and long-term moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:11.220005", "processing_time": 6.092384, "llm_used": true}, "processing_time": 6.092384, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 41.482193}}, {"timestamp": "2025-07-06T14:08:11.240179", "output_id": "output_20250706_140811_2a373d1e", "input_id": "input_20250706_140805_8487a794", "prompt_id": "prompt_20250706_140806_a0971af2", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.02, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, supporting the bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:11.240179", "processing_time": 5.918377, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.02, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, supporting the bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:11.240179", "processing_time": 5.918377, "llm_used": true}, "processing_time": 5.918377, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 47.40057}}, {"timestamp": "2025-07-06T14:08:11.385276", "output_id": "output_20250706_140811_32b40a3b", "input_id": "input_20250706_140805_fac9e55f", "prompt_id": "prompt_20250706_140806_fa7a3cd3", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50.2, "interpretation": "Indicates a neutral market condition."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "Both the signal line and histogram are near zero, suggesting no clear trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA, but both are relatively stable, indicating a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:11.385276", "processing_time": 6.061377, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50.2, "interpretation": "Indicates a neutral market condition."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "Both the signal line and histogram are near zero, suggesting no clear trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA, but both are relatively stable, indicating a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:11.385276", "processing_time": 6.061377, "llm_used": true}, "processing_time": 6.061377, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 53.461947}}, {"timestamp": "2025-07-06T14:08:12.135271", "output_id": "output_20250706_140812_bc28a881", "input_id": "input_20250706_140808_ecdd211d", "prompt_id": "prompt_20250706_140808_28141570", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:12.135271", "processing_time": 4.04116, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:12.135271", "processing_time": 4.04116, "llm_used": true}, "processing_time": 4.04116, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 57.503107}}, {"timestamp": "2025-07-06T14:08:12.824648", "output_id": "output_20250706_140812_bc5c5afe", "input_id": "input_20250706_140808_388cd0ed", "prompt_id": "prompt_20250706_140808_7ee33638", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 60, indicating that the stock may be overbought but still in a strong bullish trend."}, "MACD": {"current_value": 0.05, "interpretation": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:12.824648", "processing_time": 4.350726, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 60, indicating that the stock may be overbought but still in a strong bullish trend."}, "MACD": {"current_value": 0.05, "interpretation": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish signal."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:12.824648", "processing_time": 4.350726, "llm_used": true}, "processing_time": 4.350726, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 61.853833}}, {"timestamp": "2025-07-06T14:08:13.041646", "output_id": "output_20250706_140813_80b81bb7", "input_id": "input_20250706_140808_c159f0da", "prompt_id": "prompt_20250706_140809_61166ec8", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69.2, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:13.041646", "processing_time": 4.049482, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69.2, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:13.041646", "processing_time": 4.049482, "llm_used": true}, "processing_time": 4.049482, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 65.903315}}, {"timestamp": "2025-07-06T14:08:13.082533", "output_id": "output_20250706_140813_cdebe0ef", "input_id": "input_20250706_140809_a2febe0b", "prompt_id": "prompt_20250706_140809_ae5402b2", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": {"bullish": true, "cross": "above"}}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:13.082533", "processing_time": 3.992888, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": {"bullish": true, "cross": "above"}}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:13.082533", "processing_time": 3.992888, "llm_used": true}, "processing_time": 3.992888, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 69.896203}}, {"timestamp": "2025-07-06T14:08:13.358196", "output_id": "output_20250706_140813_0481b7de", "input_id": "input_20250706_140809_db8dd30e", "prompt_id": "prompt_20250706_140809_6a2380c0", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "crossover": "bullish"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "bullish_cross": true}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:13.358196", "processing_time": 3.875374, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "crossover": "bullish"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "bullish_cross": true}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:13.358196", "processing_time": 3.875374, "llm_used": true}, "processing_time": 3.875374, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 73.771577}}, {"timestamp": "2025-07-06T14:08:14.962610", "output_id": "output_20250706_140814_befebacf", "input_id": "input_20250706_140809_79a3756a", "prompt_id": "prompt_20250706_140809_725ffd31", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 60, indicating that the stock may be overbought, but the upward trend is strong."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "analysis": "MACD signal line is above the zero line and the histogram is rising, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 120.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:14.962610", "processing_time": 5.258142, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 60, indicating that the stock may be overbought, but the upward trend is strong."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "analysis": "MACD signal line is above the zero line and the histogram is rising, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 120.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:14.962610", "processing_time": 5.258142, "llm_used": true}, "processing_time": 5.258142, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 79.029719}}, {"timestamp": "2025-07-06T14:08:15.228722", "output_id": "output_20250706_140815_24cef2c3", "input_id": "input_20250706_140809_9e7c2ed7", "prompt_id": "prompt_20250706_140809_28ae4eab", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating that the stock might be overbought but still in a strong uptrend."}, "MACD": {"signal_line": 20, "histogram": {"current_value": 0.5, "analysis": "MACD histogram is positive and rising, suggesting a strong bullish trend."}}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:15.228722", "processing_time": 5.600479, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating that the stock might be overbought but still in a strong uptrend."}, "MACD": {"signal_line": 20, "histogram": {"current_value": 0.5, "analysis": "MACD histogram is positive and rising, suggesting a strong bullish trend."}}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:15.228722", "processing_time": 5.600479, "llm_used": true}, "processing_time": 5.600479, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 84.63019800000001}}, {"timestamp": "2025-07-06T14:08:17.389049", "output_id": "output_20250706_140817_50fc906f", "input_id": "input_20250706_140809_b2bb08d2", "prompt_id": "prompt_20250706_140809_46be984b", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above long-term MA, bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:17.380203", "processing_time": 8.36351, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above long-term MA, bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:17.380203", "processing_time": 8.36351, "llm_used": true}, "processing_time": 8.36351, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 92.99370800000001}}, {"timestamp": "2025-07-06T14:08:23.187462", "output_id": "output_20250706_140823_87bc339f", "input_id": "input_20250706_140818_f046689a", "prompt_id": "prompt_20250706_140818_2d5767ac", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 60, indicating that the stock is in a strong uptrend."}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "comment": "MACD signal line is above the zero line and the histogram is positive, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "comment": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:23.187462", "processing_time": 4.8535, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 60, indicating that the stock is in a strong uptrend."}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "comment": "MACD signal line is above the zero line and the histogram is positive, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "comment": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:23.187462", "processing_time": 4.8535, "llm_used": true}, "processing_time": 4.8535, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 97.84720800000001}}, {"timestamp": "2025-07-06T14:08:28.441753", "output_id": "output_20250706_140828_0e39b6f0", "input_id": "input_20250706_140823_6d98d34a", "prompt_id": "prompt_20250706_140823_7bd76526", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:28.399612", "processing_time": 4.511874, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:28.399612", "processing_time": 4.511874, "llm_used": true}, "processing_time": 4.511874, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 102.35908200000002}}, {"timestamp": "2025-07-06T14:08:29.970238", "output_id": "output_20250706_140829_8e356d6b", "input_id": "input_20250706_140825_170226ae", "prompt_id": "prompt_20250706_140825_2d189bab", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "RSI is above 70, indicating that the stock may be overbought, but the upbeat news could justify this."}, "MACD": {"current_value": "positive crossover", "interpretation": "MACD is showing a positive crossover, suggesting upward momentum."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 130.0, "interpretation": "The stock is above both the 50-Day and 200-Day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:29.951706", "processing_time": 4.594958, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "RSI is above 70, indicating that the stock may be overbought, but the upbeat news could justify this."}, "MACD": {"current_value": "positive crossover", "interpretation": "MACD is showing a positive crossover, suggesting upward momentum."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 130.0, "interpretation": "The stock is above both the 50-Day and 200-Day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:29.951706", "processing_time": 4.594958, "llm_used": true}, "processing_time": 4.594958, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 106.95404000000002}}, {"timestamp": "2025-07-06T14:08:33.062774", "output_id": "output_20250706_140833_56b82d5a", "input_id": "input_20250706_140827_15262625", "prompt_id": "prompt_20250706_140827_f164db3f", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:33.005591", "processing_time": 5.172127, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:33.005591", "processing_time": 5.172127, "llm_used": true}, "processing_time": 5.172127, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 112.12616700000002}}, {"timestamp": "2025-07-06T14:08:33.134693", "output_id": "output_20250706_140833_b6fe7f3d", "input_id": "input_20250706_140829_340ecca7", "prompt_id": "prompt_20250706_140829_ba6535aa", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:33.110913", "processing_time": 3.919328, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:33.110913", "processing_time": 3.919328, "llm_used": true}, "processing_time": 3.919328, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 116.04549500000002}}, {"timestamp": "2025-07-06T14:08:33.285737", "output_id": "output_20250706_140833_159b7a02", "input_id": "input_20250706_140828_08afc949", "prompt_id": "prompt_20250706_140828_b196f69c", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0, "histogram": 0.1, "comment": "MACD line is above the signal line, suggesting a bullish momentum."}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 140, "comment": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:33.233643", "processing_time": 4.731884, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0, "histogram": 0.1, "comment": "MACD line is above the signal line, suggesting a bullish momentum."}, "Moving_Average": {"50_day_MA": 160, "200_day_MA": 140, "comment": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:33.233643", "processing_time": 4.731884, "llm_used": true}, "processing_time": 4.731884, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 120.77737900000001}}, {"timestamp": "2025-07-06T14:08:35.650234", "output_id": "output_20250706_140835_5c449246", "input_id": "input_20250706_140831_c1274f4c", "prompt_id": "prompt_20250706_140831_e04ae117", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:35.650234", "processing_time": 4.206354, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:35.650234", "processing_time": 4.206354, "llm_used": true}, "processing_time": 4.206354, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 124.98373300000002}}, {"timestamp": "2025-07-06T14:08:36.397223", "output_id": "output_20250706_140836_20d48e14", "input_id": "input_20250706_140831_e6d6ec55", "prompt_id": "prompt_20250706_140831_85ae6716", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 20, "histogram": 0.5, "interpretation": "positive crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:36.397223", "processing_time": 5.146734, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 20, "histogram": 0.5, "interpretation": "positive crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:36.397223", "processing_time": 5.146734, "llm_used": true}, "processing_time": 5.146734, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 130.130467}}, {"timestamp": "2025-07-06T14:08:37.840005", "output_id": "output_20250706_140837_b96a51f4", "input_id": "input_20250706_140832_4810d790", "prompt_id": "prompt_20250706_140833_2458ed9c", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day": 160.0, "200_day": 180.0, "signal": "price above 50 and 200 day moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:37.826521", "processing_time": 4.883599, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day": 160.0, "200_day": 180.0, "signal": "price above 50 and 200 day moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:37.826521", "processing_time": 4.883599, "llm_used": true}, "processing_time": 4.883599, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 135.014066}}, {"timestamp": "2025-07-06T14:08:38.017072", "output_id": "output_20250706_140838_3938f486", "input_id": "input_20250706_140832_6e384a5b", "prompt_id": "prompt_20250706_140832_82407442", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "above MA50 and MA200"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:38.017072", "processing_time": 5.123621, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "above MA50 and MA200"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:38.017072", "processing_time": 5.123621, "llm_used": true}, "processing_time": 5.123621, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 140.13768700000003}}, {"timestamp": "2025-07-06T14:08:40.078485", "output_id": "output_20250706_140840_ed2d29d1", "input_id": "input_20250706_140835_2306f65d", "prompt_id": "prompt_20250706_140835_4aadeb60", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:40.078485", "processing_time": 4.370178, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:40.078485", "processing_time": 4.370178, "llm_used": true}, "processing_time": 4.370178, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 144.50786500000004}}, {"timestamp": "2025-07-06T14:08:40.272414", "output_id": "output_20250706_140840_8c51cb93", "input_id": "input_20250706_140835_4a97a5d1", "prompt_id": "prompt_20250706_140835_c2a506f8", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "Overbought, indicating a possible pullback or short-term reversal."}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "interpretation": "Positive crossover and histogram above zero, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "Price above both 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:40.272414", "processing_time": 4.33821, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "Overbought, indicating a possible pullback or short-term reversal."}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "interpretation": "Positive crossover and histogram above zero, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "Price above both 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:40.272414", "processing_time": 4.33821, "llm_used": true}, "processing_time": 4.33821, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 148.84607500000004}}, {"timestamp": "2025-07-06T14:08:41.740226", "output_id": "output_20250706_140841_f9467a1c", "input_id": "input_20250706_140837_44c53a62", "prompt_id": "prompt_20250706_140837_41418d33", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:41.711630", "processing_time": 4.164623, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:41.711630", "processing_time": 4.164623, "llm_used": true}, "processing_time": 4.164623, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 153.01069800000005}}, {"timestamp": "2025-07-06T14:08:41.813797", "output_id": "output_20250706_140841_c55144b4", "input_id": "input_20250706_140837_545f5b1c", "prompt_id": "prompt_20250706_140837_7e4f9101", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI接近70，表明股票可能处于超买状态，但短期内可能仍有上升空间。"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "comment": "MACD的信号线和柱状图均显示多头信号，表明市场趋势可能继续上涨。"}, "MA": {"50_day_MA": 160.0, "200_day_MA": 150.0, "comment": "50日移动平均线位于200日移动平均线之上，表明长期趋势向上。"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:41.813797", "processing_time": 4.762145, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI接近70，表明股票可能处于超买状态，但短期内可能仍有上升空间。"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "comment": "MACD的信号线和柱状图均显示多头信号，表明市场趋势可能继续上涨。"}, "MA": {"50_day_MA": 160.0, "200_day_MA": 150.0, "comment": "50日移动平均线位于200日移动平均线之上，表明长期趋势向上。"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:41.813797", "processing_time": 4.762145, "llm_used": true}, "processing_time": 4.762145, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 157.77284300000005}}, {"timestamp": "2025-07-06T14:08:44.096163", "output_id": "output_20250706_140844_75872f78", "input_id": "input_20250706_140836_c245d867", "prompt_id": "prompt_20250706_140836_f8870468", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 53.6, "signal": "neutral"}, "MACD": {"signal_line": 0.2, "histogram": -0.3, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:44.096163", "processing_time": 8.061078, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 53.6, "signal": "neutral"}, "MACD": {"signal_line": 0.2, "histogram": -0.3, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:08:44.096163", "processing_time": 8.061078, "llm_used": true}, "processing_time": 8.061078, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 165.83392100000006}}, {"timestamp": "2025-07-06T14:09:21.174289", "output_id": "output_20250706_140921_a2210ee7", "input_id": "input_20250706_140916_4a3c29ba", "prompt_id": "prompt_20250706_140916_fa458774", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0.1, "analysis": "MACD is positive and above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:09:21.174289", "processing_time": 4.598427, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0.1, "analysis": "MACD is positive and above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:09:21.174289", "processing_time": 4.598427, "llm_used": true}, "processing_time": 4.598427, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 4.598427}}, {"timestamp": "2025-07-06T14:10:01.811911", "output_id": "output_20250706_141001_f6a01cad", "input_id": "input_20250706_140956_9ed7e477", "prompt_id": "prompt_20250706_140957_0b0fd428", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "historical_line": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:01.811911", "processing_time": 5.345959, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "historical_line": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:01.811911", "processing_time": 5.345959, "llm_used": true}, "processing_time": 5.345959, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 9.944386}}, {"timestamp": "2025-07-06T14:10:02.340584", "output_id": "output_20250706_141002_de53e91f", "input_id": "input_20250706_140956_835fee25", "prompt_id": "prompt_20250706_140958_6d788b45", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:02.296579", "processing_time": 5.746941, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:02.296579", "processing_time": 5.746941, "llm_used": true}, "processing_time": 5.746941, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 15.691327}}, {"timestamp": "2025-07-06T14:10:03.235016", "output_id": "output_20250706_141003_7b105031", "input_id": "input_20250706_140957_0366e59f", "prompt_id": "prompt_20250706_140958_8ff46ec6", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:03.235016", "processing_time": 6.587497, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:03.235016", "processing_time": 6.587497, "llm_used": true}, "processing_time": 6.587497, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 22.278824}}, {"timestamp": "2025-07-06T14:10:03.426267", "output_id": "output_20250706_141003_ad76c283", "input_id": "input_20250706_140957_d2685554", "prompt_id": "prompt_20250706_140958_941c06c9", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.7, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 70, "comment": "RSI is in overbought territory, indicating potential for a pullback."}, "MACD": {"signal_line": 0.02, "comment": "MACD signal line is above the zero line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 140, "200_day_MA": 130, "comment": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:03.426267", "processing_time": 6.663319, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.7, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 70, "comment": "RSI is in overbought territory, indicating potential for a pullback."}, "MACD": {"signal_line": 0.02, "comment": "MACD signal line is above the zero line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 140, "200_day_MA": 130, "comment": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:03.426267", "processing_time": 6.663319, "llm_used": true}, "processing_time": 6.663319, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 28.942143}}, {"timestamp": "2025-07-06T14:10:04.219779", "output_id": "output_20250706_141004_71d8b18a", "input_id": "input_20250706_140957_4b53fd1f", "prompt_id": "prompt_20250706_140958_d496718e", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock is overbought but still in a strong bullish trend."}, "MACD": {"signal_line": 0.05, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:04.219779", "processing_time": 7.476308, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock is overbought but still in a strong bullish trend."}, "MACD": {"signal_line": 0.05, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:04.219779", "processing_time": 7.476308, "llm_used": true}, "processing_time": 7.476308, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 36.418451000000005}}, {"timestamp": "2025-07-06T14:10:04.806925", "output_id": "output_20250706_141004_017a2104", "input_id": "input_20250706_140959_501219dd", "prompt_id": "prompt_20250706_140959_8449926d", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions. However, the upward trend suggests that the overbought state is temporary."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "analysis": "The MACD line is above the signal line, and the histogram is positive, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:04.770758", "processing_time": 5.421368, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating overbought conditions. However, the upward trend suggests that the overbought state is temporary."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "analysis": "The MACD line is above the signal line, and the histogram is positive, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:04.770758", "processing_time": 5.421368, "llm_used": true}, "processing_time": 5.421368, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 41.839819000000006}}, {"timestamp": "2025-07-06T14:10:04.927700", "output_id": "output_20250706_141004_88964087", "input_id": "input_20250706_140957_fd2e3e20", "prompt_id": "prompt_20250706_140958_90d486ba", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:04.927700", "processing_time": 8.114565, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:04.927700", "processing_time": 8.114565, "llm_used": true}, "processing_time": 8.114565, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 49.954384000000005}}, {"timestamp": "2025-07-06T14:10:05.233594", "output_id": "output_20250706_141005_032cea1b", "input_id": "input_20250706_141001_b1fbbdbe", "prompt_id": "prompt_20250706_141001_d8578859", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day": 155.0, "200_day": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.233594", "processing_time": 3.633697, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day": 155.0, "200_day": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.233594", "processing_time": 3.633697, "llm_used": true}, "processing_time": 3.633697, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 53.588081}}, {"timestamp": "2025-07-06T14:10:05.628365", "output_id": "output_20250706_141005_76554bd4", "input_id": "input_20250706_140957_5675278e", "prompt_id": "prompt_20250706_140958_a4fa2c3b", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 70.5, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.3, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "price above long-term MA, but close to short-term MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.628365", "processing_time": 8.877897, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 70.5, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.3, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "price above long-term MA, but close to short-term MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.628365", "processing_time": 8.877897, "llm_used": true}, "processing_time": 8.877897, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 62.46597800000001}}, {"timestamp": "2025-07-06T14:10:05.713682", "output_id": "output_20250706_141005_cbfbf274", "input_id": "input_20250706_140957_9c7084a2", "prompt_id": "prompt_20250706_140958_306183f5", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI is above 50 indicating a strong uptrend."}, "MACD": {"signal_line": 100, "histogram": 0.5, "comment": "MACD line above the signal line with a rising histogram indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "comment": "Stock price above both the 50-day and 200-day moving averages, suggesting a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.669194", "processing_time": 9.072194, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI is above 50 indicating a strong uptrend."}, "MACD": {"signal_line": 100, "histogram": 0.5, "comment": "MACD line above the signal line with a rising histogram indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "comment": "Stock price above both the 50-day and 200-day moving averages, suggesting a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.669194", "processing_time": 9.072194, "llm_used": true}, "processing_time": 9.072194, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 71.538172}}, {"timestamp": "2025-07-06T14:10:05.831647", "output_id": "output_20250706_141005_f13579e1", "input_id": "input_20250706_141001_36c9922f", "prompt_id": "prompt_20250706_141001_fb6d9960", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "MACD is above the signal line with a positive histogram, suggesting upward momentum."}, "Moving_Averages": {"50-Day_MA": 155, "200-Day_MA": 145, "analysis": "The stock is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.831647", "processing_time": 4.596848, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "MACD is above the signal line with a positive histogram, suggesting upward momentum."}, "Moving_Averages": {"50-Day_MA": 155, "200-Day_MA": 145, "analysis": "The stock is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:05.831647", "processing_time": 4.596848, "llm_used": true}, "processing_time": 4.596848, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 76.13502}}, {"timestamp": "2025-07-06T14:10:06.746388", "output_id": "output_20250706_141006_45e1c6a2", "input_id": "input_20250706_141002_59c722db", "prompt_id": "prompt_20250706_141002_f1922c4e", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 10, "histogram": 0.5, "analysis": "MACD signal line is above the zero line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 145, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:06.746388", "processing_time": 4.59609, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 10, "histogram": 0.5, "analysis": "MACD signal line is above the zero line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 145, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:06.746388", "processing_time": 4.59609, "llm_used": true}, "processing_time": 4.59609, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 80.73111}}, {"timestamp": "2025-07-06T14:10:06.816267", "output_id": "output_20250706_141006_5f12d638", "input_id": "input_20250706_141001_19676062", "prompt_id": "prompt_20250706_141001_5864fbd0", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:06.815268", "processing_time": 5.434962, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:06.815268", "processing_time": 5.434962, "llm_used": true}, "processing_time": 5.434962, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 86.166072}}, {"timestamp": "2025-07-06T14:10:07.330324", "output_id": "output_20250706_141007_2d36512a", "input_id": "input_20250706_141002_52d758bc", "prompt_id": "prompt_20250706_141002_6b469377", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:07.217544", "processing_time": 4.876409, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:07.217544", "processing_time": 4.876409, "llm_used": true}, "processing_time": 4.876409, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 91.042481}}, {"timestamp": "2025-07-06T14:10:07.336852", "output_id": "output_20250706_141007_d3207606", "input_id": "input_20250706_141002_16a9bc2b", "prompt_id": "prompt_20250706_141002_0e0440ce", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68.5, "comment": "RSI is above 70, indicating a strong uptrend."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "comment": "MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "comment": "The 50-day moving average is above the 200-day moving average, supporting a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:07.258233", "processing_time": 5.19206, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68.5, "comment": "RSI is above 70, indicating a strong uptrend."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "comment": "MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "comment": "The 50-day moving average is above the 200-day moving average, supporting a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:07.258233", "processing_time": 5.19206, "llm_used": true}, "processing_time": 5.19206, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 96.234541}}, {"timestamp": "2025-07-06T14:10:08.430558", "output_id": "output_20250706_141008_8180a48e", "input_id": "input_20250706_140957_c56e1557", "prompt_id": "prompt_20250706_140958_f8a183e3", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52.3, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:08.429550", "processing_time": 11.613408, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52.3, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:08.429550", "processing_time": 11.613408, "llm_used": true}, "processing_time": 11.613408, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 107.847949}}, {"timestamp": "2025-07-06T14:10:09.213453", "output_id": "output_20250706_141009_fe898a87", "input_id": "input_20250706_141001_e1d5324e", "prompt_id": "prompt_20250706_141001_ea2b878d", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": {"current_value": 0.03, "trend": "positive"}}, "Moving_Average": {"50_day_MA": 135.0, "200_day_MA": 120.0, "trend": "upward"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:09.213453", "processing_time": 7.851076, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": {"current_value": 0.03, "trend": "positive"}}, "Moving_Average": {"50_day_MA": 135.0, "200_day_MA": 120.0, "trend": "upward"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:09.213453", "processing_time": 7.851076, "llm_used": true}, "processing_time": 7.851076, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 115.699025}}, {"timestamp": "2025-07-06T14:10:13.446427", "output_id": "output_20250706_141013_c5204ccc", "input_id": "input_20250706_141009_f6b6ce30", "prompt_id": "prompt_20250706_141009_f9901bae", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:13.366924", "processing_time": 4.060302, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:13.366924", "processing_time": 4.060302, "llm_used": true}, "processing_time": 4.060302, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 119.75932700000001}}, {"timestamp": "2025-07-06T14:10:22.009218", "output_id": "output_20250706_141022_5feae71b", "input_id": "input_20250706_141018_92bfc317", "prompt_id": "prompt_20250706_141018_053f4fa4", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69.2, "overbought": true, "signal": "buy"}, "MACD": {"signal_line": 0.0, "histogram": 0.3, "signal": "buy"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "buy"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:22.004235", "processing_time": 3.704471, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69.2, "overbought": true, "signal": "buy"}, "MACD": {"signal_line": 0.0, "histogram": 0.3, "signal": "buy"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "buy"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:22.004235", "processing_time": 3.704471, "llm_used": true}, "processing_time": 3.704471, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 123.46379800000001}}, {"timestamp": "2025-07-06T14:10:22.786515", "output_id": "output_20250706_141022_0e327ec5", "input_id": "input_20250706_141018_1c7266a5", "prompt_id": "prompt_20250706_141018_8df95c86", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "RSI is in the middle of the range, suggesting no immediate overbought or oversold conditions."}, "MACD": {"current_value": "0.00", "analysis": "MACD line is close to the signal line, indicating a lack of momentum in either direction."}, "Moving_Average": {"short_term_MA": 155.0, "long_term_MA": 150.0, "analysis": "The stock is currently trading above the short-term moving average but below the long-term moving average, suggesting a slight bullish trend in the short term."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:22.786515", "processing_time": 4.737297, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "RSI is in the middle of the range, suggesting no immediate overbought or oversold conditions."}, "MACD": {"current_value": "0.00", "analysis": "MACD line is close to the signal line, indicating a lack of momentum in either direction."}, "Moving_Average": {"short_term_MA": 155.0, "long_term_MA": 150.0, "analysis": "The stock is currently trading above the short-term moving average but below the long-term moving average, suggesting a slight bullish trend in the short term."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:22.786515", "processing_time": 4.737297, "llm_used": true}, "processing_time": 4.737297, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 128.201095}}, {"timestamp": "2025-07-06T14:10:23.912796", "output_id": "output_20250706_141023_68a59545", "input_id": "input_20250706_141019_f4471fe4", "prompt_id": "prompt_20250706_141019_f0019687", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 140, "200_day_MA": 130, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:23.912796", "processing_time": 4.587495, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 140, "200_day_MA": 130, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:23.912796", "processing_time": 4.587495, "llm_used": true}, "processing_time": 4.587495, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 132.78859}}, {"timestamp": "2025-07-06T14:10:27.695772", "output_id": "output_20250706_141027_e393ad45", "input_id": "input_20250706_141021_f15492d8", "prompt_id": "prompt_20250706_141021_1b25311f", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 70, indicating that the stock may be overbought, but the trend is still bullish."}, "MACD": {"current_value": "positive", "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:27.695772", "processing_time": 6.548071, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 70, indicating that the stock may be overbought, but the trend is still bullish."}, "MACD": {"current_value": "positive", "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:27.695772", "processing_time": 6.548071, "llm_used": true}, "processing_time": 6.548071, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 139.336661}}, {"timestamp": "2025-07-06T14:10:29.378697", "output_id": "output_20250706_141029_782dc78e", "input_id": "input_20250706_141024_a9df1325", "prompt_id": "prompt_20250706_141024_a194e9f4", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "Buy signal"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Short-term trend above long-term trend, indicating bullishness"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:29.378697", "processing_time": 5.154446, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "Buy signal"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Short-term trend above long-term trend, indicating bullishness"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:29.378697", "processing_time": 5.154446, "llm_used": true}, "processing_time": 5.154446, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 144.491107}}, {"timestamp": "2025-07-06T14:10:29.765589", "output_id": "output_20250706_141029_0b36f9d5", "input_id": "input_20250706_141023_d0c39578", "prompt_id": "prompt_20250706_141023_98a66153", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Average": {"50-Day_MA": 150.0, "200-Day_MA": 130.0, "signal": "crossover above 50-Day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:29.765589", "processing_time": 6.702852, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Average": {"50-Day_MA": 150.0, "200-Day_MA": 130.0, "signal": "crossover above 50-Day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:29.765589", "processing_time": 6.702852, "llm_used": true}, "processing_time": 6.702852, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 151.193959}}, {"timestamp": "2025-07-06T14:10:30.966018", "output_id": "output_20250706_141030_1046a242", "input_id": "input_20250706_141024_62c8edb1", "prompt_id": "prompt_20250706_141024_8e6acedd", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:30.832683", "processing_time": 6.740898, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:30.832683", "processing_time": 6.740898, "llm_used": true}, "processing_time": 6.740898, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 157.934857}}, {"timestamp": "2025-07-06T14:10:31.871895", "output_id": "output_20250706_141031_678cd651", "input_id": "input_20250706_141027_4a378eca", "prompt_id": "prompt_20250706_141027_9a2ac0ee", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 54, "trend": "neutral"}, "MACD": {"signal_line": 0.001, "histogram": -0.002, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:31.871895", "processing_time": 4.622116, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 54, "trend": "neutral"}, "MACD": {"signal_line": 0.001, "histogram": -0.002, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:31.871895", "processing_time": 4.622116, "llm_used": true}, "processing_time": 4.622116, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 162.556973}}, {"timestamp": "2025-07-06T14:10:33.179188", "output_id": "output_20250706_141033_d46c15d2", "input_id": "input_20250706_141029_c0867dda", "prompt_id": "prompt_20250706_141029_7b399b7c", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is overbought, indicating potential for a pullback."}, "MACD": {"signal_line": 0, "analysis": "MACD is above the signal line, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:33.179188", "processing_time": 4.061227, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is overbought, indicating potential for a pullback."}, "MACD": {"signal_line": 0, "analysis": "MACD is above the signal line, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:33.179188", "processing_time": 4.061227, "llm_used": true}, "processing_time": 4.061227, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 166.6182}}, {"timestamp": "2025-07-06T14:10:35.100694", "output_id": "output_20250706_141035_b473f046", "input_id": "input_20250706_141030_bb82033b", "prompt_id": "prompt_20250706_141030_c768b64c", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.033993", "processing_time": 4.881211, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.033993", "processing_time": 4.881211, "llm_used": true}, "processing_time": 4.881211, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 171.499411}}, {"timestamp": "2025-07-06T14:10:35.439895", "output_id": "output_20250706_141035_8cac1004", "input_id": "input_20250706_141030_53d485e3", "prompt_id": "prompt_20250706_141030_6a0a05b2", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 70.5, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.439895", "processing_time": 4.692803, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 70.5, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.439895", "processing_time": 4.692803, "llm_used": true}, "processing_time": 4.692803, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 176.192214}}, {"timestamp": "2025-07-06T14:10:35.545415", "output_id": "output_20250706_141035_d47469ff", "input_id": "input_20250706_141030_8e34d20b", "prompt_id": "prompt_20250706_141030_3a563ea9", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.05, "analysis": "MACD is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.545415", "processing_time": 5.161815, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.05, "analysis": "MACD is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.545415", "processing_time": 5.161815, "llm_used": true}, "processing_time": 5.161815, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 181.354029}}, {"timestamp": "2025-07-06T14:10:35.616774", "output_id": "output_20250706_141035_ff56f1f8", "input_id": "input_20250706_141029_e22dd694", "prompt_id": "prompt_20250706_141029_26b37c0b", "raw_response": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 72, "analysis": "RSI is above 70, indicating the stock is overbought, but the upbeat news might push it higher."}, "MACD": {"signal_line": 15, "histogram": 5, "analysis": "MACD is showing a bullish crossover, suggesting potential upward momentum."}, "Moving_Averages": {"50-Day_MA": 155, "200-Day_MA": 145, "analysis": "The stock is currently above both the 50-Day and 200-Day moving averages, supporting a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.585941", "processing_time": 6.378365, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 72, "analysis": "RSI is above 70, indicating the stock is overbought, but the upbeat news might push it higher."}, "MACD": {"signal_line": 15, "histogram": 5, "analysis": "MACD is showing a bullish crossover, suggesting potential upward momentum."}, "Moving_Averages": {"50-Day_MA": 155, "200-Day_MA": 145, "analysis": "The stock is currently above both the 50-Day and 200-Day moving averages, supporting a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.585941", "processing_time": 6.378365, "llm_used": true}, "processing_time": 6.378365, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 187.732394}}, {"timestamp": "2025-07-06T14:10:35.761509", "output_id": "output_20250706_141035_615ecaed", "input_id": "input_20250706_141031_21c6fb3d", "prompt_id": "prompt_20250706_141031_fc915115", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "50-day MA crossing above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.761509", "processing_time": 4.461422, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "50-day MA crossing above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-06T14:10:35.761509", "processing_time": 4.461422, "llm_used": true}, "processing_time": 4.461422, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 192.193816}}]