#!/usr/bin/env python3
"""
测试增强Shapley系统的手动触发脚本
用于验证系统是否正常工作
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_shapley_trigger.log')
        ]
    )
    return logging.getLogger(__name__)

def test_shapley_trigger():
    """测试Shapley触发器"""
    logger = setup_logging()
    logger.info("🚀 开始测试增强Shapley系统")
    
    try:
        # 导入增强的Shapley组件
        from contribution_assessment.weekly_shapley_trigger import WeeklyShapleyTrigger
        from contribution_assessment.enhanced_shapley_storage_manager import EnhancedShapleyStorageManager
        from contribution_assessment.coalition_experiment_tracker import CoalitionExperimentTracker
        
        logger.info("✅ 成功导入增强Shapley组件")
        
        # 配置
        shapley_config = {
            "data_dir": "data/trading",
            "shapley_analysis_enabled": True,
            "weekly_trigger_enabled": True,
            "trading_days_per_week": 5,
            "min_data_completeness_ratio": 0.8,
            "coalition_experiment_timeout": 300,
            "max_concurrent_experiments": 4,
            "storage_config": {
                "base_data_dir": "data/trading",
                "backup_enabled": True,
                "compression_enabled": True
            }
        }
        
        # 创建触发器
        shapley_trigger = WeeklyShapleyTrigger(
            config=shapley_config,
            logger=logger
        )
        
        logger.info("✅ 成功创建Shapley触发器")
        
        # 测试数据完整性检查
        test_date = datetime(2025, 1, 15)  # 使用测试期间的最后一天
        logger.info(f"🔍 检查数据完整性，测试日期: {test_date.date()}")
        
        data_complete = shapley_trigger._check_data_completeness(test_date)
        logger.info(f"📊 数据完整性检查结果: {data_complete}")
        
        # 测试交易周完成检查
        week_complete = shapley_trigger._check_trading_week_completion(test_date)
        logger.info(f"📅 交易周完成检查结果: {week_complete}")
        
        # 测试触发条件
        trigger_conditions = shapley_trigger.check_weekly_trigger_conditions(test_date)
        logger.info(f"🎯 触发条件检查结果: {trigger_conditions}")
        
        # 如果条件不满足，尝试强制触发
        if not trigger_conditions:
            logger.info("⚠️  触发条件不满足，尝试强制触发Shapley分析")
            
            # 强制触发分析
            result = shapley_trigger.trigger_shapley_analysis(
                week_number=1,  # 第1周
                force_trigger=True
            )
            
            logger.info(f"🔬 强制触发结果: {result}")
            
            if result.get("success", False):
                logger.info("🎉 Shapley分析成功完成！")
                
                # 检查生成的数据
                shapley_dir = Path("data/trading/shapley_analysis")
                if shapley_dir.exists():
                    logger.info(f"📁 Shapley分析目录已创建: {shapley_dir}")
                    
                    # 列出生成的文件
                    for item in shapley_dir.rglob("*"):
                        if item.is_file():
                            logger.info(f"📄 生成文件: {item}")
                else:
                    logger.warning("⚠️  Shapley分析目录未找到")
            else:
                logger.error(f"❌ Shapley分析失败: {result}")
        else:
            logger.info("✅ 触发条件满足，执行正常触发")
            result = shapley_trigger.trigger_shapley_analysis(week_number=1)
            logger.info(f"🔬 正常触发结果: {result}")
        
    except ImportError as e:
        logger.error(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    logger.info("🏁 测试完成")
    return True

if __name__ == "__main__":
    success = test_shapley_trigger()
    if success:
        print("✅ 测试成功完成")
    else:
        print("❌ 测试失败")
        sys.exit(1)
