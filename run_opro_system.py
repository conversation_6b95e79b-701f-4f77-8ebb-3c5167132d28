#!/usr/bin/env python3
"""
OPRO系统运行脚本 (OPRO System Runner)

用于运行集成了OPRO优化功能的多智能体交易系统。

使用示例:
    # 默认运行（OPRO优化模式）
    python run_opro_system.py --provider zhipuai

    # 运行基础评估（不含OPRO）
    python run_opro_system.py --provider zhipuai --mode evaluation --disable-opro

    # 运行OPRO优化循环（默认模式）
    python run_opro_system.py --provider zhipuai --mode optimization

    # 运行完整集成（评估+优化）
    python run_opro_system.py --provider zhipuai --mode integrated

    # 获取OPRO仪表板数据
    python run_opro_system.py --provider zhipuai --mode dashboard
"""

import argparse
import json
import logging
import os
import sys
import time
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

# 设置环境变量以支持UTF-8输出（Windows系统）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    # 尝试设置控制台代码页为UTF-8
    try:
        import subprocess
        subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
    except Exception:
        pass

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块
from contribution_assessment.assessor import ContributionAssessor
from contribution_assessment.llm_interface import LLMInterface

# 导入数据存储模块
from data.integrated_data_manager import IntegratedDataManager

# 导入代理交互日志记录器
try:
    from data.agent_interaction_logger import AgentInteractionLogger
except ImportError:
    AgentInteractionLogger = None

def safe_log_message(message: str) -> str:
    """
    安全处理日志消息，替换可能导致编码错误的Unicode字符
    """
    if not isinstance(message, str):
        message = str(message)

    # 替换常见的Unicode字符
    replacements = {
        '✅': '[SUCCESS]',
        '❌': '[ERROR]',
        '🎉': '[CELEBRATION]',
        '⚠️': '[WARNING]',
        '📊': '[CHART]',
        '💡': '[IDEA]',
        '🔧': '[TOOL]',
        '📈': '[TRENDING_UP]',
        '📉': '[TRENDING_DOWN]'
    }

    for unicode_char, replacement in replacements.items():
        message = message.replace(unicode_char, replacement)

    return message

def setup_logging(verbose: bool = False, log_file: Optional[str] = None) -> logging.Logger:
    """设置日志记录"""
    log_level = logging.DEBUG if verbose else logging.INFO

    # 创建自定义格式化器，处理Unicode字符
    class SafeFormatter(logging.Formatter):
        def format(self, record):
            try:
                # 尝试正常格式化
                formatted = super().format(record)
                return formatted
            except UnicodeEncodeError:
                # 如果遇到编码错误，替换特殊字符
                if hasattr(record, 'msg'):
                    # 替换常见的Unicode字符
                    msg = str(record.msg)
                    msg = msg.replace('✅', '[SUCCESS]')
                    msg = msg.replace('❌', '[ERROR]')
                    msg = msg.replace('🎉', '[CELEBRATION]')
                    record.msg = msg

                if record.args:
                    safe_args = []
                    for arg in record.args:
                        if isinstance(arg, str):
                            safe_arg = arg.replace('✅', '[SUCCESS]')
                            safe_arg = safe_arg.replace('❌', '[ERROR]')
                            safe_arg = safe_arg.replace('🎉', '[CELEBRATION]')
                            safe_args.append(safe_arg)
                        else:
                            safe_args.append(arg)
                    record.args = tuple(safe_args)

                return super().format(record)

    # 清除现有的处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(SafeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

    handlers: List[logging.Handler] = [console_handler]

    if log_file:
        # 文件处理器使用UTF-8编码
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        handlers.append(file_handler)

    logging.basicConfig(
        level=log_level,
        handlers=handlers,
        force=True  # 强制重新配置
    )

    return logging.getLogger(__name__)

def load_config(config_path: str = "config/opro_config.json") -> Dict[str, Any]:
    """加载配置文件"""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            print(f"配置文件不存在: {config_path}")
            return {}
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {}

def create_system_config(args) -> Dict[str, Any]:
    """创建系统配置"""
    return {
        "start_date": args.start_date,
        "end_date": args.end_date,
        "stocks": ["AAPL"],  # 可以通过参数扩展
        "starting_cash": 1000000,
        "simulation_days": args.simulation_days,
        "verbose": args.verbose,
        "enable_concurrent_execution": not args.disable_concurrent,
        "fail_on_large_gaps": False,
        "fill_date_gaps": True
    }

def run_evaluation_mode(assessor: ContributionAssessor, args, logger: logging.Logger) -> Dict[str, Any]:
    """运行评估模式"""
    logger.info("=" * 80)
    logger.info("运行模式: 标准评估")
    logger.info("=" * 80)
    
    try:
        if args.quick_test:
            logger.info("运行快速测试...")
            result = assessor.run_quick_test()
        else:
            logger.info("运行完整评估...")
            result = assessor.run(
                target_agents=args.agents.split(',') if args.agents else None,
                max_coalitions=args.max_coalitions
            )
        
        return {
            "mode": "evaluation", 
            "result": result,
            "success": result.get("success", False)
        }
        
    except Exception as e:
        logger.error(f"评估模式执行失败: {e}")
        return {
            "mode": "evaluation",
            "success": False,
            "error": str(e)
        }

def run_optimization_mode(assessor: ContributionAssessor, args, logger: logging.Logger) -> Dict[str, Any]:
    """运行优化模式"""
    logger.info("=" * 80)
    logger.info("运行模式: OPRO优化")
    logger.info("=" * 80)
    
    if not assessor.enable_opro:
        logger.error("OPRO功能未启用，无法运行优化模式")
        return {
            "mode": "optimization",
            "success": False,
            "error": "OPRO功能未启用"
        }
    
    try:
        target_agents = args.agents.split(',') if args.agents else None
        
        logger.info(f"开始OPRO优化循环...")
        result = assessor.run_opro_optimization_cycle(
            target_agents=target_agents,
            force_optimization=args.force_optimization
        )
        
        return {
            "mode": "optimization",
            "result": result,
            "success": result.get("success", False)
        }
        
    except Exception as e:
        logger.error(f"优化模式执行失败: {e}")
        return {
            "mode": "optimization",
            "success": False,
            "error": str(e)
        }

def run_integrated_mode(assessor: ContributionAssessor, args, logger: logging.Logger) -> Dict[str, Any]:
    """运行集成模式（评估+优化）"""
    logger.info("=" * 80)
    logger.info("运行模式: 集成模式（评估+优化）")
    logger.info("=" * 80)

    if not assessor.enable_opro:
        logger.error("OPRO功能未启用，无法运行集成模式")
        return {
            "mode": "integrated",
            "success": False,
            "error": "OPRO功能未启用"
        }

    try:
        target_agents = args.agents.split(',') if args.agents else None

        # 新增：运行完整的日期范围交易系统
        logger.info(f"开始运行完整日期范围的交易系统: {args.start_date} 到 {args.end_date}")

        result = run_full_date_range_trading_system(
            assessor=assessor,
            start_date=args.start_date,
            end_date=args.end_date,
            target_agents=target_agents,
            max_coalitions=args.max_coalitions,
            run_optimization_before=args.optimize_before,
            run_optimization_after=args.optimize_after,
            logger=logger
        )

        return {
            "mode": "integrated",
            "result": result,
            "success": result.get("success", False)
        }

    except Exception as e:
        logger.error(f"集成模式执行失败: {e}")
        return {
            "mode": "integrated",
            "success": False,
            "error": str(e)
        }

def run_full_date_range_trading_system(assessor: ContributionAssessor,
                                      start_date: str,
                                      end_date: str,
                                      target_agents: Optional[List[str]] = None,
                                      max_coalitions: Optional[int] = None,
                                      run_optimization_before: bool = False,
                                      run_optimization_after: bool = True,
                                      logger: Optional[logging.Logger] = None) -> Dict[str, Any]:
    """
    运行完整日期范围的交易系统，实现每日交易决策循环和周期性OPRO优化

    参数:
        assessor: 贡献度评估器
        start_date: 开始日期
        end_date: 结束日期
        target_agents: 目标智能体列表
        max_coalitions: 最大联盟数量
        run_optimization_before: 评估前是否运行优化
        run_optimization_after: 评估后是否运行优化
        logger: 日志记录器

    返回:
        完整的交易系统执行结果
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    logger.info("=" * 100)
    logger.info("🚀 启动完整日期范围交易系统")
    logger.info("=" * 100)
    logger.info(f"📅 交易期间: {start_date} 到 {end_date}")
    logger.info(f"🤖 目标智能体: {target_agents or '所有默认智能体'}")
    logger.info(f"🔄 OPRO优化: {'启用' if assessor.enable_opro else '禁用'}")

    start_time = time.time()
    result = {
        "success": True,
        "start_date": start_date,
        "end_date": end_date,
        "opro_enabled": assessor.enable_opro,
        "daily_results": {},
        "weekly_results": {},
        "optimization_results": {},
        "total_execution_time": 0.0,
        "trading_statistics": {}
    }

    try:
        # 步骤1：生成完整的交易日期列表
        logger.info("步骤1: 生成交易日期列表...")
        trading_dates = generate_trading_date_range(start_date, end_date, logger)

        if not trading_dates:
            raise ValueError(f"无法生成有效的交易日期范围: {start_date} 到 {end_date}")

        logger.info(f"✅ 生成了 {len(trading_dates)} 个交易日")
        result["total_trading_days"] = len(trading_dates)

        # 步骤2：评估前优化（可选）
        if run_optimization_before and assessor.enable_opro:
            logger.info("步骤2: 评估前OPRO优化...")

            # 确保有足够的历史数据进行优化
            ensure_opro_historical_data(assessor, target_agents or assessor.default_agents, logger)

            pre_optimization = assessor.run_opro_optimization_cycle(target_agents)
            result["optimization_results"]["pre_evaluation"] = pre_optimization

        # 步骤3：运行每日交易决策循环
        logger.info("步骤3: 开始每日交易决策循环...")
        daily_results = run_daily_trading_loop(
            assessor=assessor,
            trading_dates=trading_dates,
            target_agents=target_agents,
            max_coalitions=max_coalitions,
            logger=logger
        )
        result["daily_results"] = daily_results

        # 步骤4：周期性OPRO优化
        if assessor.enable_opro:
            logger.info("步骤4: 执行周期性OPRO优化...")
            weekly_optimization_results = run_weekly_opro_optimization(
                assessor=assessor,
                daily_results=daily_results,
                target_agents=target_agents,
                logger=logger
            )
            result["optimization_results"]["weekly"] = weekly_optimization_results

        # 步骤5：评估后优化（可选）
        if run_optimization_after and assessor.enable_opro:
            logger.info("步骤5: 评估后OPRO优化...")
            post_optimization = assessor.run_opro_optimization_cycle(target_agents)
            result["optimization_results"]["post_evaluation"] = post_optimization

        # 步骤6：计算交易统计
        logger.info("步骤6: 计算交易统计...")
        trading_stats = calculate_trading_statistics(daily_results, logger)
        result["trading_statistics"] = trading_stats

        result["total_execution_time"] = time.time() - start_time

        logger.info("=" * 100)
        logger.info("🎉 完整日期范围交易系统执行成功!")
        logger.info(f"📊 总交易天数: {len(trading_dates)}")
        logger.info(f"⏱️  总执行时间: {result['total_execution_time']:.2f}秒")
        logger.info("=" * 100)

        return result

    except Exception as e:
        logger.error(f"❌ 完整日期范围交易系统执行失败: {e}")
        result["success"] = False
        result["error"] = str(e)
        result["total_execution_time"] = time.time() - start_time
        return result

def run_dashboard_mode(assessor: ContributionAssessor, args, logger: logging.Logger) -> Dict[str, Any]:
    """运行仪表板模式"""
    logger.info("=" * 80)
    logger.info("运行模式: OPRO仪表板")
    logger.info("=" * 80)
    
    if not assessor.enable_opro:
        logger.error("OPRO功能未启用，无法运行仪表板模式")
        return {
            "mode": "dashboard",
            "success": False,
            "error": "OPRO功能未启用"
        }
    
    try:
        dashboard_data = assessor.get_opro_dashboard_data()
        
        # 输出仪表板数据
        logger.info("OPRO系统状态:")
        
        # 系统统计
        system_stats = dashboard_data.get("system_stats", {})
        if "optimizer" in system_stats:
            opt_stats = system_stats["optimizer"]
            logger.info(f"  优化器统计:")
            logger.info(f"    总优化次数: {opt_stats.get('total_optimizations', 0)}")
            logger.info(f"    成功优化次数: {opt_stats.get('successful_optimizations', 0)}")
            logger.info(f"    成功率: {opt_stats.get('success_rate', 0):.1f}%")
        
        # 智能体性能
        agent_performance = dashboard_data.get("agent_performance", {})
        if "ranking" in agent_performance:
            logger.info(f"  智能体性能排名:")
            for i, agent_id in enumerate(agent_performance["ranking"][:5]):
                stats = agent_performance["agent_stats"].get(agent_id, {})
                avg_score = stats.get("average_score", 0)
                logger.info(f"    {i+1}. {agent_id}: {avg_score:.6f}")
        
        # 优化建议
        recommendations = dashboard_data.get("recommendations", [])
        if recommendations:
            logger.info(f"  优化建议:")
            for rec in recommendations[:3]:
                priority = rec.get("priority", "medium")
                message = rec.get("message", "")
                logger.info(f"    [{priority.upper()}] {message}")
        
        return {
            "mode": "dashboard",
            "result": dashboard_data,
            "success": dashboard_data.get("opro_enabled", False)
        }
        
    except Exception as e:
        logger.error(f"仪表板模式执行失败: {e}")
        return {
            "mode": "dashboard",
            "success": False,
            "error": str(e)
        }

def export_results(result: Dict[str, Any], output_path: str, logger: logging.Logger):
    """导出结果到文件"""
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        logger.info(f"结果已导出至: {output_path}")
        
    except Exception as e:
        logger.error(f"导出结果失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OPRO系统运行脚本")
    
    # 基础参数
    parser.add_argument("--provider", type=str, default="zhipuai", 
                       choices=["zhipuai", "openai"], help="LLM提供商")
    parser.add_argument("--mode", type=str, default="integrated",
                       choices=["evaluation", "optimization", "integrated", "dashboard"],
                       help="运行模式")
    parser.add_argument("--enable-opro", action="store_true", default=True, help="启用OPRO功能")
    parser.add_argument("--disable-opro", action="store_true", help="禁用OPRO功能")
    parser.add_argument("--enhanced-mode", action="store_true", default=True,
                       help="启用增强OPRP模式（14天双轨实验，默认启用）")
    parser.add_argument("--traditional-mode", action="store_true",
                       help="强制使用传统模式（禁用增强功能）")
    parser.add_argument("--verbose", action="store_true", help="详细日志")
    
    # 系统配置
    parser.add_argument("--start-date", type=str, default="2025-01-01", help="开始日期")
    parser.add_argument("--end-date", type=str, default="2025-04-01", help="结束日期")
    parser.add_argument("--simulation-days", type=int, help="模拟天数")
    parser.add_argument("--agents", type=str, help="目标智能体列表（逗号分隔）")
    parser.add_argument("--max-coalitions", type=int, help="最大联盟数量")
    parser.add_argument("--disable-concurrent", action="store_true", help="禁用并发执行")
    
    # 测试选项
    parser.add_argument("--quick-test", action="store_true", help="运行快速测试")
    
    # 优化选项
    parser.add_argument("--force-optimization", action="store_true", help="强制优化")
    parser.add_argument("--optimize-before", action="store_true", help="评估前优化")
    parser.add_argument("--optimize-after", action="store_true", default=True, help="评估后优化")
    
    # 输出选项
    parser.add_argument("--output", type=str, help="结果输出文件")
    parser.add_argument("--log-file", type=str, help="日志文件")
    parser.add_argument("--export-opro-data", action="store_true", help="导出OPRO数据")

    # 代理日志记录选项
    parser.add_argument("--enable-agent-logging", action="store_true", default=True,
                       help="启用代理交互日志记录")
    parser.add_argument("--disable-agent-logging", action="store_true",
                       help="禁用代理交互日志记录")
    parser.add_argument("--agent-log-path", type=str, default="data/trading",
                       help="代理日志存储路径")
    parser.add_argument("--agent-log-date", type=str,
                       help="代理日志实验日期 (YYYY-MM-DD格式，默认为当前日期)")
    parser.add_argument("--export-agent-logs", action="store_true",
                       help="导出代理交互日志")
    parser.add_argument("--agent-log-summary", action="store_true",
                       help="显示代理日志摘要")

    # 数据存储选项
    parser.add_argument("--enable-data-storage", action="store_true", default=True, help="启用综合数据存储")
    parser.add_argument("--disable-data-storage", action="store_true", help="禁用综合数据存储")
    parser.add_argument("--export-all-data", action="store_true", help="导出所有数据")
    parser.add_argument("--create-backup", action="store_true", help="创建数据备份")
    parser.add_argument("--data-report", action="store_true", help="生成数据报告")
    parser.add_argument("--analysis-report", type=str, choices=["trading", "optimization", "comprehensive"],
                       help="生成分析报告")
    parser.add_argument("--export-format", type=str, default="excel", choices=["excel", "csv", "json"],
                       help="数据导出格式")
    parser.add_argument("--backup-type", type=str, default="auto", choices=["full", "incremental", "auto"],
                       help="备份类型")
    parser.add_argument("--backup-status", action="store_true", help="显示备份状态")
    parser.add_argument("--list-backups", action="store_true", help="列出所有备份")
    
    # 配置文件
    parser.add_argument("--config", type=str, default="config/opro_config.json", 
                       help="配置文件路径")
    
    args = parser.parse_args()


    
    # 处理OPRO开关逻辑
    if args.disable_opro:
        args.enable_opro = False

    # 处理数据存储开关逻辑
    if args.disable_data_storage:
        args.enable_data_storage = False

    # 处理代理日志记录开关逻辑
    if args.disable_agent_logging:
        args.enable_agent_logging = False
    
    # 设置日志
    log_file = args.log_file or f"opro_system_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    logger = setup_logging(args.verbose, log_file)
    
    logger.info("=" * 100)
    logger.info("OPRO系统启动")
    logger.info("=" * 100)
    logger.info(f"运行模式: {args.mode}")
    logger.info(f"LLM提供商: {args.provider}")
    logger.info(f"OPRO启用: {args.enable_opro}")
    logger.info(f"数据存储启用: {args.enable_data_storage}")
    logger.info(f"代理日志记录启用: {args.enable_agent_logging}")
    
    try:
        # 加载配置
        config_data = load_config(args.config)
        
        # 创建系统配置
        system_config = create_system_config(args)



        # 初始化系统
        logger.info("初始化系统...")
        
        # 创建LLM接口（如果需要的话）
        llm_interface = None  # 暂时设为None，实际使用时再初始化

        # 初始化代理交互日志记录器
        interaction_logger = None
        if args.enable_agent_logging and AgentInteractionLogger:
            try:
                # 设置实验日期
                experiment_date = args.agent_log_date or datetime.now().strftime("%Y-%m-%d")

                interaction_logger = AgentInteractionLogger(
                    base_path=args.agent_log_path,
                    enabled=True,
                    logger=logger,
                    use_trading_dates=True  # 启用基于交易日期的分类
                )

                # 设置实验日期
                interaction_logger.set_experiment_date(experiment_date)

                logger.info(f"代理交互日志记录器初始化成功 (实验日期: {experiment_date})")
            except Exception as e:
                logger.warning(f"代理交互日志记录器初始化失败: {e}")
                interaction_logger = None
        else:
            logger.info("代理交互日志记录功能已禁用")

        # 初始化集成数据管理器
        data_manager = None
        if args.enable_data_storage:
            try:
                data_manager = IntegratedDataManager(config=config_data, logger=logger)
                logger.info("集成数据管理器初始化成功")
            except Exception as e:
                logger.warning(f"集成数据管理器初始化失败: {e}")
        else:
            logger.info("数据存储功能已禁用")

        # 创建评估器和增强管理器
        opro_config = {}
        if config_data:
            # 合并所有相关的配置部分
            opro_config.update(config_data.get("optimization", {}))
            opro_config.update(config_data.get("evaluation", {}))
            opro_config.update(config_data.get("storage", {}))

        # 检查是否启用增强模式
    # 如果用户明确指定传统模式，则使用传统模式
    # 否则，如果启用OPRO且未禁用增强模式，则使用增强模式
        use_enhanced_mode = (args.enable_opro and
                        not getattr(args, 'traditional_mode', False) and
                        getattr(args, 'enhanced_mode', True))

        if use_enhanced_mode:
            try:
                # 尝试使用增强的OPRP管理器
                from contribution_assessment.enhanced_weekly_opro_manager import EnhancedWeeklyOPROManager

                # 准备增强配置（合并到主配置中）
                enhanced_config = {
                    "enhanced_shapley_oprp": {
                        "cycle_length_days": 14,
                        "optimization_phase_days": 7,
                        "validation_phase_days": 7,
                        "underperforming_threshold": 0.3,
                        "statistical_significance_level": 0.05,
                        "minimum_improvement_threshold": 0.02,
                        "max_concurrent_experiments": 4,
                        "auto_prompt_selection": True,
                        "backup_original_prompts": True
                    },
                    "use_only_winning_data": True,
                    "min_data_points_per_agent": 5,
                    "data_quality_threshold": 0.8,
                    "enable_historical_tracking": True,
                    "max_historical_weeks": 12
                }

                # 合并配置
                full_config = system_config.copy()
                full_config.update(enhanced_config)
                if config_data:
                    full_config.update(config_data)

                # 初始化增强管理器
                enhanced_manager = EnhancedWeeklyOPROManager(
                    config=full_config,
                    base_data_dir=config_data.get("storage", {}).get("comprehensive_storage", {}).get("trading_data_path", "data/trading") if config_data else "data/trading",
                    logger=logger
                )

                logger.info("✅ 增强OPRP管理器初始化成功")
                assessor = enhanced_manager  # 使用增强管理器作为主要评估器

            except ImportError as e:
                logger.warning(f"⚠️  增强OPRP组件不可用，回退到传统模式: {e}")
                use_enhanced_mode = False
            except Exception as e:
                logger.error(f"❌ 增强OPRP管理器初始化失败，回退到传统模式: {e}")
                use_enhanced_mode = False

        if not use_enhanced_mode:
            # 使用传统的ContributionAssessor
            assessor = ContributionAssessor(
                config=system_config,
                logger=logger,
                llm_provider=args.provider,
                enable_opro=args.enable_opro,
                opro_config=opro_config,
                interaction_logger=interaction_logger
            )
        
        logger.info("系统初始化完成")

        # 根据模式运行
        if args.mode == "evaluation":
            result = run_evaluation_mode_unified(assessor, args, logger, use_enhanced_mode)
        elif args.mode == "optimization":
            result = run_optimization_mode_unified(assessor, args, logger, use_enhanced_mode)
        elif args.mode == "integrated":
            result = run_integrated_mode_unified(assessor, args, logger, use_enhanced_mode)
        elif args.mode == "dashboard":
            result = run_dashboard_mode_unified(assessor, args, logger, use_enhanced_mode)
        else:
            raise ValueError(f"未知运行模式: {args.mode}")

        # 处理数据存储（如果启用）
        if data_manager and data_manager.enabled and result.get("success", False):
            try:
                # 处理评估结果
                if "result" in result and isinstance(result["result"], dict):
                    processing_summary = data_manager.process_assessment_result(result["result"])
                    result["data_processing"] = processing_summary
                    logger.info("评估结果数据处理完成")

                # 如果是优化模式，跟踪提示词优化
                if args.mode in ["optimization", "integrated"] and "result" in result:
                    optimization_result = result["result"].get("optimization_result", {})
                    if optimization_result.get("success", False):
                        # 这里可以添加提示词优化跟踪逻辑
                        logger.info("提示词优化数据跟踪完成")

            except Exception as e:
                logger.error(f"数据处理失败: {e}")
                result["data_processing_error"] = str(e)
        
        # 添加执行信息
        result.update({
            "execution_info": {
                "timestamp": datetime.now().isoformat(),
                "mode": args.mode,
                "opro_enabled": args.enable_opro,
                "llm_provider": args.provider,
                "config_file": args.config
            }
        })
        
        # 输出结果
        success = result.get("success", False)
        logger.info("=" * 100)
        if success:
            logger.info(safe_log_message("🎉 执行成功!"))
        else:
            logger.error(safe_log_message("❌ 执行失败!"))
            if "error" in result:
                logger.error(f"错误: {result['error']}")
        logger.info("=" * 100)
        
        # 导出结果
        if args.output:
            export_results(result, args.output, logger)
        
        # 导出OPRO数据
        if args.export_opro_data:
            enable_opro = getattr(assessor, 'enable_opro', False) if not use_enhanced_mode else True
            if enable_opro:
                try:
                    if use_enhanced_mode:
                        # 增强模式的数据导出
                        export_result = {"success": True, "export_directory": "data/trading", "message": "增强模式数据已存储"}
                        logger.info(f"增强模式数据已存储至: {export_result['export_directory']}")
                    else:
                        export_result = assessor.export_opro_data()
                        if export_result.get("success", False):
                            logger.info(f"OPRO数据已导出至: {export_result['export_directory']}")
                        else:
                            logger.warning(f"OPRO数据导出失败: {export_result.get('error', '未知错误')}")
                except Exception as e:
                    logger.warning(f"数据导出过程中出现错误: {e}")
            else:
                logger.warning("OPRO功能未启用，跳过数据导出")

        # 处理代理日志记录相关命令
        if interaction_logger and interaction_logger.enabled:
            # 显示代理日志摘要
            if args.agent_log_summary:
                summary = interaction_logger.get_all_agents_summary()
                if summary.get("enabled", False):
                    logger.info("=" * 60)
                    logger.info("代理交互日志摘要")
                    logger.info("=" * 60)
                    logger.info(f"实验日期: {summary['experiment_date']}")
                    logger.info(f"日志目录: {summary['base_directory']}")
                    logger.info(f"代理总数: {summary['total_agents']}")

                    for agent_name, agent_summary in summary.get("agents", {}).items():
                        logger.info(f"  {agent_name}:")
                        logger.info(f"    输入记录: {agent_summary.get('inputs_count', 0)}")
                        logger.info(f"    提示词记录: {agent_summary.get('prompts_count', 0)}")
                        logger.info(f"    输出记录: {agent_summary.get('outputs_count', 0)}")

                    logger.info("=" * 60)
                else:
                    logger.error("获取代理日志摘要失败")

            # 导出代理日志
            if args.export_agent_logs:
                # 获取所有代理的日志摘要
                summary = interaction_logger.get_all_agents_summary()
                if summary.get("enabled", False) and summary.get("agents"):
                    logger.info("导出代理交互日志...")

                    for agent_name in summary["agents"].keys():
                        export_result = interaction_logger.export_agent_logs(
                            agent_name=agent_name,
                            export_format="json"
                        )

                        if export_result.get("success", False):
                            logger.info(f"代理 {agent_name} 日志已导出至: {export_result['export_path']}")
                        else:
                            logger.error(f"导出代理 {agent_name} 日志失败: {export_result.get('error', '未知错误')}")
                else:
                    logger.warning("没有找到代理日志数据可供导出")

        # 处理数据存储相关命令
        if data_manager and data_manager.enabled:
            # 导出所有数据
            if args.export_all_data:
                export_result = data_manager.export_all_data(export_format=args.export_format)
                if export_result.get("success", False):
                    logger.info(f"所有数据已导出至: {export_result['export_directory']}")
                else:
                    logger.error(f"导出数据失败: {export_result.get('error', '未知错误')}")

            # 生成分析报告
            if args.analysis_report:
                analysis_result = data_manager.generate_analysis_report(report_type=args.analysis_report)
                if analysis_result.get("success", False):
                    logger.info(f"分析报告已生成: {analysis_result.get('export_directory', analysis_result.get('dashboard_directory', ''))}")

                    # 显示报告摘要
                    summary = analysis_result.get("analysis_summary", {})
                    if summary:
                        logger.info("分析报告摘要:")
                        for key, value in summary.items():
                            logger.info(f"  {key}: {value}")
                else:
                    logger.error(f"生成分析报告失败: {analysis_result.get('error', '未知错误')}")

            # 创建备份
            if args.create_backup:
                backup_result = data_manager.create_backup(backup_type=args.backup_type)
                if backup_result.get("success", False):
                    logger.info(f"数据备份已创建: {backup_result['backup_path']} ({backup_result['backup_size_mb']} MB)")
                else:
                    logger.error(f"创建备份失败: {backup_result.get('error', '未知错误')}")

            # 显示备份状态
            if args.backup_status:
                backup_status = data_manager.get_backup_status()
                if backup_status.get("enabled", False):
                    logger.info("=" * 60)
                    logger.info("备份系统状态")
                    logger.info("=" * 60)
                    logger.info(f"自动备份运行: {backup_status.get('auto_backup_running', False)}")
                    logger.info(f"总备份数: {backup_status.get('total_backups', 0)}")
                    logger.info(f"完整备份数: {backup_status.get('full_backups', 0)}")
                    logger.info(f"增量备份数: {backup_status.get('incremental_backups', 0)}")
                    logger.info(f"总大小: {backup_status.get('total_size_mb', 0):.2f} MB")

                    latest_backup = backup_status.get('latest_backup')
                    if latest_backup:
                        logger.info(f"最新备份: {latest_backup['backup_id']} ({latest_backup['backup_type']})")
                        logger.info(f"最新备份时间: {latest_backup['timestamp']}")

                    logger.info("=" * 60)
                else:
                    logger.error("备份系统未启用或出现错误")

            # 列出备份
            if args.list_backups:
                backups = data_manager.list_backups()
                if backups:
                    logger.info("=" * 80)
                    logger.info("备份列表")
                    logger.info("=" * 80)
                    logger.info(f"{'备份ID':<30} {'类型':<12} {'时间':<20} {'大小(MB)':<10} {'状态':<8}")
                    logger.info("-" * 80)

                    for backup in backups[:10]:  # 显示最近10个备份
                        status = "存在" if backup.get("exists", False) else "缺失"
                        logger.info(f"{backup['backup_id']:<30} {backup['backup_type']:<12} "
                                  f"{backup['timestamp'][:19]:<20} {backup['file_size_mb']:<10.2f} {status:<8}")

                    if len(backups) > 10:
                        logger.info(f"... 还有 {len(backups) - 10} 个备份")

                    logger.info("=" * 80)
                else:
                    logger.info("暂无备份记录")

            # 生成数据报告
            if args.data_report:
                report = data_manager.generate_comprehensive_report()
                if report.get("enabled", False):
                    logger.info("=" * 60)
                    logger.info("数据存储系统报告")
                    logger.info("=" * 60)

                    # 存储统计
                    storage_stats = report.get("storage_statistics", {})
                    if storage_stats:
                        logger.info(f"数据库大小: {storage_stats.get('database_size_mb', 0)} MB")
                        logger.info(f"交易会话数: {storage_stats.get('total_trading_sessions', 0)}")
                        logger.info(f"提示词优化数: {storage_stats.get('total_prompt_optimizations', 0)}")

                    # 建议
                    recommendations = report.get("recommendations", [])
                    if recommendations:
                        logger.info("建议:")
                        for rec in recommendations:
                            logger.info(f"  - {rec}")

                    logger.info("=" * 60)
                else:
                    logger.error("生成数据报告失败")

        # 返回适当的退出代码
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logger.error(f"系统执行异常: {e}")
        sys.exit(1)

# ==================== 辅助函数实现 ====================

def generate_trading_date_range(start_date: str, end_date: str, logger: logging.Logger) -> List[str]:
    """
    生成完整的交易日期范围

    参数:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        logger: 日志记录器

    返回:
        交易日期字符串列表
    """
    try:
        # 使用pandas生成工作日（排除周末）
        date_range = pd.date_range(start=start_date, end=end_date, freq='B')
        trading_dates = [date.strftime('%Y-%m-%d') for date in date_range]

        logger.info(f"📅 生成交易日期范围: {start_date} 到 {end_date}")
        logger.info(f"📊 总交易日数: {len(trading_dates)}")

        if len(trading_dates) > 0:
            logger.info(f"🗓️  首个交易日: {trading_dates[0]}")
            logger.info(f"🗓️  最后交易日: {trading_dates[-1]}")

        return trading_dates

    except Exception as e:
        logger.error(f"❌ 生成交易日期范围失败: {e}")
        return []

def run_daily_trading_loop(assessor: ContributionAssessor,
                          trading_dates: List[str],
                          target_agents: Optional[List[str]] = None,
                          max_coalitions: Optional[int] = None,
                          logger: Optional[logging.Logger] = None) -> Dict[str, Any]:
    """
    运行每日交易决策循环

    参数:
        assessor: 贡献度评估器
        trading_dates: 交易日期列表
        target_agents: 目标智能体列表
        max_coalitions: 最大联盟数量
        logger: 日志记录器

    返回:
        每日交易结果字典
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    daily_results = {}
    total_days = len(trading_dates)

    logger.info(f"🔄 开始每日交易循环，共 {total_days} 个交易日")

    for i, date in enumerate(trading_dates, 1):
        logger.info(f"📅 处理第 {i}/{total_days} 天: {date}")

        try:
            # 为当天创建单独的配置
            daily_config = assessor.config.copy()
            daily_config["start_date"] = date
            daily_config["end_date"] = date
            daily_config["simulation_days"] = 1

            # 临时更新评估器配置
            original_config = assessor.config
            assessor.config = daily_config

            # 重新初始化交易模拟器
            from contribution_assessment.trading_simulator import TradingSimulator
            assessor.trading_simulator = TradingSimulator(
                base_config=daily_config,
                logger=assessor.logger
            )

            # 运行当日评估
            daily_result = assessor.run(
                agents=None,  # 使用默认智能体
                target_agents=target_agents,
                max_coalitions=max_coalitions
            )

            # 确保代理日志记录器使用正确的交易日期
            if hasattr(assessor, 'interaction_logger') and assessor.interaction_logger:
                # 由于我们已经在每日配置中设置了正确的日期，
                # 代理在执行过程中会自动从状态数据中提取交易日期
                logger.debug(f"代理日志记录器将使用交易日期: {date}")

            # 提取详细的代理执行结果
            agent_execution_details = extract_agent_execution_details(daily_result, date, logger)

            daily_results[date] = {
                "success": daily_result.get("success", False),
                "shapley_values": daily_result.get("shapley_values", {}),
                "coalition_values": daily_result.get("coalition_values", {}),
                "execution_time": daily_result.get("execution_time", 0.0),
                "date": date,
                "agent_execution_details": agent_execution_details,
                "simulation_result": daily_result.get("simulation_result", {}),
                "weekly_results": daily_result.get("weekly_results", [])
            }

            if daily_result.get("success", False):
                logger.info(f"✅ 第 {i} 天 ({date}) 处理成功")
            else:
                logger.warning(f"⚠️  第 {i} 天 ({date}) 处理失败: {daily_result.get('error', '未知错误')}")
                daily_results[date]["error"] = daily_result.get("error", "未知错误")

        except Exception as e:
            logger.error(f"❌ 第 {i} 天 ({date}) 处理异常: {e}")
            daily_results[date] = {
                "success": False,
                "error": str(e),
                "date": date
            }

        finally:
            # 恢复原始配置
            assessor.config = original_config
            assessor.trading_simulator = TradingSimulator(
                base_config=original_config,
                logger=assessor.logger
            )

    successful_days = sum(1 for result in daily_results.values() if result.get("success", False))
    logger.info(f"📊 每日交易循环完成: {successful_days}/{total_days} 天成功")

    return daily_results

def run_weekly_opro_optimization(assessor: ContributionAssessor,
                                daily_results: Dict[str, Any],
                                target_agents: Optional[List[str]] = None,
                                logger: Optional[logging.Logger] = None) -> Dict[str, Any]:
    """
    运行周期性OPRO优化（集成增强的Shapley值计算系统）

    参数:
        assessor: 贡献度评估器
        daily_results: 每日交易结果
        target_agents: 目标智能体列表
        logger: 日志记录器

    返回:
        周期性优化结果
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    if not assessor.enable_opro:
        logger.info("⚠️  OPRO优化未启用，跳过周期性优化")
        return {"success": False, "reason": "OPRO未启用"}

    logger.info("🔄 开始周期性OPRO优化（使用增强的Shapley值计算系统）...")

    try:
        # 导入新的Shapley值计算组件
        from contribution_assessment.weekly_shapley_trigger import WeeklyShapleyTrigger
        from contribution_assessment.enhanced_shapley_storage_manager import EnhancedShapleyStorageManager
        from contribution_assessment.coalition_experiment_tracker import CoalitionExperimentTracker

        # 初始化增强的Shapley值计算系统
        shapley_config = {
            "data_dir": "data/trading",
            "shapley_analysis": {
                "enabled": True,
                "weekly_trigger": True,
                "max_concurrent_experiments": 4,
                "experiment_timeout": 300,
                "auto_retry_failed": True,
                "max_retry_attempts": 3
            },
            "opro_integration": {
                "coordinate_with_opro": True,
                "priority_level": "high"
            },
            "agents": {
                "analysis_agents": ["NAA", "TAA", "FAA"],
                "outlook_agents": ["BOA", "BeOA", "NOA"],
                "trading_agents": ["TRA"]
            }
        }

        # 创建增强的Shapley值计算触发器
        shapley_trigger = WeeklyShapleyTrigger(
            config=shapley_config,
            logger=logger
        )

        logger.info("✅ 增强的Shapley值计算系统初始化完成")

    except ImportError as e:
        logger.warning(f"⚠️  无法导入增强的Shapley值计算组件，使用传统方法: {e}")
        shapley_trigger = None
    except Exception as e:
        logger.error(f"❌ 增强的Shapley值计算系统初始化失败: {e}")
        shapley_trigger = None

    # 按周分组日期（每5个交易日为一周）
    trading_dates = sorted(daily_results.keys())
    weeks = []

    for i in range(0, len(trading_dates), 5):
        week_dates = trading_dates[i:i+5]
        weeks.append(week_dates)

    weekly_optimization_results = {}

    for week_num, week_dates in enumerate(weeks, 1):
        logger.info(f"📅 处理第 {week_num} 周: {week_dates[0]} 到 {week_dates[-1]}")

        try:
            # 使用增强的Shapley值计算系统（如果可用）
            if shapley_trigger:
                logger.info(f"🔬 使用增强的Shapley值计算系统分析第 {week_num} 周")

                # 检查是否满足触发条件
                current_date = datetime.strptime(week_dates[-1], "%Y-%m-%d")
                week_daily_results = {date: daily_results[date] for date in week_dates if date in daily_results}

                if shapley_trigger.check_weekly_trigger_conditions(current_date, week_daily_results):
                    # 触发增强的Shapley值分析
                    shapley_analysis_result = shapley_trigger.trigger_shapley_analysis(
                        week_number=week_num,
                        force_trigger=False
                    )

                    if shapley_analysis_result.get("status") == "success":
                        logger.info(f"✅ 第 {week_num} 周增强Shapley分析完成")

                        # 获取Shapley分析结果
                        shapley_results = shapley_analysis_result.get("shapley_results", {})
                        shapley_values = shapley_results.get("shapley_values", {})

                        # 与OPRO系统协调
                        coordination_result = shapley_trigger.coordinate_with_opro_cycle(
                            assessor, shapley_results
                        )

                        # 识别需要优化的智能体
                        underperforming_agents = coordination_result.get("underperforming_agents", [])

                        if underperforming_agents:
                            logger.info(f"🎯 基于增强Shapley分析识别到表现较差的智能体: {underperforming_agents}")

                            # 执行OPRO优化
                            optimization_result = assessor.run_opro_optimization_cycle(
                                target_agents=underperforming_agents,
                                force_optimization=True
                            )

                            weekly_optimization_results[f"week_{week_num}"] = {
                                "week_dates": week_dates,
                                "enhanced_shapley_analysis": shapley_analysis_result,
                                "coordination_result": coordination_result,
                                "underperforming_agents": underperforming_agents,
                                "optimization_result": optimization_result,
                                "method": "enhanced_shapley_system"
                            }

                            if optimization_result.get("success", False):
                                logger.info(f"✅ 第 {week_num} 周优化成功（增强系统）")
                            else:
                                logger.warning(f"⚠️  第 {week_num} 周优化失败（增强系统）")
                        else:
                            logger.info(f"✅ 第 {week_num} 周所有智能体表现良好，无需优化（增强系统）")
                            weekly_optimization_results[f"week_{week_num}"] = {
                                "week_dates": week_dates,
                                "enhanced_shapley_analysis": shapley_analysis_result,
                                "coordination_result": coordination_result,
                                "optimization_needed": False,
                                "method": "enhanced_shapley_system"
                            }
                    else:
                        logger.warning(f"⚠️  第 {week_num} 周增强Shapley分析失败，使用传统方法")
                        # 回退到传统方法
                        week_result = _run_traditional_weekly_optimization(
                            assessor, week_num, week_dates, daily_results, logger
                        )
                        weekly_optimization_results[f"week_{week_num}"] = week_result
                else:
                    logger.info(f"📊 第 {week_num} 周不满足Shapley分析触发条件，跳过")
                    weekly_optimization_results[f"week_{week_num}"] = {
                        "week_dates": week_dates,
                        "trigger_conditions_met": False,
                        "method": "enhanced_shapley_system"
                    }
            else:
                # 使用传统的Shapley值计算方法
                logger.info(f"📊 使用传统Shapley值计算方法分析第 {week_num} 周")
                week_result = _run_traditional_weekly_optimization(
                    assessor, week_num, week_dates, daily_results, logger
                )
                weekly_optimization_results[f"week_{week_num}"] = week_result

        except Exception as e:
            logger.error(f"❌ 第 {week_num} 周优化失败: {e}")
            weekly_optimization_results[f"week_{week_num}"] = {
                "week_dates": week_dates,
                "success": False,
                "error": str(e),
                "method": "enhanced_shapley_system" if shapley_trigger else "traditional"
            }

    logger.info(f"📊 周期性OPRO优化完成，处理了 {len(weeks)} 周")

    return {
        "success": True,
        "total_weeks": len(weeks),
        "weekly_results": weekly_optimization_results,
        "enhanced_system_used": shapley_trigger is not None
    }

def _run_traditional_weekly_optimization(assessor: ContributionAssessor,
                                       week_num: int,
                                       week_dates: List[str],
                                       daily_results: Dict[str, Any],
                                       logger: logging.Logger) -> Dict[str, Any]:
    """
    运行传统的周期性优化方法

    参数:
        assessor: 贡献度评估器
        week_num: 周期编号
        week_dates: 周期日期列表
        daily_results: 每日交易结果
        logger: 日志记录器

    返回:
        周期优化结果
    """
    try:
        # 收集本周的Shapley值数据
        week_shapley_data = {}
        for date in week_dates:
            if date in daily_results and daily_results[date].get("success", False):
                shapley_values = daily_results[date].get("shapley_values", {})
                for agent, value in shapley_values.items():
                    if agent not in week_shapley_data:
                        week_shapley_data[agent] = []
                    week_shapley_data[agent].append(value)

        # 计算本周平均Shapley值
        week_avg_shapley = {}
        for agent, values in week_shapley_data.items():
            if values:
                week_avg_shapley[agent] = sum(values) / len(values)

        # 识别表现较差的智能体（低于平均值的智能体）
        if week_avg_shapley:
            avg_performance = sum(week_avg_shapley.values()) / len(week_avg_shapley)
            underperforming_agents = [
                agent for agent, value in week_avg_shapley.items()
                if value < avg_performance * 0.8  # 低于平均值80%的智能体
            ]

            if underperforming_agents:
                logger.info(f"🎯 识别到表现较差的智能体: {underperforming_agents}")

                # 首先为这些智能体创建初始历史数据（如果不存在）
                for agent_id in underperforming_agents:
                    try:
                        if hasattr(assessor, 'historical_score_manager') and assessor.historical_score_manager:
                            # 为智能体创建初始优化历史记录
                            initial_score = week_avg_shapley.get(agent_id, 0.0)
                            assessor.historical_score_manager.store_optimization_result(
                                agent_id=agent_id,
                                prompt=f"默认提示词 - {agent_id}",
                                estimated_score=initial_score,
                                metadata={
                                    "source": "initial_setup",
                                    "week": week_num,
                                    "is_baseline": True
                                }
                            )
                            logger.debug(f"为智能体 {agent_id} 创建初始历史记录")
                    except Exception as e:
                        logger.warning(f"为智能体 {agent_id} 创建初始历史记录失败: {e}")

                # 对表现较差的智能体进行优化
                optimization_result = assessor.run_opro_optimization_cycle(
                    target_agents=underperforming_agents,
                    force_optimization=True
                )

                result = {
                    "week_dates": week_dates,
                    "underperforming_agents": underperforming_agents,
                    "optimization_result": optimization_result,
                    "week_avg_shapley": week_avg_shapley,
                    "method": "traditional"
                }

                if optimization_result.get("success", False):
                    logger.info(f"✅ 第 {week_num} 周优化成功（传统方法）")
                else:
                    logger.warning(f"⚠️  第 {week_num} 周优化失败（传统方法）")

                return result
            else:
                logger.info(f"✅ 第 {week_num} 周所有智能体表现良好，无需优化（传统方法）")
                return {
                    "week_dates": week_dates,
                    "optimization_needed": False,
                    "week_avg_shapley": week_avg_shapley,
                    "method": "traditional"
                }
        else:
            logger.warning(f"⚠️  第 {week_num} 周无有效的Shapley值数据")
            return {
                "week_dates": week_dates,
                "success": False,
                "error": "无有效的Shapley值数据",
                "method": "traditional"
            }

    except Exception as e:
        logger.error(f"❌ 传统方法处理第 {week_num} 周失败: {e}")
        return {
            "week_dates": week_dates,
            "success": False,
            "error": str(e),
            "method": "traditional"
        }

def calculate_trading_statistics(daily_results: Dict[str, Any], logger: logging.Logger) -> Dict[str, Any]:
    """
    计算交易统计信息

    参数:
        daily_results: 每日交易结果
        logger: 日志记录器

    返回:
        交易统计信息字典
    """
    logger.info("📊 开始计算交易统计信息...")

    try:
        total_days = len(daily_results)
        successful_days = sum(1 for result in daily_results.values() if result.get("success", False))
        success_rate = successful_days / total_days if total_days > 0 else 0.0

        # 收集所有智能体的Shapley值
        all_shapley_values = {}
        daily_shapley_data = []

        for date, result in daily_results.items():
            if result.get("success", False) and "shapley_values" in result:
                shapley_values = result["shapley_values"]
                daily_shapley_data.append({
                    "date": date,
                    "shapley_values": shapley_values
                })

                for agent, value in shapley_values.items():
                    if agent not in all_shapley_values:
                        all_shapley_values[agent] = []
                    all_shapley_values[agent].append(value)

        # 计算每个智能体的统计信息
        agent_statistics = {}
        for agent, values in all_shapley_values.items():
            if values:
                agent_statistics[agent] = {
                    "mean_shapley": sum(values) / len(values),
                    "max_shapley": max(values),
                    "min_shapley": min(values),
                    "total_contribution": sum(values),
                    "active_days": len(values)
                }

        # 计算总体统计
        overall_statistics = {
            "total_trading_days": total_days,
            "successful_days": successful_days,
            "success_rate": success_rate,
            "total_agents": len(agent_statistics),
            "agent_statistics": agent_statistics,
            "daily_data": daily_shapley_data
        }

        # 识别最佳和最差表现的智能体
        if agent_statistics:
            best_agent = max(agent_statistics.items(), key=lambda x: x[1]["mean_shapley"])
            worst_agent = min(agent_statistics.items(), key=lambda x: x[1]["mean_shapley"])

            overall_statistics["best_performing_agent"] = {
                "agent_id": best_agent[0],
                "mean_shapley": best_agent[1]["mean_shapley"]
            }
            overall_statistics["worst_performing_agent"] = {
                "agent_id": worst_agent[0],
                "mean_shapley": worst_agent[1]["mean_shapley"]
            }

        logger.info(f"✅ 交易统计计算完成:")
        logger.info(f"   📈 总交易天数: {total_days}")
        logger.info(f"   ✅ 成功天数: {successful_days}")
        logger.info(f"   📊 成功率: {success_rate:.2%}")
        logger.info(f"   🤖 活跃智能体数: {len(agent_statistics)}")

        if agent_statistics:
            logger.info(f"   🏆 最佳智能体: {overall_statistics['best_performing_agent']['agent_id']} "
                       f"(平均Shapley值: {overall_statistics['best_performing_agent']['mean_shapley']:.6f})")
            logger.info(f"   📉 最差智能体: {overall_statistics['worst_performing_agent']['agent_id']} "
                       f"(平均Shapley值: {overall_statistics['worst_performing_agent']['mean_shapley']:.6f})")

        return overall_statistics

    except Exception as e:
        logger.error(f"❌ 计算交易统计失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "total_trading_days": len(daily_results),
            "successful_days": 0,
            "success_rate": 0.0
        }

def ensure_opro_historical_data(assessor: ContributionAssessor,
                               target_agents: List[str],
                               logger: logging.Logger) -> bool:
    """
    确保OPRO系统有足够的历史数据进行优化

    参数:
        assessor: 贡献度评估器
        target_agents: 目标智能体列表
        logger: 日志记录器

    返回:
        是否成功确保历史数据
    """
    if not assessor.enable_opro or not hasattr(assessor, 'historical_score_manager') or not assessor.historical_score_manager:
        logger.warning("⚠️  OPRO未启用或历史得分管理器不可用")
        return False

    try:
        logger.info("🔍 检查OPRO历史数据...")

        for agent_id in target_agents:
            # 检查智能体是否有足够的历史数据
            history = assessor.historical_score_manager.get_agent_optimization_history(agent_id, weeks=4)

            if len(history) < 2:
                logger.info(f"📝 为智能体 {agent_id} 创建初始历史数据...")

                # 创建基础历史记录
                base_scores = [0.1, 0.15, 0.12, 0.18]  # 模拟的基础得分

                for i, score in enumerate(base_scores):
                    assessor.historical_score_manager.store_optimization_result(
                        agent_id=agent_id,
                        prompt=f"基础提示词 v{i+1} - {agent_id}",
                        estimated_score=score,
                        metadata={
                            "source": "bootstrap",
                            "week": i + 1,
                            "is_baseline": True,
                            "created_for_opro": True
                        }
                    )

                logger.info(f"✅ 为智能体 {agent_id} 创建了 {len(base_scores)} 条历史记录")
            else:
                logger.info(f"✅ 智能体 {agent_id} 已有 {len(history)} 条历史记录")

        logger.info("🎯 OPRO历史数据检查完成")
        return True

    except Exception as e:
        logger.error(f"❌ 确保OPRO历史数据失败: {e}")
        return False

def extract_agent_execution_details(daily_result: Dict[str, Any],
                                   date: str,
                                   logger: logging.Logger) -> Dict[str, Any]:
    """
    从每日评估结果中提取详细的代理执行信息

    参数:
        daily_result: 每日评估结果
        date: 交易日期
        logger: 日志记录器

    返回:
        代理执行详细信息
    """
    try:
        execution_details = {
            "date": date,
            "analysis_layer": {},
            "outlook_layer": {},
            "trading_layer": {},
            "execution_summary": {}
        }

        # 从模拟结果中提取代理执行信息
        simulation_result = daily_result.get("simulation_result", {})
        coalition_results = simulation_result.get("coalition_results", {})

        # 统计代理执行情况
        total_coalitions = len(coalition_results)
        successful_coalitions = 0
        agent_participation = {}

        for coalition, result in coalition_results.items():
            if isinstance(result, dict) and result.get("sharpe_ratio", 0) > 0:
                successful_coalitions += 1

            # 统计代理参与情况
            if isinstance(coalition, (list, tuple, set)):
                for agent_id in coalition:
                    if agent_id not in agent_participation:
                        agent_participation[agent_id] = {"total": 0, "successful": 0}
                    agent_participation[agent_id]["total"] += 1
                    if isinstance(result, dict) and result.get("sharpe_ratio", 0) > 0:
                        agent_participation[agent_id]["successful"] += 1

        # 按层级分类代理
        analysis_agents = ["NAA", "TAA", "FAA"]
        outlook_agents = ["BOA", "BeOA", "NOA"]
        trading_agents = ["TRA"]

        for agent_id, stats in agent_participation.items():
            success_rate = stats["successful"] / stats["total"] if stats["total"] > 0 else 0.0
            agent_info = {
                "total_coalitions": stats["total"],
                "successful_coalitions": stats["successful"],
                "success_rate": success_rate,
                "shapley_value": daily_result.get("shapley_values", {}).get(agent_id, 0.0)
            }

            if agent_id in analysis_agents:
                execution_details["analysis_layer"][agent_id] = agent_info
            elif agent_id in outlook_agents:
                execution_details["outlook_layer"][agent_id] = agent_info
            elif agent_id in trading_agents:
                execution_details["trading_layer"][agent_id] = agent_info

        # 执行摘要
        execution_details["execution_summary"] = {
            "total_coalitions": total_coalitions,
            "successful_coalitions": successful_coalitions,
            "success_rate": successful_coalitions / total_coalitions if total_coalitions > 0 else 0.0,
            "total_agents": len(agent_participation),
            "execution_time": daily_result.get("execution_time", 0.0),
            "overall_success": daily_result.get("success", False)
        }

        logger.debug(f"提取代理执行详情: {date} - {len(agent_participation)} 个代理, "
                    f"{successful_coalitions}/{total_coalitions} 个成功联盟")

        return execution_details

    except Exception as e:
        logger.error(f"❌ 提取代理执行详情失败: {e}")
        return {
            "date": date,
            "error": str(e),
            "analysis_layer": {},
            "outlook_layer": {},
            "trading_layer": {},
            "execution_summary": {"error": str(e)}
        }

# ==================== 统一模式运行函数 ====================

def run_evaluation_mode_unified(assessor, args, logger: logging.Logger, use_enhanced_mode: bool) -> Dict[str, Any]:
    """统一的评估模式运行函数"""
    if use_enhanced_mode:
        logger.info("=" * 80)
        logger.info("运行模式: 增强评估模式")
        logger.info("=" * 80)

        try:
            # 使用增强管理器进行评估
            target_agents = args.agents.split(',') if args.agents else ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]

            # 创建模拟的每日结果用于评估
            daily_results = {}
            current_date = args.start_date if hasattr(args, 'start_date') and args.start_date else "2025-01-01"

            result = assessor.run_enhanced_weekly_cycle(
                current_date=current_date,
                target_agents=target_agents,
                daily_results=daily_results
            )

            return {
                "mode": "enhanced_evaluation",
                "result": result,
                "success": result.get("status") in ["validation_complete", "cycle_complete"]
            }

        except Exception as e:
            logger.error(f"增强评估模式执行失败: {e}")
            return {
                "mode": "enhanced_evaluation",
                "success": False,
                "error": str(e)
            }
    else:
        return run_evaluation_mode(assessor, args, logger)

def run_optimization_mode_unified(assessor, args, logger: logging.Logger, use_enhanced_mode: bool) -> Dict[str, Any]:
    """统一的优化模式运行函数"""
    if use_enhanced_mode:
        logger.info("=" * 80)
        logger.info("运行模式: 增强优化模式")
        logger.info("=" * 80)

        try:
            target_agents = args.agents.split(',') if args.agents else ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]

            # 运行增强优化周期
            daily_results = {}
            current_date = args.start_date if hasattr(args, 'start_date') and args.start_date else "2025-01-01"

            result = assessor.run_enhanced_weekly_cycle(
                current_date=current_date,
                target_agents=target_agents,
                daily_results=daily_results
            )

            return {
                "mode": "enhanced_optimization",
                "result": result,
                "success": result.get("status") in ["validation_complete", "cycle_complete"]
            }

        except Exception as e:
            logger.error(f"增强优化模式执行失败: {e}")
            return {
                "mode": "enhanced_optimization",
                "success": False,
                "error": str(e)
            }
    else:
        return run_optimization_mode(assessor, args, logger)

def run_integrated_mode_unified(assessor, args, logger: logging.Logger, use_enhanced_mode: bool) -> Dict[str, Any]:
    """统一的集成模式运行函数（评估+优化）"""
    if use_enhanced_mode:
        logger.info("=" * 80)
        logger.info("运行模式: 增强集成模式（14天双轨实验）")
        logger.info("=" * 80)

        try:
            target_agents = args.agents.split(',') if args.agents else ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]

            # 运行增强的完整日期范围交易系统
            logger.info(f"开始运行增强的完整日期范围交易系统: {args.start_date} 到 {args.end_date}")

            result = run_enhanced_full_date_range_trading_system(
                enhanced_manager=assessor,
                start_date=args.start_date,
                end_date=args.end_date,
                target_agents=target_agents,
                logger=logger
            )

            return {
                "mode": "enhanced_integrated",
                "result": result,
                "success": result.get("success", False)
            }

        except Exception as e:
            logger.error(f"增强集成模式执行失败: {e}")
            return {
                "mode": "enhanced_integrated",
                "success": False,
                "error": str(e)
            }
    else:
        return run_integrated_mode(assessor, args, logger)

def run_dashboard_mode_unified(assessor, args, logger: logging.Logger, use_enhanced_mode: bool) -> Dict[str, Any]:
    """统一的仪表板模式运行函数"""
    if use_enhanced_mode:
        logger.info("=" * 80)
        logger.info("运行模式: 增强仪表板模式")
        logger.info("=" * 80)

        try:
            # 显示增强系统状态
            status = {
                "enhanced_mode": True,
                "system_type": "EnhancedWeeklyOPROManager",
                "cycle_length": getattr(assessor.enhanced_config, 'cycle_length_days', 14),
                "optimization_phase": getattr(assessor.enhanced_config, 'optimization_phase_days', 7),
                "validation_phase": getattr(assessor.enhanced_config, 'validation_phase_days', 7)
            }

            logger.info("📊 增强OPRP系统状态:")
            logger.info(f"  - 系统类型: {status['system_type']}")
            logger.info(f"  - 周期长度: {status['cycle_length']}天")
            logger.info(f"  - 优化阶段: {status['optimization_phase']}天")
            logger.info(f"  - 验证阶段: {status['validation_phase']}天")

            return {
                "mode": "enhanced_dashboard",
                "result": status,
                "success": True
            }

        except Exception as e:
            logger.error(f"增强仪表板模式执行失败: {e}")
            return {
                "mode": "enhanced_dashboard",
                "success": False,
                "error": str(e)
            }
    else:
        return run_dashboard_mode(assessor, args, logger)

def run_enhanced_full_date_range_trading_system(enhanced_manager,
                                              start_date: str,
                                              end_date: str,
                                              target_agents: List[str],
                                              logger: logging.Logger) -> Dict[str, Any]:
    """
    运行增强的完整日期范围交易系统，实现14天双轨实验周期

    参数:
        enhanced_manager: 增强的OPRP管理器
        start_date: 开始日期
        end_date: 结束日期
        target_agents: 目标智能体列表
        logger: 日志记录器

    返回:
        完整的增强交易系统执行结果
    """
    logger.info("=" * 100)
    logger.info("🚀 启动增强的完整日期范围交易系统（14天双轨实验）")
    logger.info("=" * 100)
    logger.info(f"📅 交易期间: {start_date} 到 {end_date}")
    logger.info(f"🤖 目标智能体: {target_agents}")
    logger.info(f"🔄 增强OPRP: 启用（14天双轨实验）")

    start_time = time.time()
    result = {
        "success": True,
        "start_date": start_date,
        "end_date": end_date,
        "enhanced_mode": True,
        "cycle_results": {},
        "daily_results": {},
        "iterative_shapley_results": {},
        "total_execution_time": 0.0,
        "trading_statistics": {}
    }

    try:
        # 步骤1：生成14天周期的交易日期列表
        logger.info("步骤1: 生成14天周期的交易日期列表...")
        trading_dates = generate_trading_date_range(start_date, end_date, logger)

        if not trading_dates:
            raise ValueError("无法生成有效的交易日期范围")

        # 步骤2：按14天周期分组处理
        logger.info("步骤2: 按14天周期分组处理...")
        cycles = []
        cycle_length = enhanced_manager.enhanced_config.cycle_length_days

        for i in range(0, len(trading_dates), cycle_length):
            cycle_dates = trading_dates[i:i + cycle_length]
            if len(cycle_dates) >= 7:  # 至少需要7天才能进行有效的实验
                cycles.append({
                    "cycle_number": len(cycles) + 1,
                    "dates": cycle_dates,
                    "start_date": cycle_dates[0],
                    "end_date": cycle_dates[-1],
                    "optimization_dates": cycle_dates[:enhanced_manager.enhanced_config.optimization_phase_days],
                    "validation_dates": cycle_dates[enhanced_manager.enhanced_config.optimization_phase_days:] if len(cycle_dates) > enhanced_manager.enhanced_config.optimization_phase_days else []
                })

        logger.info(f"📊 生成了 {len(cycles)} 个14天周期")

        # 步骤3：执行每个14天周期
        logger.info("步骤3: 开始14天双轨实验周期...")

        for cycle in cycles:
            cycle_num = cycle["cycle_number"]
            logger.info(f"🔄 开始第 {cycle_num} 个14天周期: {cycle['start_date']} 到 {cycle['end_date']}")

            try:
                # 执行14天双轨实验周期
                cycle_result = enhanced_manager.run_enhanced_weekly_cycle(
                    current_date=cycle["start_date"],
                    target_agents=target_agents,
                    daily_results=result["daily_results"]
                )

                result["cycle_results"][f"cycle_{cycle_num}"] = {
                    "cycle_info": cycle,
                    "execution_result": cycle_result,
                    "success": cycle_result.get("status") in ["validation_complete", "cycle_complete"]
                }

                logger.info(f"✅ 第 {cycle_num} 个周期完成: {cycle_result.get('status', 'unknown')}")

                # 如果周期完成，运行迭代Shapley计算
                if cycle_result.get("status") in ["validation_complete", "cycle_complete"]:
                    logger.info(f"🔬 第 {cycle_num} 个周期后运行迭代Shapley计算...")

                    try:
                        # 获取获胜实验数据
                        winning_data = cycle_result.get("winning_experiment_data", {})

                        if winning_data:
                            # 运行迭代Shapley计算
                            shapley_result = enhanced_manager.iterative_calculator.calculate_iterative_shapley(
                                current_week=cycle_num,
                                winning_experiment_data=winning_data,
                                target_agents=target_agents
                            )

                            result["iterative_shapley_results"][f"cycle_{cycle_num}"] = shapley_result
                            logger.info(f"✅ 第 {cycle_num} 个周期的迭代Shapley计算完成")
                        else:
                            logger.warning(f"⚠️  第 {cycle_num} 个周期没有获胜实验数据，跳过迭代Shapley计算")

                    except Exception as e:
                        logger.error(f"❌ 第 {cycle_num} 个周期的迭代Shapley计算失败: {e}")
                        result["iterative_shapley_results"][f"cycle_{cycle_num}"] = {
                            "success": False,
                            "error": str(e)
                        }

            except Exception as e:
                logger.error(f"❌ 第 {cycle_num} 个周期执行失败: {e}")
                result["cycle_results"][f"cycle_{cycle_num}"] = {
                    "cycle_info": cycle,
                    "success": False,
                    "error": str(e)
                }
                result["success"] = False

        # 步骤4：计算整体统计
        logger.info("步骤4: 计算整体交易统计...")
        trading_stats = calculate_enhanced_trading_statistics(result, logger)
        result["trading_statistics"] = trading_stats

        result["total_execution_time"] = time.time() - start_time

        logger.info("=" * 100)
        logger.info("🎉 增强的完整日期范围交易系统执行完成!")
        logger.info(f"📊 总周期数: {len(cycles)}")
        logger.info(f"📊 总交易天数: {len(trading_dates)}")
        logger.info(f"⏱️  总执行时间: {result['total_execution_time']:.2f}秒")
        logger.info("=" * 100)

        return result

    except Exception as e:
        logger.error(f"❌ 增强交易系统执行失败: {e}")
        result["success"] = False
        result["error"] = str(e)
        result["total_execution_time"] = time.time() - start_time
        return result

def calculate_enhanced_trading_statistics(result: Dict[str, Any], logger: logging.Logger) -> Dict[str, Any]:
    """
    计算增强交易系统的统计信息

    参数:
        result: 增强交易系统执行结果
        logger: 日志记录器

    返回:
        增强交易统计信息
    """
    try:
        stats = {
            "total_cycles": len(result.get("cycle_results", {})),
            "successful_cycles": 0,
            "failed_cycles": 0,
            "total_shapley_calculations": len(result.get("iterative_shapley_results", {})),
            "successful_shapley_calculations": 0,
            "failed_shapley_calculations": 0,
            "cycle_performance": {},
            "overall_performance": {
                "total_execution_time": result.get("total_execution_time", 0),
                "average_cycle_time": 0,
                "success_rate": 0
            }
        }

        # 分析周期结果
        for cycle_id, cycle_data in result.get("cycle_results", {}).items():
            if cycle_data.get("success", False):
                stats["successful_cycles"] += 1
            else:
                stats["failed_cycles"] += 1

            # 记录周期性能
            execution_result = cycle_data.get("execution_result", {})
            stats["cycle_performance"][cycle_id] = {
                "status": execution_result.get("status", "unknown"),
                "phase": execution_result.get("current_phase", {}).get("phase_name", "unknown"),
                "day_in_cycle": execution_result.get("current_phase", {}).get("day_in_cycle", 0),
                "success": cycle_data.get("success", False)
            }

        # 分析Shapley计算结果
        for shapley_id, shapley_data in result.get("iterative_shapley_results", {}).items():
            if shapley_data.get("success", False):
                stats["successful_shapley_calculations"] += 1
            else:
                stats["failed_shapley_calculations"] += 1

        # 计算整体性能指标
        if stats["total_cycles"] > 0:
            stats["overall_performance"]["success_rate"] = stats["successful_cycles"] / stats["total_cycles"]
            stats["overall_performance"]["average_cycle_time"] = stats["overall_performance"]["total_execution_time"] / stats["total_cycles"]

        logger.info("📊 增强交易统计计算完成:")
        logger.info(f"  - 总周期数: {stats['total_cycles']}")
        logger.info(f"  - 成功周期: {stats['successful_cycles']}")
        logger.info(f"  - 失败周期: {stats['failed_cycles']}")
        logger.info(f"  - 成功率: {stats['overall_performance']['success_rate']:.2%}")
        logger.info(f"  - Shapley计算: {stats['successful_shapley_calculations']}/{stats['total_shapley_calculations']}")

        return stats

    except Exception as e:
        logger.error(f"❌ 增强交易统计计算失败: {e}")
        return {
            "error": str(e),
            "total_cycles": 0,
            "successful_cycles": 0,
            "failed_cycles": 0
        }

if __name__ == "__main__":
    main()