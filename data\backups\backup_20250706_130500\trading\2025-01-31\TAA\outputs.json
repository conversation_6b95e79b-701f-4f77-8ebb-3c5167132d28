[{"timestamp": "2025-07-05T23:17:49.489802", "output_id": "output_20250705_231749_2ada3b57", "input_id": "input_20250705_231744_5059bca7", "prompt_id": "prompt_20250705_231744_6e006fed", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": {"current_value": 0.05, "signal": "neutral"}}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T23:17:49.488801", "processing_time": 4.531037, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": {"current_value": 0.05, "signal": "neutral"}}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T23:17:49.488801", "processing_time": 4.531037, "llm_used": true}, "processing_time": 4.531037, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 4.531037}}, {"timestamp": "2025-07-05T23:18:34.048430", "output_id": "output_20250705_231834_ee6e75e4", "input_id": "input_20250705_231828_2aeeac9e", "prompt_id": "prompt_20250705_231829_a249096d", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55.5, "trend": "neutral"}, "MACD": {"signal_line": 0.2, "histogram": -0.05, "trend": "neutral"}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 160.0, "current_price": 150.5, "trend": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:34.048430", "processing_time": 5.505764, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55.5, "trend": "neutral"}, "MACD": {"signal_line": 0.2, "histogram": -0.05, "trend": "neutral"}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 160.0, "current_price": 150.5, "trend": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:34.048430", "processing_time": 5.505764, "llm_used": true}, "processing_time": 5.505764, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 10.036801}}, {"timestamp": "2025-07-05T23:18:34.398334", "output_id": "output_20250705_231834_a38beee0", "input_id": "input_20250705_231828_2af7b3fc", "prompt_id": "prompt_20250705_231829_5f2d2959", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 135.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is close to the middle line, indicating a neutral market condition."}, "MACD": {"signal_line": 0, "analysis": "The MACD line is close to the signal line, suggesting no strong bullish or bearish momentum."}, "Moving_Averages": {"50_day_MA": 145.0, "200_day_MA": 160.0, "analysis": "The stock is currently trading below the 50-day moving average but above the 200-day moving average, indicating a slightly bearish short-term trend with a neutral to long-term trend."}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:34.398334", "processing_time": 5.885021, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 135.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is close to the middle line, indicating a neutral market condition."}, "MACD": {"signal_line": 0, "analysis": "The MACD line is close to the signal line, suggesting no strong bullish or bearish momentum."}, "Moving_Averages": {"50_day_MA": 145.0, "200_day_MA": 160.0, "analysis": "The stock is currently trading below the 50-day moving average but above the 200-day moving average, indicating a slightly bearish short-term trend with a neutral to long-term trend."}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:34.398334", "processing_time": 5.885021, "llm_used": true}, "processing_time": 5.885021, "llm_used": true, "confidence": 0.75, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 15.921822}}, {"timestamp": "2025-07-05T23:18:35.618837", "output_id": "output_20250705_231835_c49add57", "input_id": "input_20250705_231828_0913b0be", "prompt_id": "prompt_20250705_231829_c9cfe7ab", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:35.618837", "processing_time": 7.015605, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:35.618837", "processing_time": 7.015605, "llm_used": true}, "processing_time": 7.015605, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 22.937427}}, {"timestamp": "2025-07-05T23:18:37.851440", "output_id": "output_20250705_231837_a2fcc67c", "input_id": "input_20250705_231828_5ee6b12e", "prompt_id": "prompt_20250705_231828_4e8f132d", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:37.851440", "processing_time": 9.395462, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:37.851440", "processing_time": 9.395462, "llm_used": true}, "processing_time": 9.395462, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 32.332889}}, {"timestamp": "2025-07-05T23:18:39.091667", "output_id": "output_20250705_231839_36ad35a0", "input_id": "input_20250705_231834_bc2a859b", "prompt_id": "prompt_20250705_231834_95e22e0e", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "signal": "bullish crossover"}, "moving_averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:39.091667", "processing_time": 4.145887, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "signal": "bullish crossover"}, "moving_averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:39.091667", "processing_time": 4.145887, "llm_used": true}, "processing_time": 4.145887, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 36.478776}}, {"timestamp": "2025-07-05T23:18:39.335857", "output_id": "output_20250705_231839_c465af5a", "input_id": "input_20250705_231828_f82bcd5f", "prompt_id": "prompt_20250705_231828_8099a78e", "raw_response": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 53, "analysis": "RSI is in the middle of the overbought/oversold range, indicating no strong momentum in either direction."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD signal line is slightly positive, but the histogram is negative, suggesting a lack of strong momentum."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 160.0, "analysis": "The stock is currently below both the 50-day and 200-day moving averages, indicating a possible bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:39.335857", "processing_time": 10.94207, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 53, "analysis": "RSI is in the middle of the overbought/oversold range, indicating no strong momentum in either direction."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD signal line is slightly positive, but the histogram is negative, suggesting a lack of strong momentum."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 160.0, "analysis": "The stock is currently below both the 50-day and 200-day moving averages, indicating a possible bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:39.335857", "processing_time": 10.94207, "llm_used": true}, "processing_time": 10.94207, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 47.420846000000004}}, {"timestamp": "2025-07-05T23:18:39.338856", "output_id": "output_20250705_231839_e37f5ca4", "input_id": "input_20250705_231828_6d94d5b5", "prompt_id": "prompt_20250705_231828_ae28e7ba", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.001, "histogram": -0.002, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 152.0, "200-Day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:39.338856", "processing_time": 10.920412, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.001, "histogram": -0.002, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 152.0, "200-Day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:39.338856", "processing_time": 10.920412, "llm_used": true}, "processing_time": 10.920412, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 58.341258}}, {"timestamp": "2025-07-05T23:18:39.438471", "output_id": "output_20250705_231839_a1f5a87f", "input_id": "input_20250705_231829_b1b1dd28", "prompt_id": "prompt_20250705_231829_17b20c89", "raw_response": {"trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 10.2, "histogram": 0.5, "interpretation": "The MACD signal line is above the zero line with a positive histogram, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:39.438471", "processing_time": 10.766381, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 10.2, "histogram": 0.5, "interpretation": "The MACD signal line is above the zero line with a positive histogram, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:39.438471", "processing_time": 10.766381, "llm_used": true}, "processing_time": 10.766381, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 69.107639}}, {"timestamp": "2025-07-05T23:18:40.880555", "output_id": "output_20250705_231840_ea847f77", "input_id": "input_20250705_231828_2796ae4c", "prompt_id": "prompt_20250705_231829_7ab00187", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "Neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "Neutral"}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 150, "interpretation": "50-day MA above 200-day MA, indicating a bullish trend, but close to 50-day MA, suggesting potential reversal."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:40.880555", "processing_time": 12.349835, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "Neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "Neutral"}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 150, "interpretation": "50-day MA above 200-day MA, indicating a bullish trend, but close to 50-day MA, suggesting potential reversal."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:40.880555", "processing_time": 12.349835, "llm_used": true}, "processing_time": 12.349835, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 81.457474}}, {"timestamp": "2025-07-05T23:18:41.138154", "output_id": "output_20250705_231841_508cdad7", "input_id": "input_20250705_231837_6c949d7f", "prompt_id": "prompt_20250705_231837_2be3ad3f", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above long-term MA, indicating bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:41.138154", "processing_time": 3.942613, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above long-term MA, indicating bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:41.138154", "processing_time": 3.942613, "llm_used": true}, "processing_time": 3.942613, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 85.400087}}, {"timestamp": "2025-07-05T23:18:41.223926", "output_id": "output_20250705_231841_d9d38194", "input_id": "input_20250705_231835_ae87b390", "prompt_id": "prompt_20250705_231835_d3cc6eeb", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "The RSI is in the middle of the neutral zone, suggesting that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is close to zero, indicating a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, suggesting a potential long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:41.223926", "processing_time": 6.063141, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "The RSI is in the middle of the neutral zone, suggesting that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is close to zero, indicating a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, suggesting a potential long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:41.223926", "processing_time": 6.063141, "llm_used": true}, "processing_time": 6.063141, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 91.463228}}, {"timestamp": "2025-07-05T23:18:42.182343", "output_id": "output_20250705_231842_5da806c1", "input_id": "input_20250705_231834_6ff5718c", "prompt_id": "prompt_20250705_231835_e333093f", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought but still in an uptrend."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "analysis": "MACD is above the signal line with a positive histogram, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:42.182343", "processing_time": 7.188139, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought but still in an uptrend."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "analysis": "MACD is above the signal line with a positive histogram, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:42.182343", "processing_time": 7.188139, "llm_used": true}, "processing_time": 7.188139, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 98.651367}}, {"timestamp": "2025-07-05T23:18:42.223549", "output_id": "output_20250705_231842_ae36e879", "input_id": "input_20250705_231834_6546b79e", "prompt_id": "prompt_20250705_231834_1069d1aa", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 130.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:42.215535", "processing_time": 7.346859, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 130.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:42.215535", "processing_time": 7.346859, "llm_used": true}, "processing_time": 7.346859, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 105.99822599999999}}, {"timestamp": "2025-07-05T23:18:42.296668", "output_id": "output_20250705_231842_1b64c600", "input_id": "input_20250705_231835_828cf04e", "prompt_id": "prompt_20250705_231835_5136b498", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:42.296668", "processing_time": 7.143414, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:42.296668", "processing_time": 7.143414, "llm_used": true}, "processing_time": 7.143414, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 113.14164}}, {"timestamp": "2025-07-05T23:18:45.429136", "output_id": "output_20250705_231845_b29cdb03", "input_id": "input_20250705_231835_88abe70c", "prompt_id": "prompt_20250705_231835_829935f5", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.2, "analysis": "MACD line is above the signal line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:45.429136", "processing_time": 9.791873, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.2, "analysis": "MACD line is above the signal line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:45.429136", "processing_time": 9.791873, "llm_used": true}, "processing_time": 9.791873, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 122.93351299999999}}, {"timestamp": "2025-07-05T23:18:45.888516", "output_id": "output_20250705_231845_0205577a", "input_id": "input_20250705_231836_fb9c17f0", "prompt_id": "prompt_20250705_231836_b489494b", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.03, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:45.888516", "processing_time": 9.420536, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.03, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:45.888516", "processing_time": 9.420536, "llm_used": true}, "processing_time": 9.420536, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 132.354049}}, {"timestamp": "2025-07-05T23:18:46.649395", "output_id": "output_20250705_231846_e70dd04c", "input_id": "input_20250705_231836_16ee3bf0", "prompt_id": "prompt_20250705_231836_70ec6dd8", "raw_response": {"analysis_date": "2025-01-31", "trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"current_value": 0.02, "analysis": "MACD is close to the signal line, suggesting a continuation of the current trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 135.0, "analysis": "The stock price is above the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:46.649395", "processing_time": 10.420573, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-31", "trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"current_value": 0.02, "analysis": "MACD is close to the signal line, suggesting a continuation of the current trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 135.0, "analysis": "The stock price is above the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:46.649395", "processing_time": 10.420573, "llm_used": true}, "processing_time": 10.420573, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 142.774622}}, {"timestamp": "2025-07-05T23:18:47.288651", "output_id": "output_20250705_231847_a1ed5d3c", "input_id": "input_20250705_231837_b1631a17", "prompt_id": "prompt_20250705_231837_47d00316", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "The RSI is above 70, indicating that the stock may be overbought, but it's close to a neutral zone, suggesting potential for a pullback."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "The MACD signal line is positive and above the zero line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 130.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:47.287654", "processing_time": 9.41358, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "The RSI is above 70, indicating that the stock may be overbought, but it's close to a neutral zone, suggesting potential for a pullback."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "The MACD signal line is positive and above the zero line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 130.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:47.287654", "processing_time": 9.41358, "llm_used": true}, "processing_time": 9.41358, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 152.188202}}, {"timestamp": "2025-07-05T23:18:59.410445", "output_id": "output_20250705_231859_c9d06026", "input_id": "input_20250705_231851_a4f1413f", "prompt_id": "prompt_20250705_231851_f0c71fda", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 50.5, "trend": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": {"bullish": false, "bearish": false}}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:59.410445", "processing_time": 7.672736, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 50.5, "trend": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": {"bullish": false, "bearish": false}}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:18:59.410445", "processing_time": 7.672736, "llm_used": true}, "processing_time": 7.672736, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 159.86093799999998}}, {"timestamp": "2025-07-05T23:19:03.979359", "output_id": "output_20250705_231903_06563efe", "input_id": "input_20250705_231858_e3fc8df5", "prompt_id": "prompt_20250705_231858_4ff36f88", "raw_response": {"analysis_date": "2025-01-31", "trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:03.979359", "processing_time": 5.456656, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-31", "trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:03.979359", "processing_time": 5.456656, "llm_used": true}, "processing_time": 5.456656, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 165.31759399999999}}, {"timestamp": "2025-07-05T23:19:06.272622", "output_id": "output_20250705_231906_438113f7", "input_id": "input_20250705_231851_334ae89a", "prompt_id": "prompt_20250705_231851_802dc92a", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "historical_highs": false, "signal": "bullish"}, "Moving_Averages": {"50_day_moving_average": 140.0, "200_day_moving_average": 120.0, "crossover": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:06.272622", "processing_time": 14.436354, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "historical_highs": false, "signal": "bullish"}, "Moving_Averages": {"50_day_moving_average": 140.0, "200_day_moving_average": 120.0, "crossover": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:06.272622", "processing_time": 14.436354, "llm_used": true}, "processing_time": 14.436354, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 179.75394799999998}}, {"timestamp": "2025-07-05T23:19:06.804855", "output_id": "output_20250705_231906_352c2e0e", "input_id": "input_20250705_231901_bddd4a3c", "prompt_id": "prompt_20250705_231901_98b0787c", "raw_response": {"trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 67, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:06.788569", "processing_time": 5.103515, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 67, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:06.788569", "processing_time": 5.103515, "llm_used": true}, "processing_time": 5.103515, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 184.85746299999997}}, {"timestamp": "2025-07-05T23:19:10.002896", "output_id": "output_20250705_231910_a5811dcf", "input_id": "input_20250705_231902_6152e129", "prompt_id": "prompt_20250705_231902_a97c0a38", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:10.002896", "processing_time": 7.4305, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:10.002896", "processing_time": 7.4305, "llm_used": true}, "processing_time": 7.4305, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 192.28796299999996}}, {"timestamp": "2025-07-05T23:19:10.771699", "output_id": "output_20250705_231910_b5b9b457", "input_id": "input_20250705_231905_8a89a3d9", "prompt_id": "prompt_20250705_231905_6eceea62", "raw_response": {"trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.05, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:10.771699", "processing_time": 4.929058, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.05, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:10.771699", "processing_time": 4.929058, "llm_used": true}, "processing_time": 4.929058, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 197.21702099999996}}, {"timestamp": "2025-07-05T23:19:11.011804", "output_id": "output_20250705_231911_690100c6", "input_id": "input_20250705_231904_d7b1fb78", "prompt_id": "prompt_20250705_231904_024d0b75", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "The RSI is at a neutral level, indicating that the stock may be neither overbought nor oversold."}, "MACD": {"current_value": "0.00", "analysis": "The MACD line is close to the signal line, suggesting a lack of momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 165.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, which may indicate a bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:11.011804", "processing_time": 6.684081, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "The RSI is at a neutral level, indicating that the stock may be neither overbought nor oversold."}, "MACD": {"current_value": "0.00", "analysis": "The MACD line is close to the signal line, suggesting a lack of momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 165.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, which may indicate a bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:11.011804", "processing_time": 6.684081, "llm_used": true}, "processing_time": 6.684081, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 203.90110199999995}}, {"timestamp": "2025-07-05T23:19:13.419504", "output_id": "output_20250705_231913_86f3ab85", "input_id": "input_20250705_231902_be0be3e5", "prompt_id": "prompt_20250705_231902_3118689a", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "analysis": "The RSI is currently in the neutral zone, suggesting no strong momentum in either direction."}, "MACD": {"signal_line": 0.01, "analysis": "The MACD signal line is close to zero, indicating a lack of clear direction in the short term."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "The stock is currently trading slightly below its 50-day moving average but above its 200-day moving average, suggesting a long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:13.418833", "processing_time": 11.353906, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "analysis": "The RSI is currently in the neutral zone, suggesting no strong momentum in either direction."}, "MACD": {"signal_line": 0.01, "analysis": "The MACD signal line is close to zero, indicating a lack of clear direction in the short term."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "The stock is currently trading slightly below its 50-day moving average but above its 200-day moving average, suggesting a long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:13.418833", "processing_time": 11.353906, "llm_used": true}, "processing_time": 11.353906, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 215.25500799999995}}, {"timestamp": "2025-07-05T23:19:14.326020", "output_id": "output_20250705_231914_b9f6859d", "input_id": "input_20250705_231906_d9e8418e", "prompt_id": "prompt_20250705_231906_1a880449", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "The MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:14.326020", "processing_time": 7.743663, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "The MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:14.326020", "processing_time": 7.743663, "llm_used": true}, "processing_time": 7.743663, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 222.99867099999994}}, {"timestamp": "2025-07-05T23:19:15.353166", "output_id": "output_20250705_231915_711736f2", "input_id": "input_20250705_231908_8b8e1904", "prompt_id": "prompt_20250705_231908_f0df7908", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:15.353166", "processing_time": 6.843931, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:15.353166", "processing_time": 6.843931, "llm_used": true}, "processing_time": 6.843931, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 229.84260199999994}}, {"timestamp": "2025-07-05T23:19:15.802867", "output_id": "output_20250705_231915_e8d88b6d", "input_id": "input_20250705_231908_7f9c58b6", "prompt_id": "prompt_20250705_231908_7c901fc6", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "interpretation": "Indicates a neutral market condition."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "interpretation": "MACD line is close to the signal line with a slight bearish bias."}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "interpretation": "The stock is currently below its 50-day and 200-day moving averages, suggesting a bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:15.751728", "processing_time": 7.373079, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "interpretation": "Indicates a neutral market condition."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "interpretation": "MACD line is close to the signal line with a slight bearish bias."}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "interpretation": "The stock is currently below its 50-day and 200-day moving averages, suggesting a bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:15.751728", "processing_time": 7.373079, "llm_used": true}, "processing_time": 7.373079, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 237.21568099999993}}, {"timestamp": "2025-07-05T23:19:15.807869", "output_id": "output_20250705_231915_5146dd3c", "input_id": "input_20250705_231910_00c3477d", "prompt_id": "prompt_20250705_231910_f379f5ce", "raw_response": {"analysis_date": "2025-01-31", "trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is in the middle to upper part of the range, indicating the stock is overbought but not excessively so."}, "MACD": {"value": 0.01, "analysis": "The MACD line is slightly above the signal line, suggesting a bullish trend."}, "Moving Averages": {"50-Day MA": 155.0, "200-Day MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:15.779282", "processing_time": 5.273594, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-31", "trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is in the middle to upper part of the range, indicating the stock is overbought but not excessively so."}, "MACD": {"value": 0.01, "analysis": "The MACD line is slightly above the signal line, suggesting a bullish trend."}, "Moving Averages": {"50-Day MA": 155.0, "200-Day MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:15.779282", "processing_time": 5.273594, "llm_used": true}, "processing_time": 5.273594, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 242.48927499999994}}, {"timestamp": "2025-07-05T23:19:19.273304", "output_id": "output_20250705_231919_26629e37", "input_id": "input_20250705_231913_d7cb543d", "prompt_id": "prompt_20250705_231913_fc3e41d1", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "indicating a neutral market condition"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "MACD line is close to the signal line, suggesting no strong trend"}, "Moving_Average": {"50_day_MA": 155, "200_day_MA": 145, "interpretation": "50-day MA close to current price, 200-day MA below current price, suggesting a short-term bullish but long-term bearish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:19.273304", "processing_time": 5.95157, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "indicating a neutral market condition"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "MACD line is close to the signal line, suggesting no strong trend"}, "Moving_Average": {"50_day_MA": 155, "200_day_MA": 145, "interpretation": "50-day MA close to current price, 200-day MA below current price, suggesting a short-term bullish but long-term bearish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:19.273304", "processing_time": 5.95157, "llm_used": true}, "processing_time": 5.95157, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 248.44084499999994}}, {"timestamp": "2025-07-05T23:19:19.801797", "output_id": "output_20250705_231919_1d0479f9", "input_id": "input_20250705_231911_4b630bb9", "prompt_id": "prompt_20250705_231911_e35e50b1", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "trend": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.03, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "current_price": 155.5, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:19.749535", "processing_time": 8.421117, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "trend": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.03, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "current_price": 155.5, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T23:19:19.749535", "processing_time": 8.421117, "llm_used": true}, "processing_time": 8.421117, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 256.86196199999995}}]