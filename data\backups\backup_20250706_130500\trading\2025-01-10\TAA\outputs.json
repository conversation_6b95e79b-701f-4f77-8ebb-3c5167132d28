[{"timestamp": "2025-07-05T22:27:28.486062", "output_id": "output_20250705_222728_8417bf3f", "input_id": "input_20250705_222724_7d399108", "prompt_id": "prompt_20250705_222725_511d566b", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:28.485061", "processing_time": 3.810833, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:28.485061", "processing_time": 3.810833, "llm_used": true}, "processing_time": 3.810833, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 10.200823}}, {"timestamp": "2025-07-05T22:27:29.925622", "output_id": "output_20250705_222729_43b283c2", "input_id": "input_20250705_222724_abf65de9", "prompt_id": "prompt_20250705_222725_03e1d276", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 50, indicating a bullish trend."}, "MACD": {"current_value": 0.5, "comment": "MACD is above the signal line, suggesting bullish momentum."}, "Moving Averages": {"50-day_MA": 152.0, "200-day_MA": 145.0, "comment": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:29.924621", "processing_time": 5.356501, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 50, indicating a bullish trend."}, "MACD": {"current_value": 0.5, "comment": "MACD is above the signal line, suggesting bullish momentum."}, "Moving Averages": {"50-day_MA": 152.0, "200-day_MA": 145.0, "comment": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:29.924621", "processing_time": 5.356501, "llm_used": true}, "processing_time": 5.356501, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 15.557324}}, {"timestamp": "2025-07-05T22:27:29.965034", "output_id": "output_20250705_222729_417ee6bd", "input_id": "input_20250705_222724_d38be688", "prompt_id": "prompt_20250705_222724_3bb3bb86", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 55, "interpretation": "Indicates a neutral market condition."}, "MACD": {"signal_line": 0.0, "histogram": 0.02, "interpretation": "Signal line crossing the zero line, suggesting a neutral trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 150.0, "interpretation": "The stock is currently between the 50-day and 200-day moving averages, indicating a possible consolidation phase."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:29.965034", "processing_time": 5.483237, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 55, "interpretation": "Indicates a neutral market condition."}, "MACD": {"signal_line": 0.0, "histogram": 0.02, "interpretation": "Signal line crossing the zero line, suggesting a neutral trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 150.0, "interpretation": "The stock is currently between the 50-day and 200-day moving averages, indicating a possible consolidation phase."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:29.965034", "processing_time": 5.483237, "llm_used": true}, "processing_time": 5.483237, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 21.040561}}, {"timestamp": "2025-07-05T22:27:30.185928", "output_id": "output_20250705_222730_3501464f", "input_id": "input_20250705_222724_454e237d", "prompt_id": "prompt_20250705_222724_f14053fa", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 57, "analysis": "RSI is currently neutral, indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "The MACD line is slightly above the signal line with a small positive histogram, suggesting a slightly bullish trend but no strong momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently above its 50-day moving average but below its 200-day moving average, suggesting a short-term bullish trend with a longer-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:30.185928", "processing_time": 5.779392, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 57, "analysis": "RSI is currently neutral, indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "The MACD line is slightly above the signal line with a small positive histogram, suggesting a slightly bullish trend but no strong momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently above its 50-day moving average but below its 200-day moving average, suggesting a short-term bullish trend with a longer-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:30.185928", "processing_time": 5.779392, "llm_used": true}, "processing_time": 5.779392, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 26.819952999999998}}, {"timestamp": "2025-07-05T22:27:31.847412", "output_id": "output_20250705_222731_bc98489f", "input_id": "input_20250705_222724_5cc3bcda", "prompt_id": "prompt_20250705_222725_98ac7251", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56.7, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:31.847412", "processing_time": 7.243269, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56.7, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:31.847412", "processing_time": 7.243269, "llm_used": true}, "processing_time": 7.243269, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 34.063221999999996}}, {"timestamp": "2025-07-05T22:27:32.038960", "output_id": "output_20250705_222732_fbdebbcf", "input_id": "input_20250705_222724_e76d6ef8", "prompt_id": "prompt_20250705_222724_dbbbfed2", "raw_response": {"trend": "neutral", "technical_score": -0.2, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 145.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 58, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.1, "interpretation": "Neutral"}, "Moving_Averages": {"50_MA": 147.5, "200_MA": 165.0, "interpretation": "Long-term bullish, short-term neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:32.038960", "processing_time": 7.509982, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.2, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 145.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 58, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.1, "interpretation": "Neutral"}, "Moving_Averages": {"50_MA": 147.5, "200_MA": 165.0, "interpretation": "Long-term bullish, short-term neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:32.038960", "processing_time": 7.509982, "llm_used": true}, "processing_time": 7.509982, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 41.573204}}, {"timestamp": "2025-07-05T22:27:34.961762", "output_id": "output_20250705_222734_cd0632b4", "input_id": "input_20250705_222724_65d3f35d", "prompt_id": "prompt_20250705_222725_d081b528", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "analysis": "MACD line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, indicating a possible downward trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:34.961762", "processing_time": 10.414251, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "analysis": "MACD line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, indicating a possible downward trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:34.961762", "processing_time": 10.414251, "llm_used": true}, "processing_time": 10.414251, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 51.987455}}, {"timestamp": "2025-07-05T22:27:35.208935", "output_id": "output_20250705_222735_c69f63a4", "input_id": "input_20250705_222729_824a767e", "prompt_id": "prompt_20250705_222729_5abc891b", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:35.208935", "processing_time": 5.760832, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:35.208935", "processing_time": 5.760832, "llm_used": true}, "processing_time": 5.760832, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 57.748287}}, {"timestamp": "2025-07-05T22:27:35.483061", "output_id": "output_20250705_222735_c4c28234", "input_id": "input_20250705_222730_00896bb8", "prompt_id": "prompt_20250705_222730_9ea60520", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating the stock may be overbought but remains in a strong bullish trend."}, "MACD": {"current_value": "positive", "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving Averages": {"50-Day MA": 152.0, "200-Day MA": 145.0, "analysis": "The stock is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:35.483061", "processing_time": 5.281609, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating the stock may be overbought but remains in a strong bullish trend."}, "MACD": {"current_value": "positive", "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving Averages": {"50-Day MA": 152.0, "200-Day MA": 145.0, "analysis": "The stock is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:35.483061", "processing_time": 5.281609, "llm_used": true}, "processing_time": 5.281609, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 63.029896}}, {"timestamp": "2025-07-05T22:27:35.497781", "output_id": "output_20250705_222735_29f328b4", "input_id": "input_20250705_222731_b4c1bca3", "prompt_id": "prompt_20250705_222731_7d19fcd5", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:35.492771", "processing_time": 4.350907, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:35.492771", "processing_time": 4.350907, "llm_used": true}, "processing_time": 4.350907, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 67.380803}}, {"timestamp": "2025-07-05T22:27:37.614145", "output_id": "output_20250705_222737_5bf56550", "input_id": "input_20250705_222732_ff8001f5", "prompt_id": "prompt_20250705_222732_da5edd89", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:37.614145", "processing_time": 4.860416, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:37.614145", "processing_time": 4.860416, "llm_used": true}, "processing_time": 4.860416, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 72.241219}}, {"timestamp": "2025-07-05T22:27:38.376937", "output_id": "output_20250705_222738_1bb8d0e7", "input_id": "input_20250705_222733_1585e3dd", "prompt_id": "prompt_20250705_222733_ae5cfdf1", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought, suggesting a potential pullback"}, "MACD": {"signal_line": 0.2, "histogram": 0.05, "interpretation": "Positive crossover and rising histogram indicates bullish momentum"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "Price above both 50-day and 200-day moving averages, indicating long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:38.376937", "processing_time": 5.15696, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought, suggesting a potential pullback"}, "MACD": {"signal_line": 0.2, "histogram": 0.05, "interpretation": "Positive crossover and rising histogram indicates bullish momentum"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "Price above both 50-day and 200-day moving averages, indicating long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:38.376937", "processing_time": 5.15696, "llm_used": true}, "processing_time": 5.15696, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 77.398179}}, {"timestamp": "2025-07-05T22:27:39.099996", "output_id": "output_20250705_222739_5e60cdba", "input_id": "input_20250705_222724_f8c86498", "prompt_id": "prompt_20250705_222724_cf813c59", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "interpretation": "slightly oversold"}, "MACD": {"signal_line": 0.05, "histogram": -0.03, "interpretation": "indicating a possible trend reversal"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 170.0, "interpretation": "50-day MA above 200-day MA, indicating a long-term bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:39.099996", "processing_time": 14.561969, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "interpretation": "slightly oversold"}, "MACD": {"signal_line": 0.05, "histogram": -0.03, "interpretation": "indicating a possible trend reversal"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 170.0, "interpretation": "50-day MA above 200-day MA, indicating a long-term bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:39.099996", "processing_time": 14.561969, "llm_used": true}, "processing_time": 14.561969, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 91.960148}}, {"timestamp": "2025-07-05T22:27:40.206364", "output_id": "output_20250705_222740_e1aaedfc", "input_id": "input_20250705_222730_76474f86", "prompt_id": "prompt_20250705_222730_35b85195", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 50, indicating that the stock is in a bullish momentum."}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "comment": "MACD histogram is positive, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "comment": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:40.206364", "processing_time": 9.368053, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 50, indicating that the stock is in a bullish momentum."}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "comment": "MACD histogram is positive, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "comment": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:40.206364", "processing_time": 9.368053, "llm_used": true}, "processing_time": 9.368053, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 101.328201}}, {"timestamp": "2025-07-05T22:27:41.239080", "output_id": "output_20250705_222741_a07f1eee", "input_id": "input_20250705_222735_84939074", "prompt_id": "prompt_20250705_222735_586a4a57", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 70, "analysis": "RSI is above 70, indicating that the stock may be overbought and could be due for a pullback."}, "MACD": {"value": "positive", "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Average": {"short_term": {"value": 151.0, "analysis": "The short-term moving average is rising, supporting the bullish trend."}, "long_term": {"value": 145.0, "analysis": "The long-term moving average is also rising, indicating a strong bullish trend."}}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:41.239080", "processing_time": 5.507297, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 70, "analysis": "RSI is above 70, indicating that the stock may be overbought and could be due for a pullback."}, "MACD": {"value": "positive", "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Average": {"short_term": {"value": 151.0, "analysis": "The short-term moving average is rising, supporting the bullish trend."}, "long_term": {"value": 145.0, "analysis": "The long-term moving average is also rising, indicating a strong bullish trend."}}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:41.239080", "processing_time": 5.507297, "llm_used": true}, "processing_time": 5.507297, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 106.835498}}, {"timestamp": "2025-07-05T22:27:41.369612", "output_id": "output_20250705_222741_8f1e11d1", "input_id": "input_20250705_222730_341bc24a", "prompt_id": "prompt_20250705_222730_6978de62", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:41.369612", "processing_time": 10.630914, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:41.369612", "processing_time": 10.630914, "llm_used": true}, "processing_time": 10.630914, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 117.466412}}, {"timestamp": "2025-07-05T22:27:41.517121", "output_id": "output_20250705_222741_f1c0dad7", "input_id": "input_20250705_222729_d35540fd", "prompt_id": "prompt_20250705_222729_23c827af", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:41.516123", "processing_time": 12.120893, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:41.516123", "processing_time": 12.120893, "llm_used": true}, "processing_time": 12.120893, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 129.58730500000001}}, {"timestamp": "2025-07-05T22:27:47.078470", "output_id": "output_20250705_222747_343f9ef6", "input_id": "input_20250705_222741_94a574bc", "prompt_id": "prompt_20250705_222741_23dabd21", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 153.0, "200_day_MA": 148.0, "signal": "above MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:47.078470", "processing_time": 5.305384, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 153.0, "200_day_MA": 148.0, "signal": "above MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:47.078470", "processing_time": 5.305384, "llm_used": true}, "processing_time": 5.305384, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 134.89268900000002}}, {"timestamp": "2025-07-05T22:27:48.462890", "output_id": "output_20250705_222748_3b19e4a0", "input_id": "input_20250705_222731_cd2063d1", "prompt_id": "prompt_20250705_222731_89eb9d7c", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"value": 67, "analysis": "The RSI is above 70, indicating that the stock is overbought and may be due for a pullback."}, "MACD": {"signal_line": 0.1, "analysis": "The MACD line is above the signal line and shows upward momentum, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 120.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:48.462890", "processing_time": 17.055492, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"value": 67, "analysis": "The RSI is above 70, indicating that the stock is overbought and may be due for a pullback."}, "MACD": {"signal_line": 0.1, "analysis": "The MACD line is above the signal line and shows upward momentum, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 140.0, "200_day_MA": 120.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:48.462890", "processing_time": 17.055492, "llm_used": true}, "processing_time": 17.055492, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 151.94818100000003}}, {"timestamp": "2025-07-05T22:27:54.117718", "output_id": "output_20250705_222754_e54a8a87", "input_id": "input_20250705_222748_175aba1e", "prompt_id": "prompt_20250705_222748_239f104f", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:54.117718", "processing_time": 5.905049, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:54.117718", "processing_time": 5.905049, "llm_used": true}, "processing_time": 5.905049, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 157.85323000000002}}, {"timestamp": "2025-07-05T22:27:55.878728", "output_id": "output_20250705_222755_b9cc72c1", "input_id": "input_20250705_222749_02ce2831", "prompt_id": "prompt_20250705_222749_483beb06", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 152.0, "200-Day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:55.878728", "processing_time": 6.794789, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 152.0, "200-Day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:55.878728", "processing_time": 6.794789, "llm_used": true}, "processing_time": 6.794789, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 164.64801900000003}}, {"timestamp": "2025-07-05T22:27:56.483793", "output_id": "output_20250705_222756_3db2db44", "input_id": "input_20250705_222745_849fccc2", "prompt_id": "prompt_20250705_222745_378ed885", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:56.483793", "processing_time": 10.963345, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:56.483793", "processing_time": 10.963345, "llm_used": true}, "processing_time": 10.963345, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 175.61136400000004}}, {"timestamp": "2025-07-05T22:27:56.638958", "output_id": "output_20250705_222756_d042dc52", "input_id": "input_20250705_222752_ea85d6ae", "prompt_id": "prompt_20250705_222752_9970eab8", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:56.638958", "processing_time": 4.30444, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:27:56.638958", "processing_time": 4.30444, "llm_used": true}, "processing_time": 4.30444, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 179.91580400000004}}, {"timestamp": "2025-07-05T22:28:00.974187", "output_id": "output_20250705_222800_c0febac5", "input_id": "input_20250705_222751_711ab2ac", "prompt_id": "prompt_20250705_222751_a9467f00", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.4, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 56, "interpretation": "slightly above neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "indicating slight bullish momentum"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "interpretation": "short-term moving average is above long-term moving average, indicating potential upward trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:00.973183", "processing_time": 9.354346, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.4, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 56, "interpretation": "slightly above neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "indicating slight bullish momentum"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "interpretation": "short-term moving average is above long-term moving average, indicating potential upward trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:00.973183", "processing_time": 9.354346, "llm_used": true}, "processing_time": 9.354346, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 189.27015000000003}}, {"timestamp": "2025-07-05T22:28:01.473318", "output_id": "output_20250705_222801_db2de578", "input_id": "input_20250705_222756_465e3438", "prompt_id": "prompt_20250705_222756_6838aaf2", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "interpretation": "indicating a neutral market condition"}, "MACD": {"signal_line": 0.1, "histogram": -0.3, "interpretation": "suggesting a weak bullish trend"}, "Moving_Average": {"50_day_MA": 153.0, "200_day_MA": 148.0, "interpretation": "50-day MA crossed above 200-day MA, indicating a bullish trend"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:01.473318", "processing_time": 4.637527, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "interpretation": "indicating a neutral market condition"}, "MACD": {"signal_line": 0.1, "histogram": -0.3, "interpretation": "suggesting a weak bullish trend"}, "Moving_Average": {"50_day_MA": 153.0, "200_day_MA": 148.0, "interpretation": "50-day MA crossed above 200-day MA, indicating a bullish trend"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:01.473318", "processing_time": 4.637527, "llm_used": true}, "processing_time": 4.637527, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 193.90767700000004}}, {"timestamp": "2025-07-05T22:28:03.819789", "output_id": "output_20250705_222803_a32eb7b6", "input_id": "input_20250705_222759_2e1b39da", "prompt_id": "prompt_20250705_222759_33223f74", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 60, indicating that the stock is in an uptrend and may continue to rise."}, "MACD": {"current_value": "positive", "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving Averages": {"50-Day MA": {"current_value": 152.0, "analysis": "The 50-day moving average is rising, supporting the bullish trend."}, "200-Day MA": {"current_value": 145.0, "analysis": "The 200-day moving average is also rising, indicating a long-term bullish trend."}}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:03.819789", "processing_time": 4.741673, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 60, indicating that the stock is in an uptrend and may continue to rise."}, "MACD": {"current_value": "positive", "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving Averages": {"50-Day MA": {"current_value": 152.0, "analysis": "The 50-day moving average is rising, supporting the bullish trend."}, "200-Day MA": {"current_value": 145.0, "analysis": "The 200-day moving average is also rising, indicating a long-term bullish trend."}}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:03.819789", "processing_time": 4.741673, "llm_used": true}, "processing_time": 4.741673, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 198.64935000000003}}, {"timestamp": "2025-07-05T22:28:06.084186", "output_id": "output_20250705_222806_0fd0173d", "input_id": "input_20250705_222758_5bdaa1d8", "prompt_id": "prompt_20250705_222758_c45c9e4c", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:06.084186", "processing_time": 7.941671, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:06.084186", "processing_time": 7.941671, "llm_used": true}, "processing_time": 7.941671, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 206.59102100000004}}, {"timestamp": "2025-07-05T22:28:10.901245", "output_id": "output_20250705_222810_0ade668e", "input_id": "input_20250705_222756_c0667fe3", "prompt_id": "prompt_20250705_222756_1ea7f327", "raw_response": {"analysis_date": "2025-01-10", "trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "overbought_threshold": 70, "overbought": true, "interpretation": "The RSI is in the overbought territory, suggesting a possible pullback, but the overall trend remains bullish."}, "MACD": {"signal_line": 0.0, "macd_line": 0.1, "histogram": 0.05, "interpretation": "The MACD is bullish with the MACD line above the signal line and a positive histogram, indicating potential upward momentum."}, "Moving_Average": {"50_day_ma": 155.0, "200_day_ma": 145.0, "interpretation": "The stock is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:10.901245", "processing_time": 14.561228, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-10", "trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "overbought_threshold": 70, "overbought": true, "interpretation": "The RSI is in the overbought territory, suggesting a possible pullback, but the overall trend remains bullish."}, "MACD": {"signal_line": 0.0, "macd_line": 0.1, "histogram": 0.05, "interpretation": "The MACD is bullish with the MACD line above the signal line and a positive histogram, indicating potential upward momentum."}, "Moving_Average": {"50_day_ma": 155.0, "200_day_ma": 145.0, "interpretation": "The stock is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:10.901245", "processing_time": 14.561228, "llm_used": true}, "processing_time": 14.561228, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 221.15224900000004}}, {"timestamp": "2025-07-05T22:28:10.994532", "output_id": "output_20250705_222810_deb16901", "input_id": "input_20250705_222806_ad67320e", "prompt_id": "prompt_20250705_222806_d337ad3e", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "neutral"}, "MACD": {"signal_line": 0.02, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:10.994532", "processing_time": 4.300885, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "neutral"}, "MACD": {"signal_line": 0.02, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:10.994532", "processing_time": 4.300885, "llm_used": true}, "processing_time": 4.300885, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 225.45313400000003}}, {"timestamp": "2025-07-05T22:28:11.577600", "output_id": "output_20250705_222811_551e257c", "input_id": "input_20250705_222802_855415e5", "prompt_id": "prompt_20250705_222802_90982018", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is close to the signal line, suggesting a lack of strong trend momentum."}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 148.0, "analysis": "The stock is currently trading above the 50-day moving average but below the 200-day moving average, indicating a potential bullish trend but with caution."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:11.577600", "processing_time": 9.261814, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is close to the signal line, suggesting a lack of strong trend momentum."}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 148.0, "analysis": "The stock is currently trading above the 50-day moving average but below the 200-day moving average, indicating a potential bullish trend but with caution."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:11.577600", "processing_time": 9.261814, "llm_used": true}, "processing_time": 9.261814, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 234.71494800000002}}, {"timestamp": "2025-07-05T22:28:12.603762", "output_id": "output_20250705_222812_2834e4ef", "input_id": "input_20250705_222804_5118669a", "prompt_id": "prompt_20250705_222804_ecb48069", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 54, "comment": "RSI is neutral, indicating no overbought or oversold conditions."}, "MACD": {"signal_line": 0.03, "histogram": 0.01, "comment": "MACD signal line is slightly above the zero line, suggesting a slight bullish trend but no strong momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "The stock is currently above the 50-day moving average but below the 200-day moving average, suggesting a short-term bullish trend with a long-term neutral trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:12.603762", "processing_time": 8.528325, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 54, "comment": "RSI is neutral, indicating no overbought or oversold conditions."}, "MACD": {"signal_line": 0.03, "histogram": 0.01, "comment": "MACD signal line is slightly above the zero line, suggesting a slight bullish trend but no strong momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "The stock is currently above the 50-day moving average but below the 200-day moving average, suggesting a short-term bullish trend with a long-term neutral trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:12.603762", "processing_time": 8.528325, "llm_used": true}, "processing_time": 8.528325, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 243.24327300000002}}, {"timestamp": "2025-07-05T22:28:13.140072", "output_id": "output_20250705_222813_f99a0223", "input_id": "input_20250705_222806_9fd4579b", "prompt_id": "prompt_20250705_222806_2f643470", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line_cross": "above", "historical_line_cross": "above", "signal": "bullish"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "current_price_above_50_day_MA": true, "current_price_above_200_day_MA": true}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:13.123015", "processing_time": 6.188723, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line_cross": "above", "historical_line_cross": "above", "signal": "bullish"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "current_price_above_50_day_MA": true, "current_price_above_200_day_MA": true}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:28:13.123015", "processing_time": 6.188723, "llm_used": true}, "processing_time": 6.188723, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 249.43199600000003}}, {"timestamp": "2025-07-05T22:29:17.560095", "output_id": "output_20250705_222917_ba78bba7", "input_id": "input_20250705_222909_9e18ded2", "prompt_id": "prompt_20250705_222910_4fbbbdfd", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is above 50, indicating that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0.01, "analysis": "The MACD line is close to the signal line, suggesting a lack of strong trend momentum."}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently trading above its 50-day MA but below its 200-day MA, indicating a short-term bullish trend but a long-term neutral trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:29:17.558586", "processing_time": 7.592801, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is above 50, indicating that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0.01, "analysis": "The MACD line is close to the signal line, suggesting a lack of strong trend momentum."}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently trading above its 50-day MA but below its 200-day MA, indicating a short-term bullish trend but a long-term neutral trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:29:17.558586", "processing_time": 7.592801, "llm_used": true}, "processing_time": 7.592801, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 7.592801}}, {"timestamp": "2025-07-05T22:29:59.802753", "output_id": "output_20250705_222959_0ed03a6e", "input_id": "input_20250705_222954_b0b2913c", "prompt_id": "prompt_20250705_222954_91c500c3", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:29:59.802753", "processing_time": 5.941135, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:29:59.802753", "processing_time": 5.941135, "llm_used": true}, "processing_time": 5.941135, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 13.533936}}, {"timestamp": "2025-07-05T22:30:01.154716", "output_id": "output_20250705_223001_5e2c71bf", "input_id": "input_20250705_222954_7890563e", "prompt_id": "prompt_20250705_222954_fe8491ff", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "interpretation": "Neutral - No strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "The MACD line is just above the signal line with a negative histogram, indicating a weak bearish trend."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 170.0, "interpretation": "The stock is below both the 50-day and 200-day moving averages, suggesting a potential bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:01.154716", "processing_time": 7.248699, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "interpretation": "Neutral - No strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "The MACD line is just above the signal line with a negative histogram, indicating a weak bearish trend."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 170.0, "interpretation": "The stock is below both the 50-day and 200-day moving averages, suggesting a potential bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:01.154716", "processing_time": 7.248699, "llm_used": true}, "processing_time": 7.248699, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 20.782635}}, {"timestamp": "2025-07-05T22:30:02.528882", "output_id": "output_20250705_223002_64c2eef0", "input_id": "input_20250705_222954_3ac2a898", "prompt_id": "prompt_20250705_222955_8a7e4fa2", "raw_response": {"trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "trend": "neutral"}, "MACD": {"current_value": -0.02, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 160.0, "crossover": "50_day_MA above 200_day_MA"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:02.528882", "processing_time": 8.358942, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "trend": "neutral"}, "MACD": {"current_value": -0.02, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 160.0, "crossover": "50_day_MA above 200_day_MA"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:02.528882", "processing_time": 8.358942, "llm_used": true}, "processing_time": 8.358942, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 29.141576999999998}}, {"timestamp": "2025-07-05T22:30:02.727253", "output_id": "output_20250705_223002_d8f6846b", "input_id": "input_20250705_222954_6bfc6b74", "prompt_id": "prompt_20250705_222955_5c5680aa", "raw_response": {"trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "The RSI is currently in the middle of the overbought/oversold zone, indicating a neutral trend."}, "MACD": {"signal_line": -0.1, "analysis": "The MACD signal line is slightly below the zero line, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "The stock is currently trading below its 50-day and 200-day moving averages, which could indicate a bearish trend. However, the 50-day MA is closer, suggesting a possible short-term bounce."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:02.727253", "processing_time": 8.739483, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "The RSI is currently in the middle of the overbought/oversold zone, indicating a neutral trend."}, "MACD": {"signal_line": -0.1, "analysis": "The MACD signal line is slightly below the zero line, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "The stock is currently trading below its 50-day and 200-day moving averages, which could indicate a bearish trend. However, the 50-day MA is closer, suggesting a possible short-term bounce."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:02.727253", "processing_time": 8.739483, "llm_used": true}, "processing_time": 8.739483, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 37.88106}}, {"timestamp": "2025-07-05T22:30:03.275945", "output_id": "output_20250705_223003_70965e06", "input_id": "input_20250705_222958_9167ed20", "prompt_id": "prompt_20250705_222958_8f3d8659", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:03.275945", "processing_time": 4.381589, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:03.275945", "processing_time": 4.381589, "llm_used": true}, "processing_time": 4.381589, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 42.262648999999996}}, {"timestamp": "2025-07-05T22:30:05.014138", "output_id": "output_20250705_223005_b2ec9943", "input_id": "input_20250705_222954_4f8777d7", "prompt_id": "prompt_20250705_222954_15ec0e8b", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60.2, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "interpretation": "Slight bullish trend"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "interpretation": "Short-term above long-term MA, suggesting slight bullishness"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:05.014138", "processing_time": 11.093046, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60.2, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "interpretation": "Slight bullish trend"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "interpretation": "Short-term above long-term MA, suggesting slight bullishness"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:05.014138", "processing_time": 11.093046, "llm_used": true}, "processing_time": 11.093046, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 53.355695}}, {"timestamp": "2025-07-05T22:30:05.427401", "output_id": "output_20250705_223005_266e57d6", "input_id": "input_20250705_222954_a4217628", "prompt_id": "prompt_20250705_222955_13fffb35", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.03, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:05.427401", "processing_time": 11.359556, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.03, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:05.427401", "processing_time": 11.359556, "llm_used": true}, "processing_time": 11.359556, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 64.715251}}, {"timestamp": "2025-07-05T22:30:07.137516", "output_id": "output_20250705_223007_0e48e33e", "input_id": "input_20250705_222958_13868a8d", "prompt_id": "prompt_20250705_222958_0cb5c167", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.05, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:07.137516", "processing_time": 8.302348, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.05, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:07.137516", "processing_time": 8.302348, "llm_used": true}, "processing_time": 8.302348, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 73.01759899999999}}, {"timestamp": "2025-07-05T22:30:08.652238", "output_id": "output_20250705_223008_7b34bbc9", "input_id": "input_20250705_223001_a6b4c7c6", "prompt_id": "prompt_20250705_223001_5d01eb13", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought, suggesting a possible pullback"}, "MACD": {"signal_line": 20, "histogram": 10, "interpretation": "MACD line crossing above signal line, indicating bullish momentum"}, "Moving_Average": {"50_day_MA": 155, "200_day_MA": 145, "interpretation": "Price above both 50-day and 200-day moving averages, strong bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:08.652238", "processing_time": 7.603741, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought, suggesting a possible pullback"}, "MACD": {"signal_line": 20, "histogram": 10, "interpretation": "MACD line crossing above signal line, indicating bullish momentum"}, "Moving_Average": {"50_day_MA": 155, "200_day_MA": 145, "interpretation": "Price above both 50-day and 200-day moving averages, strong bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:08.652238", "processing_time": 7.603741, "llm_used": true}, "processing_time": 7.603741, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 80.62133999999999}}, {"timestamp": "2025-07-05T22:30:08.849076", "output_id": "output_20250705_223008_6c2f75c1", "input_id": "input_20250705_222954_95045256", "prompt_id": "prompt_20250705_222955_4f89cb5e", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:08.849076", "processing_time": 14.912948, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:08.849076", "processing_time": 14.912948, "llm_used": true}, "processing_time": 14.912948, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 95.53428799999999}}, {"timestamp": "2025-07-05T22:30:08.927790", "output_id": "output_20250705_223008_f73b0815", "input_id": "input_20250705_223000_6bbb5072", "prompt_id": "prompt_20250705_223000_ccc2440e", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:08.922550", "processing_time": 8.74944, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:08.922550", "processing_time": 8.74944, "llm_used": true}, "processing_time": 8.74944, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 104.283728}}, {"timestamp": "2025-07-05T22:30:09.424180", "output_id": "output_20250705_223009_dd1bee4e", "input_id": "input_20250705_223002_62154511", "prompt_id": "prompt_20250705_223002_acbb3833", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "above long-term MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:09.424180", "processing_time": 7.129136, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "above long-term MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:09.424180", "processing_time": 7.129136, "llm_used": true}, "processing_time": 7.129136, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 111.412864}}, {"timestamp": "2025-07-05T22:30:09.911427", "output_id": "output_20250705_223009_4e26440e", "input_id": "input_20250705_223000_61fdd3f1", "prompt_id": "prompt_20250705_223000_2c207e0a", "raw_response": {"analysis_date": "2025-01-10", "trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "MA": {"50_day": 152.0, "200_day": 140.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:09.896359", "processing_time": 9.486699, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-10", "trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "MA": {"50_day": 152.0, "200_day": 140.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:09.896359", "processing_time": 9.486699, "llm_used": true}, "processing_time": 9.486699, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 120.899563}}, {"timestamp": "2025-07-05T22:30:11.734142", "output_id": "output_20250705_223011_18c66c34", "input_id": "input_20250705_223005_632db903", "prompt_id": "prompt_20250705_223005_037fc885", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "comment": "MACD line is above the signal line with a rising histogram, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "comment": "The 50-day MA is above the 200-day MA, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:11.734142", "processing_time": 6.499122, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "comment": "MACD line is above the signal line with a rising histogram, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "comment": "The 50-day MA is above the 200-day MA, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:11.734142", "processing_time": 6.499122, "llm_used": true}, "processing_time": 6.499122, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 127.398685}}, {"timestamp": "2025-07-05T22:30:12.451188", "output_id": "output_20250705_223012_118ebcc2", "input_id": "input_20250705_223004_66ceb86e", "prompt_id": "prompt_20250705_223004_6ffaa13e", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "crossover": "bullish"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:12.451188", "processing_time": 7.575901, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "crossover": "bullish"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:12.451188", "processing_time": 7.575901, "llm_used": true}, "processing_time": 7.575901, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 134.974586}}, {"timestamp": "2025-07-05T22:30:14.042778", "output_id": "output_20250705_223014_0a957fb1", "input_id": "input_20250705_223000_f8a04601", "prompt_id": "prompt_20250705_223000_84a99130", "raw_response": {"analysis_date": "2025-01-10", "trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is in the middle of the overbought territory, suggesting that while the stock may have strong momentum, it could be due for a pullback."}, "MACD": {"signal_line": 0.0, "analysis": "The MACD is above the signal line and has crossed over, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:14.042778", "processing_time": 13.333028, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-10", "trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is in the middle of the overbought territory, suggesting that while the stock may have strong momentum, it could be due for a pullback."}, "MACD": {"signal_line": 0.0, "analysis": "The MACD is above the signal line and has crossed over, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:14.042778", "processing_time": 13.333028, "llm_used": true}, "processing_time": 13.333028, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 148.307614}}, {"timestamp": "2025-07-05T22:30:15.412789", "output_id": "output_20250705_223015_d77a8062", "input_id": "input_20250705_223007_02fa7e63", "prompt_id": "prompt_20250705_223007_021d1cad", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.1, "analysis": "MACD is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:15.412789", "processing_time": 7.840807, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.1, "analysis": "MACD is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:15.412789", "processing_time": 7.840807, "llm_used": true}, "processing_time": 7.840807, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 156.148421}}, {"timestamp": "2025-07-05T22:30:15.960017", "output_id": "output_20250705_223015_c1e6920a", "input_id": "input_20250705_222953_45699aca", "prompt_id": "prompt_20250705_222953_10b05d4b", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "Indicates neutral market conditions."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "MACD is near zero, suggesting a lack of strong trend."}, "Moving_Average": {"50_day_MA": 153.0, "200_day_MA": 160.0, "interpretation": "The stock is trading slightly below its 50-day and above its 200-day moving averages, indicating a slightly bearish short-term trend with a bullish long-term trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:15.921480", "processing_time": 22.134394, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "Indicates neutral market conditions."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "MACD is near zero, suggesting a lack of strong trend."}, "Moving_Average": {"50_day_MA": 153.0, "200_day_MA": 160.0, "interpretation": "The stock is trading slightly below its 50-day and above its 200-day moving averages, indicating a slightly bearish short-term trend with a bullish long-term trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:15.921480", "processing_time": 22.134394, "llm_used": true}, "processing_time": 22.134394, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 178.28281500000003}}, {"timestamp": "2025-07-05T22:30:22.124554", "output_id": "output_20250705_223022_3568f8fc", "input_id": "input_20250705_223016_25168d12", "prompt_id": "prompt_20250705_223016_120e2a55", "raw_response": {"analysis_date": "2025-01-10", "trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "comment": "MACD line is above the signal line and histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "comment": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:22.124554", "processing_time": 5.763389, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-10", "trend": "bullish", "technical_score": 0.45, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "comment": "MACD line is above the signal line and histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "comment": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:22.124554", "processing_time": 5.763389, "llm_used": true}, "processing_time": 5.763389, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 184.04620400000002}}, {"timestamp": "2025-07-05T22:30:27.258563", "output_id": "output_20250705_223027_fb039bbe", "input_id": "input_20250705_223018_5401eb8f", "prompt_id": "prompt_20250705_223018_b2849c71", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 63, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:27.258563", "processing_time": 8.514734, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 63, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:27.258563", "processing_time": 8.514734, "llm_used": true}, "processing_time": 8.514734, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 192.56093800000002}}, {"timestamp": "2025-07-05T22:30:32.897320", "output_id": "output_20250705_223032_090552f9", "input_id": "input_20250705_223027_db1e6ed5", "prompt_id": "prompt_20250705_223027_db6ac0be", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 70, indicating the stock may be overbought, but the overall trend is still bullish."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "MACD signal line is above the zero line with a rising histogram, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:32.897320", "processing_time": 5.804298, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 70, indicating the stock may be overbought, but the overall trend is still bullish."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "MACD signal line is above the zero line with a rising histogram, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:32.897320", "processing_time": 5.804298, "llm_used": true}, "processing_time": 5.804298, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 198.365236}}, {"timestamp": "2025-07-05T22:30:34.078572", "output_id": "output_20250705_223034_fa165a55", "input_id": "input_20250705_223019_2428edc1", "prompt_id": "prompt_20250705_223019_61d0e079", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is in the middle of the overbought zone, indicating a potential pullback."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "The MACD line is above the signal line, showing a bullish trend. The histogram is rising, suggesting continued upward momentum."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which are strong bullish signals."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:34.078572", "processing_time": 14.422861, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is in the middle of the overbought zone, indicating a potential pullback."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "The MACD line is above the signal line, showing a bullish trend. The histogram is rising, suggesting continued upward momentum."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which are strong bullish signals."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:34.078572", "processing_time": 14.422861, "llm_used": true}, "processing_time": 14.422861, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 212.78809700000002}}, {"timestamp": "2025-07-05T22:30:37.003215", "output_id": "output_20250705_223037_ad606a8f", "input_id": "input_20250705_223032_6047fd16", "prompt_id": "prompt_20250705_223032_140c449c", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 56, "trend": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "crossover": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:37.003215", "processing_time": 4.491337, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 56, "trend": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "crossover": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:37.003215", "processing_time": 4.491337, "llm_used": true}, "processing_time": 4.491337, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 217.279434}}, {"timestamp": "2025-07-05T22:30:38.708935", "output_id": "output_20250705_223038_6ea629a2", "input_id": "input_20250705_223033_3df7e43a", "prompt_id": "prompt_20250705_223033_25ade6eb", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": -0.05, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "crossover": "50_day_MA above 200_day_MA", "trend": "bullish"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:38.688909", "processing_time": 5.234818, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": -0.05, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "crossover": "50_day_MA above 200_day_MA", "trend": "bullish"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:38.688909", "processing_time": 5.234818, "llm_used": true}, "processing_time": 5.234818, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 222.514252}}, {"timestamp": "2025-07-05T22:30:39.969976", "output_id": "output_20250705_223039_873cdb96", "input_id": "input_20250705_223030_321586ab", "prompt_id": "prompt_20250705_223030_f8795fc0", "raw_response": {"analysis_date": "2025-01-10", "trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "RSI is currently above 50, indicating a neutral market condition."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "analysis": "The MACD line is close to the signal line with a minimal histogram, suggesting no strong trend."}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "analysis": "The 50-day MA is above the 200-day MA, but the gap is narrow, indicating a weak bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:39.953352", "processing_time": 9.156874, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-10", "trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "RSI is currently above 50, indicating a neutral market condition."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "analysis": "The MACD line is close to the signal line with a minimal histogram, suggesting no strong trend."}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "analysis": "The 50-day MA is above the 200-day MA, but the gap is narrow, indicating a weak bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:39.953352", "processing_time": 9.156874, "llm_used": true}, "processing_time": 9.156874, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 231.671126}}, {"timestamp": "2025-07-05T22:30:42.651247", "output_id": "output_20250705_223042_e36f4535", "input_id": "input_20250705_223038_240822ef", "prompt_id": "prompt_20250705_223038_2c6050be", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "MA": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA, above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:42.651247", "processing_time": 4.584582, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "MA": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA, above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:42.651247", "processing_time": 4.584582, "llm_used": true}, "processing_time": 4.584582, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 236.255708}}, {"timestamp": "2025-07-05T22:30:46.318162", "output_id": "output_20250705_223046_467a4c61", "input_id": "input_20250705_223041_9bbcf88e", "prompt_id": "prompt_20250705_223041_94469177", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"value": "positive", "analysis": "MACD is above the signal line, showing a bullish trend."}, "Moving_Average": {"simple_moving_average": 155.0, "exponential_moving_average": 157.0, "analysis": "Both the simple and exponential moving averages are above the current price, suggesting a bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:46.318162", "processing_time": 4.830989, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"value": "positive", "analysis": "MACD is above the signal line, showing a bullish trend."}, "Moving_Average": {"simple_moving_average": 155.0, "exponential_moving_average": 157.0, "analysis": "Both the simple and exponential moving averages are above the current price, suggesting a bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:46.318162", "processing_time": 4.830989, "llm_used": true}, "processing_time": 4.830989, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 241.086697}}, {"timestamp": "2025-07-05T22:30:48.908783", "output_id": "output_20250705_223048_375efb04", "input_id": "input_20250705_223044_1b32430f", "prompt_id": "prompt_20250705_223044_f7666e6d", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "interpretation": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:48.896278", "processing_time": 4.580295, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "interpretation": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:48.896278", "processing_time": 4.580295, "llm_used": true}, "processing_time": 4.580295, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 245.666992}}, {"timestamp": "2025-07-05T22:30:49.140184", "output_id": "output_20250705_223049_e4d1ec34", "input_id": "input_20250705_223041_26769f61", "prompt_id": "prompt_20250705_223041_df695db9", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"current_value": 0.1, "analysis": "MACD line is above the signal line, showing bullish momentum."}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term uptrend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:49.140184", "processing_time": 8.045406, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"current_value": 0.1, "analysis": "MACD line is above the signal line, showing bullish momentum."}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term uptrend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:49.140184", "processing_time": 8.045406, "llm_used": true}, "processing_time": 8.045406, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 253.712398}}, {"timestamp": "2025-07-05T22:30:49.204217", "output_id": "output_20250705_223049_732e4781", "input_id": "input_20250705_223033_fabb0203", "prompt_id": "prompt_20250705_223033_984103a7", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 58.2, "interpretation": "Neutral - indicating that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "Neutral - suggesting no strong momentum in either direction."}, "Moving_Average": {"50-Day_MA": 165.0, "200-Day_MA": 175.0, "interpretation": "Neutral - price is within a range between the two moving averages."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:49.182127", "processing_time": 15.994251, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 58.2, "interpretation": "Neutral - indicating that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "Neutral - suggesting no strong momentum in either direction."}, "Moving_Average": {"50-Day_MA": 165.0, "200-Day_MA": 175.0, "interpretation": "Neutral - price is within a range between the two moving averages."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:49.182127", "processing_time": 15.994251, "llm_used": true}, "processing_time": 15.994251, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 269.706649}}, {"timestamp": "2025-07-05T22:30:49.647992", "output_id": "output_20250705_223049_05cca2d9", "input_id": "input_20250705_223040_b315b950", "prompt_id": "prompt_20250705_223040_a5a496fe", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:49.647992", "processing_time": 8.82932, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:49.647992", "processing_time": 8.82932, "llm_used": true}, "processing_time": 8.82932, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 278.535969}}, {"timestamp": "2025-07-05T22:30:56.340005", "output_id": "output_20250705_223056_fcf19d77", "input_id": "input_20250705_223038_58885abd", "prompt_id": "prompt_20250705_223038_59684c2f", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "trend": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "trend": "neutral"}, "Moving_Averages": {"50-Day_MA": 153.0, "200-Day_MA": 145.0, "current_price": 150.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:56.339060", "processing_time": 17.686831, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "trend": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "trend": "neutral"}, "Moving_Averages": {"50-Day_MA": 153.0, "200-Day_MA": 145.0, "current_price": 150.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:30:56.339060", "processing_time": 17.686831, "llm_used": true}, "processing_time": 17.686831, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 296.2228}}]