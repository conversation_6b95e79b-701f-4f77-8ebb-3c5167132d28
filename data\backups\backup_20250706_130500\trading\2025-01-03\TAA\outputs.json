[{"timestamp": "2025-07-05T22:15:15.013247", "output_id": "output_20250705_221515_8362ae45", "input_id": "input_20250705_221456_0a05b53c", "prompt_id": "prompt_20250705_221456_9cd5d7f4", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50.3, "trend": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": {"bullish": false, "bearish": true}}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "current_price": 155.5}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:15:15.013247", "processing_time": 18.581713, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50.3, "trend": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": {"bullish": false, "bearish": true}}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "current_price": 155.5}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:15:15.013247", "processing_time": 18.581713, "llm_used": true}, "processing_time": 18.581713, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 18.581713}}, {"timestamp": "2025-07-05T22:16:03.452194", "output_id": "output_20250705_221603_66334ea5", "input_id": "input_20250705_221559_a248befb", "prompt_id": "prompt_20250705_221559_f779cb76", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.03, "signal": "neutral"}, "Moving_Average": {"50_day": 140.0, "200_day": 130.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:03.452194", "processing_time": 4.430783, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.03, "signal": "neutral"}, "Moving_Average": {"50_day": 140.0, "200_day": 130.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:03.452194", "processing_time": 4.430783, "llm_used": true}, "processing_time": 4.430783, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 23.012496}}, {"timestamp": "2025-07-05T22:16:03.601828", "output_id": "output_20250705_221603_6ee27aa8", "input_id": "input_20250705_221559_3bf2dbf9", "prompt_id": "prompt_20250705_221559_4fcfaba0", "raw_response": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal_line": -0.02, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:03.588137", "processing_time": 4.579289, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal_line": -0.02, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:03.588137", "processing_time": 4.579289, "llm_used": true}, "processing_time": 4.579289, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 27.591784999999998}}, {"timestamp": "2025-07-05T22:16:04.837451", "output_id": "output_20250705_221604_a8b1828b", "input_id": "input_20250705_221558_5bcd90ba", "prompt_id": "prompt_20250705_221558_b5b48901", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 57.5, "signal": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:04.837451", "processing_time": 5.949695, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 57.5, "signal": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:04.837451", "processing_time": 5.949695, "llm_used": true}, "processing_time": 5.949695, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 33.54148}}, {"timestamp": "2025-07-05T22:16:05.195046", "output_id": "output_20250705_221605_d24fa608", "input_id": "input_20250705_221558_d6869b31", "prompt_id": "prompt_20250705_221558_dc0093b3", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 56.7, "analysis": "Neutral - The RSI is neither overbought nor oversold, indicating a stable market condition."}, "MACD": {"signal_line": 0.05, "analysis": "Neutral - The MACD line is close to the signal line, suggesting no strong trend."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 160.0, "analysis": "Neutral - The stock is currently between the 50-day and 200-day moving averages, indicating a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:05.192046", "processing_time": 6.399256, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 56.7, "analysis": "Neutral - The RSI is neither overbought nor oversold, indicating a stable market condition."}, "MACD": {"signal_line": 0.05, "analysis": "Neutral - The MACD line is close to the signal line, suggesting no strong trend."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 160.0, "analysis": "Neutral - The stock is currently between the 50-day and 200-day moving averages, indicating a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:05.192046", "processing_time": 6.399256, "llm_used": true}, "processing_time": 6.399256, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 39.940736}}, {"timestamp": "2025-07-05T22:16:05.535675", "output_id": "output_20250705_221605_3fc627e1", "input_id": "input_20250705_221559_a3e6c82a", "prompt_id": "prompt_20250705_221559_36a170f4", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "analysis": "Neutral - The RSI is neither overbought nor oversold, indicating a stable trend."}, "MACD": {"signal_line": 0.03, "histogram": -0.02, "analysis": "Neutral - The MACD signal line and histogram are close to zero, suggesting no strong trend direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "Neutral - The stock is currently between the 50-day and 200-day moving averages, suggesting a stable trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:05.535675", "processing_time": 6.433201, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "analysis": "Neutral - The RSI is neither overbought nor oversold, indicating a stable trend."}, "MACD": {"signal_line": 0.03, "histogram": -0.02, "analysis": "Neutral - The MACD signal line and histogram are close to zero, suggesting no strong trend direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "Neutral - The stock is currently between the 50-day and 200-day moving averages, suggesting a stable trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:05.535675", "processing_time": 6.433201, "llm_used": true}, "processing_time": 6.433201, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 46.373937}}, {"timestamp": "2025-07-05T22:16:06.894391", "output_id": "output_20250705_221606_2109a002", "input_id": "input_20250705_221559_5892947c", "prompt_id": "prompt_20250705_221559_47b07737", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:06.894391", "processing_time": 7.815041, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:06.894391", "processing_time": 7.815041, "llm_used": true}, "processing_time": 7.815041, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 54.188978}}, {"timestamp": "2025-07-05T22:16:07.419981", "output_id": "output_20250705_221607_8e0876c0", "input_id": "input_20250705_221559_f58f736c", "prompt_id": "prompt_20250705_221600_4b315871", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 54, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.05, "interpretation": "Slight bullish crossover"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 150.0, "interpretation": "50-Day MA above 200-Day MA, suggesting a slight bullish trend"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:07.419981", "processing_time": 8.181586, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 54, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.05, "interpretation": "Slight bullish crossover"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 150.0, "interpretation": "50-Day MA above 200-Day MA, suggesting a slight bullish trend"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:07.419981", "processing_time": 8.181586, "llm_used": true}, "processing_time": 8.181586, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 62.370564}}, {"timestamp": "2025-07-05T22:16:07.677825", "output_id": "output_20250705_221607_36c7e903", "input_id": "input_20250705_221603_ac15890b", "prompt_id": "prompt_20250705_221603_3fe1ea39", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:07.677825", "processing_time": 4.201598, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:07.677825", "processing_time": 4.201598, "llm_used": true}, "processing_time": 4.201598, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 66.572162}}, {"timestamp": "2025-07-05T22:16:08.170519", "output_id": "output_20250705_221608_9f1b1381", "input_id": "input_20250705_221559_76503d70", "prompt_id": "prompt_20250705_221559_8ca12696", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 160.0, "200-Day_MA": 180.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:08.170519", "processing_time": 9.081655, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 160.0, "200-Day_MA": 180.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:08.170519", "processing_time": 9.081655, "llm_used": true}, "processing_time": 9.081655, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 75.653817}}, {"timestamp": "2025-07-05T22:16:08.288730", "output_id": "output_20250705_221608_b6487bb8", "input_id": "input_20250705_221604_5433eea6", "prompt_id": "prompt_20250705_221604_d75205a4", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.3, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:08.285731", "processing_time": 3.977208, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.3, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:08.285731", "processing_time": 3.977208, "llm_used": true}, "processing_time": 3.977208, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 79.63102500000001}}, {"timestamp": "2025-07-05T22:16:08.938513", "output_id": "output_20250705_221608_520af6b9", "input_id": "input_20250705_221603_0846da65", "prompt_id": "prompt_20250705_221603_460be552", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:08.938513", "processing_time": 5.243462, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:08.938513", "processing_time": 5.243462, "llm_used": true}, "processing_time": 5.243462, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 84.874487}}, {"timestamp": "2025-07-05T22:16:09.156671", "output_id": "output_20250705_221609_b6a1b9ad", "input_id": "input_20250705_221604_0b6f3b85", "prompt_id": "prompt_20250705_221604_28737b3f", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:09.156671", "processing_time": 4.801093, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:09.156671", "processing_time": 4.801093, "llm_used": true}, "processing_time": 4.801093, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 89.67558}}, {"timestamp": "2025-07-05T22:16:09.586383", "output_id": "output_20250705_221609_39ac82ae", "input_id": "input_20250705_221604_908192b2", "prompt_id": "prompt_20250705_221604_cf38c35d", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69.3, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 140.0, "signal": "50-Day_MA above 200-Day_MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:09.586383", "processing_time": 4.795843, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69.3, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 140.0, "signal": "50-Day_MA above 200-Day_MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:09.586383", "processing_time": 4.795843, "llm_used": true}, "processing_time": 4.795843, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 94.471423}}, {"timestamp": "2025-07-05T22:16:10.541557", "output_id": "output_20250705_221610_7fceaed2", "input_id": "input_20250705_221605_a62edad0", "prompt_id": "prompt_20250705_221605_6d2e19a7", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "interpretation": "MACD line above signal line indicates bullish momentum"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above both 50-day and 200-day moving averages indicates strong bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:10.541557", "processing_time": 5.346511, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "interpretation": "MACD line above signal line indicates bullish momentum"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above both 50-day and 200-day moving averages indicates strong bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:10.541557", "processing_time": 5.346511, "llm_used": true}, "processing_time": 5.346511, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 99.81793400000001}}, {"timestamp": "2025-07-05T22:16:10.687596", "output_id": "output_20250705_221610_710f0fc3", "input_id": "input_20250705_221604_02ff2a34", "prompt_id": "prompt_20250705_221604_989ecb5a", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "analysis": "The MACD signal line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "analysis": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, suggesting a long-term bullish trend but a short-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:10.687596", "processing_time": 6.283954, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "analysis": "The MACD signal line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "analysis": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, suggesting a long-term bullish trend but a short-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:10.687596", "processing_time": 6.283954, "llm_used": true}, "processing_time": 6.283954, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 106.101888}}, {"timestamp": "2025-07-05T22:16:11.165676", "output_id": "output_20250705_221611_0e669b17", "input_id": "input_20250705_221606_f3155832", "prompt_id": "prompt_20250705_221606_14f8a7af", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69.2, "overbought": true}, "MACD": {"signal_line": 0.2, "histogram": 0.4, "trend": "upward"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "trend": "upward"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:11.165676", "processing_time": 4.55609, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69.2, "overbought": true}, "MACD": {"signal_line": 0.2, "histogram": 0.4, "trend": "upward"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "trend": "upward"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:11.165676", "processing_time": 4.55609, "llm_used": true}, "processing_time": 4.55609, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 110.657978}}, {"timestamp": "2025-07-05T22:16:13.294733", "output_id": "output_20250705_221613_828b0aa9", "input_id": "input_20250705_221607_a4e6cc2c", "prompt_id": "prompt_20250705_221607_7361dcb4", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "interpretation": "50-day MA above 200-day MA, indicating a long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:13.294733", "processing_time": 5.357588, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "interpretation": "50-day MA above 200-day MA, indicating a long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:13.294733", "processing_time": 5.357588, "llm_used": true}, "processing_time": 5.357588, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 116.015566}}, {"timestamp": "2025-07-05T22:16:14.791636", "output_id": "output_20250705_221614_bf1bec5b", "input_id": "input_20250705_221604_c5e64807", "prompt_id": "prompt_20250705_221604_75954f9a", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.005, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Average": {"50_day": 155.0, "200_day": 145.0, "signal": "price above 50-day moving average"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:14.791636", "processing_time": 10.212981, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.005, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Average": {"50_day": 155.0, "200_day": 145.0, "signal": "price above 50-day moving average"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:14.791636", "processing_time": 10.212981, "llm_used": true}, "processing_time": 10.212981, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 126.228547}}, {"timestamp": "2025-07-05T22:16:21.769859", "output_id": "output_20250705_221621_1d44c43b", "input_id": "input_20250705_221613_9ad3219b", "prompt_id": "prompt_20250705_221613_b0e79e19", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 10, "histogram": {"current_value": 0.5, "trend": "upward"}}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "trend": "upward"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:21.769859", "processing_time": 8.545446, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 10, "histogram": {"current_value": 0.5, "trend": "upward"}}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "trend": "upward"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:21.769859", "processing_time": 8.545446, "llm_used": true}, "processing_time": 8.545446, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 134.77399300000002}}, {"timestamp": "2025-07-05T22:16:23.384295", "output_id": "output_20250705_221623_f1dadcbc", "input_id": "input_20250705_221617_43a6733c", "prompt_id": "prompt_20250705_221617_96a3c298", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 140.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 50, "analysis": "The RSI is at a neutral level, indicating no immediate overbought or oversold conditions."}, "MACD": {"signal_line": 0, "analysis": "The MACD line is at the zero level, suggesting a lack of strong momentum in either direction."}, "Moving_Average": {"50_day_MA": 145.0, "200_day_MA": 155.0, "analysis": "The stock is currently below its 50-day moving average but above its 200-day moving average, indicating a slightly bearish short-term trend but a long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:23.384295", "processing_time": 6.27306, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 140.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 50, "analysis": "The RSI is at a neutral level, indicating no immediate overbought or oversold conditions."}, "MACD": {"signal_line": 0, "analysis": "The MACD line is at the zero level, suggesting a lack of strong momentum in either direction."}, "Moving_Average": {"50_day_MA": 145.0, "200_day_MA": 155.0, "analysis": "The stock is currently below its 50-day moving average but above its 200-day moving average, indicating a slightly bearish short-term trend but a long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:23.384295", "processing_time": 6.27306, "llm_used": true}, "processing_time": 6.27306, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 141.047053}}, {"timestamp": "2025-07-05T22:16:24.378009", "output_id": "output_20250705_221624_d586e017", "input_id": "input_20250705_221618_224118a4", "prompt_id": "prompt_20250705_221618_6ff8b2e1", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "price above 50-day and 200-day moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:24.378009", "processing_time": 5.928135, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "price above 50-day and 200-day moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:24.378009", "processing_time": 5.928135, "llm_used": true}, "processing_time": 5.928135, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 146.975188}}, {"timestamp": "2025-07-05T22:16:27.444615", "output_id": "output_20250705_221627_ac78e4b2", "input_id": "input_20250705_221622_2570fb6f", "prompt_id": "prompt_20250705_221622_90f37255", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69.2, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "interpretation": "50-day MA above 200-day MA, indicating a bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:27.388786", "processing_time": 4.580609, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69.2, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "interpretation": "50-day MA above 200-day MA, indicating a bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:27.388786", "processing_time": 4.580609, "llm_used": true}, "processing_time": 4.580609, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 151.555797}}, {"timestamp": "2025-07-05T22:16:33.271556", "output_id": "output_20250705_221633_fa4ab536", "input_id": "input_20250705_221627_9d2a6444", "prompt_id": "prompt_20250705_221627_9aba1fee", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is neither overbought nor oversold, indicating a neutral trend."}, "MACD": {"signal_line": 0.01, "analysis": "MACD line is close to the signal line, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "analysis": "Stock price is currently between the 50-day and 200-day moving averages, which indicates a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:33.271556", "processing_time": 6.126515, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is neither overbought nor oversold, indicating a neutral trend."}, "MACD": {"signal_line": 0.01, "analysis": "MACD line is close to the signal line, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "analysis": "Stock price is currently between the 50-day and 200-day moving averages, which indicates a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:33.271556", "processing_time": 6.126515, "llm_used": true}, "processing_time": 6.126515, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 157.68231200000002}}, {"timestamp": "2025-07-05T22:16:34.554793", "output_id": "output_20250705_221634_ff715faf", "input_id": "input_20250705_221629_9b0c081a", "prompt_id": "prompt_20250705_221629_8d51d24f", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Average": {"50_day": 155.0, "200_day": 145.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:34.554793", "processing_time": 5.287874, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Average": {"50_day": 155.0, "200_day": 145.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:34.554793", "processing_time": 5.287874, "llm_used": true}, "processing_time": 5.287874, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 162.970186}}, {"timestamp": "2025-07-05T22:16:38.474541", "output_id": "output_20250705_221638_0ec5105c", "input_id": "input_20250705_221631_832566a4", "prompt_id": "prompt_20250705_221632_35dc693f", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 60.2, "interpretation": "Neutral; the RSI is neither overbought nor oversold, indicating a stable market condition."}, "MACD": {"signal_line": 0.001, "histogram": -0.002, "interpretation": "The MACD is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 160.0, "interpretation": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, suggesting a long-term bullish trend but a short-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:38.474541", "processing_time": 6.549426, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 60.2, "interpretation": "Neutral; the RSI is neither overbought nor oversold, indicating a stable market condition."}, "MACD": {"signal_line": 0.001, "histogram": -0.002, "interpretation": "The MACD is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 160.0, "interpretation": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, suggesting a long-term bullish trend but a short-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:38.474541", "processing_time": 6.549426, "llm_used": true}, "processing_time": 6.549426, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 169.51961200000002}}, {"timestamp": "2025-07-05T22:16:40.019740", "output_id": "output_20250705_221640_8d8d48cc", "input_id": "input_20250705_221635_a809da21", "prompt_id": "prompt_20250705_221635_45167759", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50.0, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "historical_line": 0.0, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:40.019740", "processing_time": 4.131096, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50.0, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "historical_line": 0.0, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:40.019740", "processing_time": 4.131096, "llm_used": true}, "processing_time": 4.131096, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 173.65070800000004}}, {"timestamp": "2025-07-05T22:16:41.927911", "output_id": "output_20250705_221641_0355723e", "input_id": "input_20250705_221627_2d410399", "prompt_id": "prompt_20250705_221627_83cea3e5", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 20, "histogram": {"current_value": 0.5, "trend": "positive"}}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 120.0, "trend": "upward"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:41.927911", "processing_time": 14.156464, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 20, "histogram": {"current_value": 0.5, "trend": "positive"}}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 120.0, "trend": "upward"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:41.927911", "processing_time": 14.156464, "llm_used": true}, "processing_time": 14.156464, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 187.80717200000004}}, {"timestamp": "2025-07-05T22:16:42.499707", "output_id": "output_20250705_221642_07a6b189", "input_id": "input_20250705_221622_fb661e27", "prompt_id": "prompt_20250705_221622_fd3f1e2c", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:42.498199", "processing_time": 19.999664, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:42.498199", "processing_time": 19.999664, "llm_used": true}, "processing_time": 19.999664, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 207.80683600000003}}, {"timestamp": "2025-07-05T22:16:42.505554", "output_id": "output_20250705_221642_43a84b93", "input_id": "input_20250705_221633_63e31f20", "prompt_id": "prompt_20250705_221633_110ea9be", "raw_response": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 120.0, "resistance_level": 130.0, "indicators": {"RSI": {"current_value": 60, "trend": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 125.0, "200_day_MA": 135.0, "current_price": 125.5, "trend": "neutral"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:42.499707", "processing_time": 8.619657, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 120.0, "resistance_level": 130.0, "indicators": {"RSI": {"current_value": 60, "trend": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 125.0, "200_day_MA": 135.0, "current_price": 125.5, "trend": "neutral"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:42.499707", "processing_time": 8.619657, "llm_used": true}, "processing_time": 8.619657, "llm_used": true, "confidence": 0.75, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 216.42649300000002}}, {"timestamp": "2025-07-05T22:16:45.776570", "output_id": "output_20250705_221645_992c0036", "input_id": "input_20250705_221637_044c4c7e", "prompt_id": "prompt_20250705_221637_b0cc5f36", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "analysis": "The RSI is close to the middle range, indicating neither overbought nor oversold conditions."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is slightly above the signal line, showing a mild bullish trend."}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 145.0, "analysis": "The stock is currently above the 50-day moving average and close to the 200-day moving average, suggesting a stable trend."}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:45.751608", "processing_time": 8.731244, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "analysis": "The RSI is close to the middle range, indicating neither overbought nor oversold conditions."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is slightly above the signal line, showing a mild bullish trend."}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 145.0, "analysis": "The stock is currently above the 50-day moving average and close to the 200-day moving average, suggesting a stable trend."}}, "confidence": 0.85, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:45.751608", "processing_time": 8.731244, "llm_used": true}, "processing_time": 8.731244, "llm_used": true, "confidence": 0.85, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 225.15773700000003}}, {"timestamp": "2025-07-05T22:16:50.555099", "output_id": "output_20250705_221650_98a40a58", "input_id": "input_20250705_221639_3ef5e8ea", "prompt_id": "prompt_20250705_221639_5541636e", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 70, "comment": "RSI is overbought, indicating a possible pullback."}, "MACD": {"signal": "positive", "comment": "MACD is showing a bullish crossover, suggesting upward momentum."}, "Moving_Average": {"short_term": {"value": 140.0, "comment": "Short-term moving average is above the long-term, indicating a bullish trend."}, "long_term": {"value": 130.0, "comment": "Long-term moving average is providing support at around $130."}}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:50.555099", "processing_time": 11.145408, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 70, "comment": "RSI is overbought, indicating a possible pullback."}, "MACD": {"signal": "positive", "comment": "MACD is showing a bullish crossover, suggesting upward momentum."}, "Moving_Average": {"short_term": {"value": 140.0, "comment": "Short-term moving average is above the long-term, indicating a bullish trend."}, "long_term": {"value": 130.0, "comment": "Long-term moving average is providing support at around $130."}}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:50.555099", "processing_time": 11.145408, "llm_used": true}, "processing_time": 11.145408, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 236.30314500000003}}, {"timestamp": "2025-07-05T22:16:53.809603", "output_id": "output_20250705_221653_69c38aea", "input_id": "input_20250705_221642_12724ec8", "prompt_id": "prompt_20250705_221642_5d5c39e4", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "overbought": false, "oversold": false, "signal": "bullish"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "crossover": "bullish", "signal": "bullish"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 140.0, "crossover": "bullish", "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:53.809603", "processing_time": 11.004664, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "overbought": false, "oversold": false, "signal": "bullish"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "crossover": "bullish", "signal": "bullish"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 140.0, "crossover": "bullish", "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:16:53.809603", "processing_time": 11.004664, "llm_used": true}, "processing_time": 11.004664, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 247.30780900000002}}]