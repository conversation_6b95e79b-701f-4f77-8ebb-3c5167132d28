[{"timestamp": "2025-07-05T22:24:13.027056", "output_id": "output_20250705_222413_f2d1deb7", "input_id": "input_20250705_222403_36985466", "prompt_id": "prompt_20250705_222403_5cef3043", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.7, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal": "bullish crossover", "histogram": "positive trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:24:13.027056", "processing_time": 9.482543, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.7, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal": "bullish crossover", "histogram": "positive trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:24:13.027056", "processing_time": 9.482543, "llm_used": true}, "processing_time": 9.482543, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 9.482543}}, {"timestamp": "2025-07-05T22:25:08.055602", "output_id": "output_20250705_222508_ca28bd16", "input_id": "input_20250705_222504_3216219f", "prompt_id": "prompt_20250705_222504_b43974a6", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "interpretation": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 155.0, "crossover": "none", "interpretation": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:08.055602", "processing_time": 4.001581, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "interpretation": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 155.0, "crossover": "none", "interpretation": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:08.055602", "processing_time": 4.001581, "llm_used": true}, "processing_time": 4.001581, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 13.484124}}, {"timestamp": "2025-07-05T22:25:09.010055", "output_id": "output_20250705_222509_c7af42a1", "input_id": "input_20250705_222503_e11bf8d5", "prompt_id": "prompt_20250705_222504_34f2fd72", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.7, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "MACD signal line is above the zero line, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "Price is above both the 50-day and 200-day moving averages, suggesting a long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:09.010055", "processing_time": 5.020307, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.7, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "MACD signal line is above the zero line, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "Price is above both the 50-day and 200-day moving averages, suggesting a long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:09.010055", "processing_time": 5.020307, "llm_used": true}, "processing_time": 5.020307, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 18.504431}}, {"timestamp": "2025-07-05T22:25:10.993828", "output_id": "output_20250705_222510_7e70343b", "input_id": "input_20250705_222504_f7475214", "prompt_id": "prompt_20250705_222504_43bd3090", "raw_response": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68.5, "interpretation": "overbought"}, "MACD": {"signal_line": 20.3, "histogram": 9.5, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:10.993828", "processing_time": 6.857732, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68.5, "interpretation": "overbought"}, "MACD": {"signal_line": 20.3, "histogram": 9.5, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:10.993828", "processing_time": 6.857732, "llm_used": true}, "processing_time": 6.857732, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 25.362163000000002}}, {"timestamp": "2025-07-05T22:25:11.691616", "output_id": "output_20250705_222511_9d41af17", "input_id": "input_20250705_222504_4490df78", "prompt_id": "prompt_20250705_222504_8fac9d95", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 55, "analysis": "Neutral - The RSI is neither overbought nor oversold, indicating a stable market condition."}, "MACD": {"signal_line": 0.02, "analysis": "Neutral - The MACD line is close to the signal line, suggesting no strong trend direction."}, "Moving_Averages": {"50_MA": 152.0, "200_MA": 150.0, "analysis": "Slightly Bullish - The stock is above the 50-day and 200-day moving averages, suggesting a long-term uptrend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:11.691616", "processing_time": 7.602763, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 55, "analysis": "Neutral - The RSI is neither overbought nor oversold, indicating a stable market condition."}, "MACD": {"signal_line": 0.02, "analysis": "Neutral - The MACD line is close to the signal line, suggesting no strong trend direction."}, "Moving_Averages": {"50_MA": 152.0, "200_MA": 150.0, "analysis": "Slightly Bullish - The stock is above the 50-day and 200-day moving averages, suggesting a long-term uptrend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:11.691616", "processing_time": 7.602763, "llm_used": true}, "processing_time": 7.602763, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 32.964926000000006}}, {"timestamp": "2025-07-05T22:25:11.817928", "output_id": "output_20250705_222511_1c5b6591", "input_id": "input_20250705_222504_922528ff", "prompt_id": "prompt_20250705_222504_0dfa07d9", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:11.817928", "processing_time": 7.682336, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:11.817928", "processing_time": 7.682336, "llm_used": true}, "processing_time": 7.682336, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 40.647262000000005}}, {"timestamp": "2025-07-05T22:25:12.009626", "output_id": "output_20250705_222512_4c9ecb97", "input_id": "input_20250705_222504_b169120e", "prompt_id": "prompt_20250705_222504_1635c588", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line_above": true, "interpretation": "The MACD line is above the signal line, indicating a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, suggesting a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:12.009626", "processing_time": 7.772852, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line_above": true, "interpretation": "The MACD line is above the signal line, indicating a bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, suggesting a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:12.009626", "processing_time": 7.772852, "llm_used": true}, "processing_time": 7.772852, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 48.420114000000005}}, {"timestamp": "2025-07-05T22:25:12.056260", "output_id": "output_20250705_222512_18c0438b", "input_id": "input_20250705_222503_1cbab470", "prompt_id": "prompt_20250705_222503_760132de", "raw_response": {"trend": "neutral", "technical_score": -0.2, "trading_signal": "neutral", "signal_strength": 0.4, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 53.5, "trend": "neutral"}, "MACD": {"signal_line": 0.02, "trend": "neutral"}, "Moving_Average": {"50_day_MA": 165.0, "200_day_MA": 160.0, "trend": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:12.056260", "processing_time": 8.185252, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.2, "trading_signal": "neutral", "signal_strength": 0.4, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 53.5, "trend": "neutral"}, "MACD": {"signal_line": 0.02, "trend": "neutral"}, "Moving_Average": {"50_day_MA": 165.0, "200_day_MA": 160.0, "trend": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:12.056260", "processing_time": 8.185252, "llm_used": true}, "processing_time": 8.185252, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 56.605366000000004}}, {"timestamp": "2025-07-05T22:25:13.008059", "output_id": "output_20250705_222513_f284f53e", "input_id": "input_20250705_222508_f1975913", "prompt_id": "prompt_20250705_222508_73138118", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "crossover above 50-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:13.008059", "processing_time": 4.112137, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "crossover above 50-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:13.008059", "processing_time": 4.112137, "llm_used": true}, "processing_time": 4.112137, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 60.717503}}, {"timestamp": "2025-07-05T22:25:13.524254", "output_id": "output_20250705_222513_00415d55", "input_id": "input_20250705_222507_a2a391d2", "prompt_id": "prompt_20250705_222507_818ef11e", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:13.524254", "processing_time": 5.921449, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:13.524254", "processing_time": 5.921449, "llm_used": true}, "processing_time": 5.921449, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 66.638952}}, {"timestamp": "2025-07-05T22:25:14.219018", "output_id": "output_20250705_222514_102b187a", "input_id": "input_20250705_222507_b51a9600", "prompt_id": "prompt_20250705_222507_8c1a87c1", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:14.219018", "processing_time": 6.272023, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:14.219018", "processing_time": 6.272023, "llm_used": true}, "processing_time": 6.272023, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 72.91097500000001}}, {"timestamp": "2025-07-05T22:25:14.415808", "output_id": "output_20250705_222514_a6ef2c99", "input_id": "input_20250705_222503_3af38d43", "prompt_id": "prompt_20250705_222503_38d0d472", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55.2, "analysis": "The RSI is near the middle of the range, indicating no strong overbought or oversold conditions."}, "MACD": {"current_value": -0.05, "analysis": "The MACD line is just below the signal line, suggesting a weak bullish trend but not strong enough to be considered a buy signal."}, "Moving_Average": {"short_term_MA": 153.5, "long_term_MA": 148.0, "analysis": "The stock price is currently above both the short-term and long-term moving averages, suggesting a slightly bullish trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:14.415808", "processing_time": 10.510025, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55.2, "analysis": "The RSI is near the middle of the range, indicating no strong overbought or oversold conditions."}, "MACD": {"current_value": -0.05, "analysis": "The MACD line is just below the signal line, suggesting a weak bullish trend but not strong enough to be considered a buy signal."}, "Moving_Average": {"short_term_MA": 153.5, "long_term_MA": 148.0, "analysis": "The stock price is currently above both the short-term and long-term moving averages, suggesting a slightly bullish trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:14.415808", "processing_time": 10.510025, "llm_used": true}, "processing_time": 10.510025, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 83.421}}, {"timestamp": "2025-07-05T22:25:14.855110", "output_id": "output_20250705_222514_bc974d00", "input_id": "input_20250705_222509_358b62f1", "prompt_id": "prompt_20250705_222509_071b2f1e", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "MACD signal line is above the zero line with a rising histogram, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:14.855110", "processing_time": 4.908662, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "analysis": "MACD signal line is above the zero line with a rising histogram, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:14.855110", "processing_time": 4.908662, "llm_used": true}, "processing_time": 4.908662, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 88.32966200000001}}, {"timestamp": "2025-07-05T22:25:16.387128", "output_id": "output_20250705_222516_e4f8a11c", "input_id": "input_20250705_222508_3ccf0ca4", "prompt_id": "prompt_20250705_222508_421b206f", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is in the overbought territory, suggesting a potential pullback."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is above the signal line, indicating bullish momentum."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is bullish."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:16.387128", "processing_time": 7.998275, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is in the overbought territory, suggesting a potential pullback."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is above the signal line, indicating bullish momentum."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is bullish."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:16.387128", "processing_time": 7.998275, "llm_used": true}, "processing_time": 7.998275, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 96.32793700000002}}, {"timestamp": "2025-07-05T22:25:16.436841", "output_id": "output_20250705_222516_74d18dfd", "input_id": "input_20250705_222509_caa8025e", "prompt_id": "prompt_20250705_222509_bbf3e339", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "interpretation": "MACD signal line is above the zero line, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, suggesting a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:16.436463", "processing_time": 6.849434, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "interpretation": "MACD signal line is above the zero line, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, suggesting a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:16.436463", "processing_time": 6.849434, "llm_used": true}, "processing_time": 6.849434, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 103.17737100000002}}, {"timestamp": "2025-07-05T22:25:16.645056", "output_id": "output_20250705_222516_aef0c8e2", "input_id": "input_20250705_222509_5f51d061", "prompt_id": "prompt_20250705_222509_98e5c7fc", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"current_value": "positive", "analysis": "MACD is showing a positive signal, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:16.645056", "processing_time": 6.924543, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"current_value": "positive", "analysis": "MACD is showing a positive signal, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:16.645056", "processing_time": 6.924543, "llm_used": true}, "processing_time": 6.924543, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 110.10191400000002}}, {"timestamp": "2025-07-05T22:25:16.700483", "output_id": "output_20250705_222516_346c29a2", "input_id": "input_20250705_222508_3b673e54", "prompt_id": "prompt_20250705_222508_d50b4537", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:16.700483", "processing_time": 7.75926, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:16.700483", "processing_time": 7.75926, "llm_used": true}, "processing_time": 7.75926, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 117.86117400000002}}, {"timestamp": "2025-07-05T22:25:19.431565", "output_id": "output_20250705_222519_fa3c05a7", "input_id": "input_20250705_222510_7aadd2f2", "prompt_id": "prompt_20250705_222510_a0de5785", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.5, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:19.431565", "processing_time": 8.770452, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.5, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:19.431565", "processing_time": 8.770452, "llm_used": true}, "processing_time": 8.770452, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 126.63162600000003}}, {"timestamp": "2025-07-05T22:25:20.107685", "output_id": "output_20250705_222520_7e868b43", "input_id": "input_20250705_222515_88d45155", "prompt_id": "prompt_20250705_222515_d39a42ad", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:20.107685", "processing_time": 4.555681, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:20.107685", "processing_time": 4.555681, "llm_used": true}, "processing_time": 4.555681, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 131.18730700000003}}, {"timestamp": "2025-07-05T22:25:30.168913", "output_id": "output_20250705_222530_f7a200d4", "input_id": "input_20250705_222524_ff1bf31a", "prompt_id": "prompt_20250705_222524_d118cff2", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI处于中性区域，但接近超买水平，短期内有回调风险"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "comment": "MACD线在信号线之上，且柱状图呈上升趋势，显示看涨趋势"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "50日移动平均线向上穿过200日移动平均线，确认长期看涨趋势"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:30.168913", "processing_time": 5.625355, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI处于中性区域，但接近超买水平，短期内有回调风险"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "comment": "MACD线在信号线之上，且柱状图呈上升趋势，显示看涨趋势"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "50日移动平均线向上穿过200日移动平均线，确认长期看涨趋势"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:30.168913", "processing_time": 5.625355, "llm_used": true}, "processing_time": 5.625355, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 136.81266200000005}}, {"timestamp": "2025-07-05T22:25:31.385955", "output_id": "output_20250705_222531_502f0461", "input_id": "input_20250705_222525_56ad9cd6", "prompt_id": "prompt_20250705_222525_e91862e3", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 60, "histogram": {"current": 0.5, "previous": 0.2}, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160, "200_day_MA": 150, "interpretation": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:31.385955", "processing_time": 5.917704, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 60, "histogram": {"current": 0.5, "previous": 0.2}, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160, "200_day_MA": 150, "interpretation": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:31.385955", "processing_time": 5.917704, "llm_used": true}, "processing_time": 5.917704, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 142.73036600000003}}, {"timestamp": "2025-07-05T22:25:35.644844", "output_id": "output_20250705_222535_76430c3a", "input_id": "input_20250705_222529_c8075a59", "prompt_id": "prompt_20250705_222529_f5a6a966", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "interpretation": "Positive crossover, indicating a bullish trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Stock price above both moving averages, suggesting long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:35.644844", "processing_time": 6.581166, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "interpretation": "Positive crossover, indicating a bullish trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Stock price above both moving averages, suggesting long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:35.644844", "processing_time": 6.581166, "llm_used": true}, "processing_time": 6.581166, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 149.31153200000003}}, {"timestamp": "2025-07-05T22:25:36.472118", "output_id": "output_20250705_222536_941d44b8", "input_id": "input_20250705_222531_08eef1d4", "prompt_id": "prompt_20250705_222531_19260ba8", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70.5, "analysis": "RSI is above 70, indicating the stock may be overbought."}, "MACD": {"signal_line": 15.3, "histogram": 2.1, "analysis": "MACD histogram is positive and above the signal line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:36.472118", "processing_time": 5.157464, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70.5, "analysis": "RSI is above 70, indicating the stock may be overbought."}, "MACD": {"signal_line": 15.3, "histogram": 2.1, "analysis": "MACD histogram is positive and above the signal line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:36.472118", "processing_time": 5.157464, "llm_used": true}, "processing_time": 5.157464, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 154.46899600000003}}, {"timestamp": "2025-07-05T22:25:39.501833", "output_id": "output_20250705_222539_6dc8c66c", "input_id": "input_20250705_222535_4d2e5411", "prompt_id": "prompt_20250705_222535_f06f0d09", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "interpretation": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:39.466276", "processing_time": 4.097893, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "interpretation": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:39.466276", "processing_time": 4.097893, "llm_used": true}, "processing_time": 4.097893, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 158.56688900000003}}, {"timestamp": "2025-07-05T22:25:40.407547", "output_id": "output_20250705_222540_be3bb287", "input_id": "input_20250705_222534_ee9e2406", "prompt_id": "prompt_20250705_222534_3b2245c9", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "Positive crossover, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Price above both moving averages, suggesting long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:40.407547", "processing_time": 5.88338, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "Positive crossover, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Price above both moving averages, suggesting long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:40.407547", "processing_time": 5.88338, "llm_used": true}, "processing_time": 5.88338, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 164.45026900000002}}, {"timestamp": "2025-07-05T22:25:41.307600", "output_id": "output_20250705_222541_8b1d0464", "input_id": "input_20250705_222536_29424994", "prompt_id": "prompt_20250705_222536_6ac9b879", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "interpretation": "Indicates the stock is neither overbought nor oversold, suggesting a neutral trend."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "MACD signal line is close to the zero line, indicating no clear trend, and the histogram is below zero, suggesting a neutral trend."}, "Moving_Average": {"50_day_MA": 153.5, "200_day_MA": 145.0, "interpretation": "The stock is currently between its 50-day and 200-day moving averages, indicating a lack of a strong trend in either direction."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:41.307600", "processing_time": 5.30289, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "interpretation": "Indicates the stock is neither overbought nor oversold, suggesting a neutral trend."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "MACD signal line is close to the zero line, indicating no clear trend, and the histogram is below zero, suggesting a neutral trend."}, "Moving_Average": {"50_day_MA": 153.5, "200_day_MA": 145.0, "interpretation": "The stock is currently between its 50-day and 200-day moving averages, indicating a lack of a strong trend in either direction."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:41.307600", "processing_time": 5.30289, "llm_used": true}, "processing_time": 5.30289, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 169.753159}}, {"timestamp": "2025-07-05T22:25:41.602796", "output_id": "output_20250705_222541_b713cba5", "input_id": "input_20250705_222534_e6642d41", "prompt_id": "prompt_20250705_222534_95b96e59", "raw_response": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "Buy signal - crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "Short-term MA above long-term MA, indicating bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:41.602796", "processing_time": 7.204827, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "interpretation": "Buy signal - crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "Short-term MA above long-term MA, indicating bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:41.602796", "processing_time": 7.204827, "llm_used": true}, "processing_time": 7.204827, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 176.957986}}, {"timestamp": "2025-07-05T22:25:42.817540", "output_id": "output_20250705_222542_29f15a24", "input_id": "input_20250705_222536_a0fafcb7", "prompt_id": "prompt_20250705_222536_8e776e46", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "price above long-term MA, near short-term MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:42.817540", "processing_time": 6.591134, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "price above long-term MA, near short-term MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:42.817540", "processing_time": 6.591134, "llm_used": true}, "processing_time": 6.591134, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 183.54912000000002}}, {"timestamp": "2025-07-05T22:25:47.521150", "output_id": "output_20250705_222547_00b81975", "input_id": "input_20250705_222542_d3e6569c", "prompt_id": "prompt_20250705_222542_f968a802", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.3, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:47.521150", "processing_time": 4.815181, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.3, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:47.521150", "processing_time": 4.815181, "llm_used": true}, "processing_time": 4.815181, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 188.364301}}, {"timestamp": "2025-07-05T22:25:48.602099", "output_id": "output_20250705_222548_1ac8324c", "input_id": "input_20250705_222540_31cb4373", "prompt_id": "prompt_20250705_222540_8e6b66af", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:48.602099", "processing_time": 7.876155, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:48.602099", "processing_time": 7.876155, "llm_used": true}, "processing_time": 7.876155, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 196.24045600000002}}, {"timestamp": "2025-07-05T22:25:49.176549", "output_id": "output_20250705_222549_6b063acb", "input_id": "input_20250705_222544_3c877665", "prompt_id": "prompt_20250705_222544_c5f7dd50", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "analysis": "The MACD signal line is above the zero line, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The 50-day moving average is above the 200-day moving average, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:49.142989", "processing_time": 5.13462, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "analysis": "The MACD signal line is above the zero line, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The 50-day moving average is above the 200-day moving average, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:49.142989", "processing_time": 5.13462, "llm_used": true}, "processing_time": 5.13462, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 201.37507600000004}}, {"timestamp": "2025-07-05T22:25:51.122147", "output_id": "output_20250705_222551_2238b470", "input_id": "input_20250705_222537_4f85de63", "prompt_id": "prompt_20250705_222537_857f0b6d", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:51.122147", "processing_time": 13.471882, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:51.122147", "processing_time": 13.471882, "llm_used": true}, "processing_time": 13.471882, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 214.84695800000003}}, {"timestamp": "2025-07-05T22:25:52.975067", "output_id": "output_20250705_222552_c7def2b3", "input_id": "input_20250705_222544_3801a010", "prompt_id": "prompt_20250705_222544_f952ad09", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 70, indicating the stock is in an overbought condition, but the trend is strong."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "The MACD signal line is positive and above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:52.975067", "processing_time": 8.099006, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 70, indicating the stock is in an overbought condition, but the trend is strong."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "The MACD signal line is positive and above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:25:52.975067", "processing_time": 8.099006, "llm_used": true}, "processing_time": 8.099006, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 222.94596400000003}}]