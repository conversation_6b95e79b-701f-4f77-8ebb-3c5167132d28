[{"timestamp": "2025-07-05T22:42:14.340213", "output_id": "output_20250705_224214_6926d22c", "input_id": "input_20250705_224205_86873b6d", "prompt_id": "prompt_20250705_224205_8d8540a2", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "comment": "RSI is neutral, indicating that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "comment": "MACD is slightly bullish as the signal line is above the MACD line and the histogram is positive."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "The stock is currently above the 50-day MA but below the 200-day MA, suggesting a short-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:42:14.340213", "processing_time": 8.844978, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "comment": "RSI is neutral, indicating that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "comment": "MACD is slightly bullish as the signal line is above the MACD line and the histogram is positive."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "The stock is currently above the 50-day MA but below the 200-day MA, suggesting a short-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:42:14.340213", "processing_time": 8.844978, "llm_used": true}, "processing_time": 8.844978, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 8.844978}}, {"timestamp": "2025-07-05T22:43:05.065236", "output_id": "output_20250705_224305_5d30b33e", "input_id": "input_20250705_224300_cb72c360", "prompt_id": "prompt_20250705_224301_780ba684", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:05.065236", "processing_time": 4.670126, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:05.065236", "processing_time": 4.670126, "llm_used": true}, "processing_time": 4.670126, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 13.515104}}, {"timestamp": "2025-07-05T22:43:05.897520", "output_id": "output_20250705_224305_9d8b4f03", "input_id": "input_20250705_224300_8273baa0", "prompt_id": "prompt_20250705_224300_8fa7233e", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 61, "analysis": "RSI is in the middle of the range, indicating neither overbought nor oversold conditions."}, "MACD": {"signal_line": 0.02, "histogram": -0.03, "analysis": "The MACD signal line is slightly positive, suggesting a neutral trend. The histogram is negative, which suggests a slight bearish bias."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently below its 50-day moving average but above its 200-day moving average, indicating a potential long-term bullish trend but a short-term neutral to bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:05.897520", "processing_time": 5.70903, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 61, "analysis": "RSI is in the middle of the range, indicating neither overbought nor oversold conditions."}, "MACD": {"signal_line": 0.02, "histogram": -0.03, "analysis": "The MACD signal line is slightly positive, suggesting a neutral trend. The histogram is negative, which suggests a slight bearish bias."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently below its 50-day moving average but above its 200-day moving average, indicating a potential long-term bullish trend but a short-term neutral to bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:05.897520", "processing_time": 5.70903, "llm_used": true}, "processing_time": 5.70903, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 19.224134}}, {"timestamp": "2025-07-05T22:43:06.152349", "output_id": "output_20250705_224306_8c59c27d", "input_id": "input_20250705_224300_2ab0d0d5", "prompt_id": "prompt_20250705_224300_ffedef49", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.1, "historical_line": 0.08, "interpretation": "Signal line above historical line, bullish trend"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "interpretation": "50-day MA crossing above 200-day MA, long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:06.152349", "processing_time": 6.086221, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.1, "historical_line": 0.08, "interpretation": "Signal line above historical line, bullish trend"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "interpretation": "50-day MA crossing above 200-day MA, long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:06.152349", "processing_time": 6.086221, "llm_used": true}, "processing_time": 6.086221, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 25.310355}}, {"timestamp": "2025-07-05T22:43:06.480481", "output_id": "output_20250705_224306_783cd1cc", "input_id": "input_20250705_224300_05e3728d", "prompt_id": "prompt_20250705_224301_932a72da", "raw_response": {"analysis_date": "2025-01-16", "trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "moderately overbought, suggesting a possible pullback"}, "MACD": {"signal_line": 0.002, "histogram": 0.01, "interpretation": "MACD line is above the signal line, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "interpretation": "50-day MA crossed above the 200-day MA, indicating a long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:06.480481", "processing_time": 6.035307, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-16", "trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "moderately overbought, suggesting a possible pullback"}, "MACD": {"signal_line": 0.002, "histogram": 0.01, "interpretation": "MACD line is above the signal line, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "interpretation": "50-day MA crossed above the 200-day MA, indicating a long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:06.480481", "processing_time": 6.035307, "llm_used": true}, "processing_time": 6.035307, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 31.345662}}, {"timestamp": "2025-07-05T22:43:07.635487", "output_id": "output_20250705_224307_e3615088", "input_id": "input_20250705_224300_d2fde7c1", "prompt_id": "prompt_20250705_224300_766617a0", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "analysis": "RSI is neutral, indicating no overbought or oversold conditions."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD signal line is slightly bullish, suggesting a minor upward trend."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 170.0, "analysis": "The stock is currently trading below its 50-Day MA but above its 200-Day MA, suggesting a long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:07.635487", "processing_time": 7.365594, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "analysis": "RSI is neutral, indicating no overbought or oversold conditions."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD signal line is slightly bullish, suggesting a minor upward trend."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 170.0, "analysis": "The stock is currently trading below its 50-Day MA but above its 200-Day MA, suggesting a long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:07.635487", "processing_time": 7.365594, "llm_used": true}, "processing_time": 7.365594, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 38.711256}}, {"timestamp": "2025-07-05T22:43:08.014428", "output_id": "output_20250705_224308_d913e6cd", "input_id": "input_20250705_224300_c6a7f570", "prompt_id": "prompt_20250705_224300_0b98e311", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 50, "interpretation": "Neutral, market not overbought or oversold."}, "MACD": {"signal_line": 0.0, "histogram": -0.02, "interpretation": "Slightly bearish, but not strong enough to indicate a major trend reversal."}, "Moving_Average": {"50-Day_MA": 155.0, "200-Day_MA": 175.0, "interpretation": "Price is currently between the 50-Day and 200-Day Moving Averages, indicating a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:08.014428", "processing_time": 7.949293, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 50, "interpretation": "Neutral, market not overbought or oversold."}, "MACD": {"signal_line": 0.0, "histogram": -0.02, "interpretation": "Slightly bearish, but not strong enough to indicate a major trend reversal."}, "Moving_Average": {"50-Day_MA": 155.0, "200-Day_MA": 175.0, "interpretation": "Price is currently between the 50-Day and 200-Day Moving Averages, indicating a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:08.014428", "processing_time": 7.949293, "llm_used": true}, "processing_time": 7.949293, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 46.660548999999996}}, {"timestamp": "2025-07-05T22:43:08.098867", "output_id": "output_20250705_224308_b40773a0", "input_id": "input_20250705_224300_a8eca178", "prompt_id": "prompt_20250705_224300_c2f9f073", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 57, "analysis": "RSI is slightly above 50, indicating a neutral market condition."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD signal line is close to zero and the histogram is negative, suggesting a slight bearish trend."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 165.0, "analysis": "The stock is currently trading below its 50-day and 200-day moving averages, which are bearish signals."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:08.098867", "processing_time": 7.881296, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 57, "analysis": "RSI is slightly above 50, indicating a neutral market condition."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD signal line is close to zero and the histogram is negative, suggesting a slight bearish trend."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 165.0, "analysis": "The stock is currently trading below its 50-day and 200-day moving averages, which are bearish signals."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:08.098867", "processing_time": 7.881296, "llm_used": true}, "processing_time": 7.881296, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 54.541844999999995}}, {"timestamp": "2025-07-05T22:43:08.115980", "output_id": "output_20250705_224308_19899ccb", "input_id": "input_20250705_224300_c660d2fa", "prompt_id": "prompt_20250705_224301_5dd834a0", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 54.5, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "crossover": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:08.114979", "processing_time": 7.716863, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 54.5, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "crossover": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:08.114979", "processing_time": 7.716863, "llm_used": true}, "processing_time": 7.716863, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 62.258708}}, {"timestamp": "2025-07-05T22:43:11.494022", "output_id": "output_20250705_224311_193f3ea8", "input_id": "input_20250705_224307_008e4dcd", "prompt_id": "prompt_20250705_224307_b3ad51a4", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 153.0, "200_day_MA": 148.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:11.494022", "processing_time": 4.305792, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 153.0, "200_day_MA": 148.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:11.494022", "processing_time": 4.305792, "llm_used": true}, "processing_time": 4.305792, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 66.5645}}, {"timestamp": "2025-07-05T22:43:12.342089", "output_id": "output_20250705_224312_b0fbeb66", "input_id": "input_20250705_224308_6f737773", "prompt_id": "prompt_20250705_224308_c2da436d", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:12.342089", "processing_time": 4.122179, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:12.342089", "processing_time": 4.122179, "llm_used": true}, "processing_time": 4.122179, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 70.686679}}, {"timestamp": "2025-07-05T22:43:12.627388", "output_id": "output_20250705_224312_c90dcfb1", "input_id": "input_20250705_224306_04cec958", "prompt_id": "prompt_20250705_224306_67ddbc14", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": {"bullish": false, "bearish": false}}, "moving_averages": {"50_day_MA": 152.0, "200_day_MA": 160.0}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:12.626445", "processing_time": 5.910224, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": {"bullish": false, "bearish": false}}, "moving_averages": {"50_day_MA": 152.0, "200_day_MA": 160.0}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:12.626445", "processing_time": 5.910224, "llm_used": true}, "processing_time": 5.910224, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 76.596903}}, {"timestamp": "2025-07-05T22:43:13.256456", "output_id": "output_20250705_224313_86501fab", "input_id": "input_20250705_224305_d4491e29", "prompt_id": "prompt_20250705_224305_af3bee22", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 70, indicating the stock is overbought and may be due for a pullback."}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "The MACD is close to the signal line with a small positive histogram, suggesting a neutral trend but with a slight bullish bias."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:13.256456", "processing_time": 7.851036, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is above 70, indicating the stock is overbought and may be due for a pullback."}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "The MACD is close to the signal line with a small positive histogram, suggesting a neutral trend but with a slight bullish bias."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:13.256456", "processing_time": 7.851036, "llm_used": true}, "processing_time": 7.851036, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 84.44793899999999}}, {"timestamp": "2025-07-05T22:43:13.527988", "output_id": "output_20250705_224313_264ded9f", "input_id": "input_20250705_224307_7680924b", "prompt_id": "prompt_20250705_224307_173bbd6e", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68.5, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.3, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:13.527988", "processing_time": 6.049882, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68.5, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.3, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:13.527988", "processing_time": 6.049882, "llm_used": true}, "processing_time": 6.049882, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 90.49782099999999}}, {"timestamp": "2025-07-05T22:43:14.372377", "output_id": "output_20250705_224314_2cf1b7d2", "input_id": "input_20250705_224307_6c21b7bf", "prompt_id": "prompt_20250705_224307_0c1797c7", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 10, "histogram": {"bullish": true, "current_value": 5}}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:14.372377", "processing_time": 7.231004, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 10, "histogram": {"bullish": true, "current_value": 5}}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:14.372377", "processing_time": 7.231004, "llm_used": true}, "processing_time": 7.231004, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 97.72882499999999}}, {"timestamp": "2025-07-05T22:43:14.404337", "output_id": "output_20250705_224314_34239893", "input_id": "input_20250705_224307_0566933a", "prompt_id": "prompt_20250705_224307_7943b6f4", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI is in the middle of the overbought zone, suggesting a possible pullback."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "comment": "MACD is above the signal line, indicating bullish momentum."}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 145.0, "comment": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:14.404337", "processing_time": 7.204378, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "comment": "RSI is in the middle of the overbought zone, suggesting a possible pullback."}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "comment": "MACD is above the signal line, indicating bullish momentum."}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 145.0, "comment": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:14.404337", "processing_time": 7.204378, "llm_used": true}, "processing_time": 7.204378, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 104.93320299999999}}, {"timestamp": "2025-07-05T22:43:14.805817", "output_id": "output_20250705_224314_55e4e841", "input_id": "input_20250705_224309_10c028e8", "prompt_id": "prompt_20250705_224309_76a21f1c", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.1, "histogram": 0.3, "analysis": "MACD signal line is above the zero line with a rising histogram, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:14.805817", "processing_time": 5.283087, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.1, "histogram": 0.3, "analysis": "MACD signal line is above the zero line with a rising histogram, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:14.805817", "processing_time": 5.283087, "llm_used": true}, "processing_time": 5.283087, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 110.21628999999999}}, {"timestamp": "2025-07-05T22:43:16.173310", "output_id": "output_20250705_224316_e5622fa7", "input_id": "input_20250705_224309_8171c4a3", "prompt_id": "prompt_20250705_224309_a2dff582", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"value": 68, "interpretation": "overbought"}, "MACD": {"signal": "bullish crossover", "interpretation": "strengthening trend"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 120.0, "interpretation": "50-Day MA above 200-Day MA, bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:16.173310", "processing_time": 6.472028, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"value": 68, "interpretation": "overbought"}, "MACD": {"signal": "bullish crossover", "interpretation": "strengthening trend"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 120.0, "interpretation": "50-Day MA above 200-Day MA, bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:16.173310", "processing_time": 6.472028, "llm_used": true}, "processing_time": 6.472028, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 116.68831799999998}}, {"timestamp": "2025-07-05T22:43:16.198358", "output_id": "output_20250705_224316_b7c046e7", "input_id": "input_20250705_224306_f911f130", "prompt_id": "prompt_20250705_224306_dd55a557", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought but still in a strong uptrend."}, "MACD": {"current_value": 0.1, "analysis": "MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 135.0, "analysis": "The stock is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:16.195356", "processing_time": 9.514984, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought but still in a strong uptrend."}, "MACD": {"current_value": 0.1, "analysis": "MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 135.0, "analysis": "The stock is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:16.195356", "processing_time": 9.514984, "llm_used": true}, "processing_time": 9.514984, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 126.20330199999998}}, {"timestamp": "2025-07-05T22:43:28.183073", "output_id": "output_20250705_224328_c273fecd", "input_id": "input_20250705_224323_1638399b", "prompt_id": "prompt_20250705_224323_a354d12d", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "signal": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:28.183073", "processing_time": 4.745038, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "signal": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:28.183073", "processing_time": 4.745038, "llm_used": true}, "processing_time": 4.745038, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 130.94833999999997}}, {"timestamp": "2025-07-05T22:43:30.178994", "output_id": "output_20250705_224330_1f93c83e", "input_id": "input_20250705_224322_ebc27cb0", "prompt_id": "prompt_20250705_224322_2f198d23", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:30.178994", "processing_time": 7.400164, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:30.178994", "processing_time": 7.400164, "llm_used": true}, "processing_time": 7.400164, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 138.34850399999996}}, {"timestamp": "2025-07-05T22:43:31.659955", "output_id": "output_20250705_224331_f982807a", "input_id": "input_20250705_224325_82c0c432", "prompt_id": "prompt_20250705_224325_fee99b8a", "raw_response": {"trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "indicating a neutral trend"}, "MACD": {"signal_line": 0.001, "historical_line": -0.002, "interpretation": "indicating a slight bearish trend"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "interpretation": "50-day MA crossing above 200-day MA, suggesting a bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:31.659955", "processing_time": 6.239783, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "indicating a neutral trend"}, "MACD": {"signal_line": 0.001, "historical_line": -0.002, "interpretation": "indicating a slight bearish trend"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "interpretation": "50-day MA crossing above 200-day MA, suggesting a bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:31.659955", "processing_time": 6.239783, "llm_used": true}, "processing_time": 6.239783, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 144.58828699999995}}, {"timestamp": "2025-07-05T22:43:33.698180", "output_id": "output_20250705_224333_7b803aeb", "input_id": "input_20250705_224329_963daafa", "prompt_id": "prompt_20250705_224329_f14a1a40", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA, indicating a bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:33.697175", "processing_time": 4.657987, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA, indicating a bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:33.697175", "processing_time": 4.657987, "llm_used": true}, "processing_time": 4.657987, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 149.24627399999994}}, {"timestamp": "2025-07-05T22:43:36.261969", "output_id": "output_20250705_224336_698a6c1f", "input_id": "input_20250705_224330_4a4d7a60", "prompt_id": "prompt_20250705_224330_68364bcd", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is currently above 50, indicating that the stock is neither overbought nor oversold."}, "MACD": {"current_value": "0.02", "analysis": "The MACD line is close to the signal line, suggesting that the trend is neutral."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, indicating a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:36.260972", "processing_time": 5.956663, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is currently above 50, indicating that the stock is neither overbought nor oversold."}, "MACD": {"current_value": "0.02", "analysis": "The MACD line is close to the signal line, suggesting that the trend is neutral."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, indicating a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:36.260972", "processing_time": 5.956663, "llm_used": true}, "processing_time": 5.956663, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 155.20293699999993}}, {"timestamp": "2025-07-05T22:43:40.098863", "output_id": "output_20250705_224340_a050d27d", "input_id": "input_20250705_224330_f62585b0", "prompt_id": "prompt_20250705_224330_ec1236bc", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "slightly above neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "weak bullish signal"}, "moving_averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "short-term MA above long-term MA, suggesting a potential bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:40.098863", "processing_time": 9.485046, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "slightly above neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "weak bullish signal"}, "moving_averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "short-term MA above long-term MA, suggesting a potential bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:40.098863", "processing_time": 9.485046, "llm_used": true}, "processing_time": 9.485046, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 164.68798299999995}}, {"timestamp": "2025-07-05T22:43:40.886460", "output_id": "output_20250705_224340_059d37d3", "input_id": "input_20250705_224332_cc1197da", "prompt_id": "prompt_20250705_224332_94c02290", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60.5, "analysis": "RSI is above 50, indicating a neutral to slightly bullish trend."}, "MACD": {"signal_line": 0.02, "analysis": "MACD signal line is slightly above the zero line, suggesting a neutral trend."}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "analysis": "The stock is currently trading above the 50-day MA but below the 200-day MA, suggesting a short-term bullish but long-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:40.886460", "processing_time": 8.533812, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60.5, "analysis": "RSI is above 50, indicating a neutral to slightly bullish trend."}, "MACD": {"signal_line": 0.02, "analysis": "MACD signal line is slightly above the zero line, suggesting a neutral trend."}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "analysis": "The stock is currently trading above the 50-day MA but below the 200-day MA, suggesting a short-term bullish but long-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:40.886460", "processing_time": 8.533812, "llm_used": true}, "processing_time": 8.533812, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 173.22179499999996}}, {"timestamp": "2025-07-05T22:43:42.006454", "output_id": "output_20250705_224342_09a60d53", "input_id": "input_20250705_224333_58fc9b60", "prompt_id": "prompt_20250705_224333_b7d59631", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"value": 69, "interpretation": "The RSI is above 70, indicating the stock is overbought and could be due for a pullback."}, "MACD": {"signal_line": 0.02, "interpretation": "The MACD line is above the signal line, showing a bullish trend."}, "Moving Averages": {"50-Day MA": 155.0, "200-Day MA": 180.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:42.006454", "processing_time": 8.138752, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"value": 69, "interpretation": "The RSI is above 70, indicating the stock is overbought and could be due for a pullback."}, "MACD": {"signal_line": 0.02, "interpretation": "The MACD line is above the signal line, showing a bullish trend."}, "Moving Averages": {"50-Day MA": 155.0, "200-Day MA": 180.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:42.006454", "processing_time": 8.138752, "llm_used": true}, "processing_time": 8.138752, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 181.36054699999997}}, {"timestamp": "2025-07-05T22:43:42.906521", "output_id": "output_20250705_224342_64202ccd", "input_id": "input_20250705_224336_b9d14910", "prompt_id": "prompt_20250705_224336_e6dde88f", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "analysis": "Neutral, suggesting no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "analysis": "MACD line is close to the signal line with a small negative histogram, indicating a slight bearish trend but not a strong one."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently trading above the 50-day MA but below the 200-day MA, suggesting a short-term bullish trend with a long-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:42.906521", "processing_time": 6.508091, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "analysis": "Neutral, suggesting no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.1, "histogram": -0.05, "analysis": "MACD line is close to the signal line with a small negative histogram, indicating a slight bearish trend but not a strong one."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently trading above the 50-day MA but below the 200-day MA, suggesting a short-term bullish trend with a long-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:42.906521", "processing_time": 6.508091, "llm_used": true}, "processing_time": 6.508091, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 187.86863799999998}}, {"timestamp": "2025-07-05T22:43:43.657260", "output_id": "output_20250705_224343_16ca5557", "input_id": "input_20250705_224332_2cee664c", "prompt_id": "prompt_20250705_224332_7432335f", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is in the middle of the range, suggesting no strong overbought or oversold conditions."}, "MACD": {"current_value": 0.02, "analysis": "MACD line is close to the signal line, indicating a lack of strong momentum in either direction."}, "Moving_Average": {"50-Day_MA": 155.0, "200-Day_MA": 165.0, "analysis": "The stock is currently below its 50-day moving average and well below its 200-day moving average, suggesting a long-term bearish trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:43.657260", "processing_time": 11.118706, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is in the middle of the range, suggesting no strong overbought or oversold conditions."}, "MACD": {"current_value": 0.02, "analysis": "MACD line is close to the signal line, indicating a lack of strong momentum in either direction."}, "Moving_Average": {"50-Day_MA": 155.0, "200-Day_MA": 165.0, "analysis": "The stock is currently below its 50-day moving average and well below its 200-day moving average, suggesting a long-term bearish trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:43.657260", "processing_time": 11.118706, "llm_used": true}, "processing_time": 11.118706, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 198.98734399999998}}, {"timestamp": "2025-07-05T22:43:44.410193", "output_id": "output_20250705_224344_71653438", "input_id": "input_20250705_224337_0b3cd78c", "prompt_id": "prompt_20250705_224337_e1314950", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "interpretation": "MACD line above signal line, indicating bullish momentum"}, "Moving_Average": {"50_day_MA": 153.0, "200_day_MA": 145.0, "interpretation": "price above both 50-day and 200-day moving averages, long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:44.410193", "processing_time": 6.518991, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "interpretation": "MACD line above signal line, indicating bullish momentum"}, "Moving_Average": {"50_day_MA": 153.0, "200_day_MA": 145.0, "interpretation": "price above both 50-day and 200-day moving averages, long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:44.410193", "processing_time": 6.518991, "llm_used": true}, "processing_time": 6.518991, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 205.50633499999998}}, {"timestamp": "2025-07-05T22:43:47.332982", "output_id": "output_20250705_224347_6917a872", "input_id": "input_20250705_224335_3889e6e9", "prompt_id": "prompt_20250705_224335_eab662f4", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56, "interpretation": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 150.0, "crossover": "50_day_MA crossing above 200_day_MA", "interpretation": "slightly bullish"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:47.332982", "processing_time": 11.922183, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56, "interpretation": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 150.0, "crossover": "50_day_MA crossing above 200_day_MA", "interpretation": "slightly bullish"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:47.332982", "processing_time": 11.922183, "llm_used": true}, "processing_time": 11.922183, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 217.42851799999997}}, {"timestamp": "2025-07-05T22:43:48.031284", "output_id": "output_20250705_224348_f3dcc4d8", "input_id": "input_20250705_224339_451e1f7f", "prompt_id": "prompt_20250705_224339_5e0a3303", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 53, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "No clear trend"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "Slightly bullish crossover"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:48.030280", "processing_time": 8.112957, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 53, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": "No clear trend"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "Slightly bullish crossover"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:48.030280", "processing_time": 8.112957, "llm_used": true}, "processing_time": 8.112957, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 225.54147499999996}}, {"timestamp": "2025-07-05T22:43:48.442380", "output_id": "output_20250705_224348_a140b7b8", "input_id": "input_20250705_224340_7379122e", "prompt_id": "prompt_20250705_224340_b0bf9fda", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 60.5, "analysis": "RSI is slightly above 50, indicating a neutral trend."}, "MACD": {"signal_line": 0.0, "histogram": -0.01, "analysis": "MACD line is close to the signal line with a slight bearish crossover, suggesting a possible short-term downward trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "Stock is currently below its 50-day MA and above its 200-day MA, indicating a neutral to bullish trend in the long term."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:48.442380", "processing_time": 7.755834, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 60.5, "analysis": "RSI is slightly above 50, indicating a neutral trend."}, "MACD": {"signal_line": 0.0, "histogram": -0.01, "analysis": "MACD line is close to the signal line with a slight bearish crossover, suggesting a possible short-term downward trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "Stock is currently below its 50-day MA and above its 200-day MA, indicating a neutral to bullish trend in the long term."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:43:48.442380", "processing_time": 7.755834, "llm_used": true}, "processing_time": 7.755834, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 233.29730899999996}}]