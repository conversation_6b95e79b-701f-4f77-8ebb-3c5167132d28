[{"timestamp": "2025-07-05T22:17:55.449971", "output_id": "output_20250705_221755_82d3d90f", "input_id": "input_20250705_221751_7792ff88", "prompt_id": "prompt_20250705_221751_6acea50c", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60.5, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 148.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:17:55.449845", "processing_time": 3.872798, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60.5, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 148.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:17:55.449845", "processing_time": 3.872798, "llm_used": true}, "processing_time": 3.872798, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 3.872798}}, {"timestamp": "2025-07-05T22:18:47.026790", "output_id": "output_20250705_221847_7d857d5c", "input_id": "input_20250705_221842_284ea197", "prompt_id": "prompt_20250705_221842_57a4a1be", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:47.026790", "processing_time": 4.208744, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:47.026790", "processing_time": 4.208744, "llm_used": true}, "processing_time": 4.208744, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 8.081542}}, {"timestamp": "2025-07-05T22:18:48.187904", "output_id": "output_20250705_221848_c7c35cae", "input_id": "input_20250705_221843_22b01c46", "prompt_id": "prompt_20250705_221843_9b68445b", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought."}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "analysis": "MACD signal line is positive and above the zero line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:48.180911", "processing_time": 5.281006, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought."}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "analysis": "MACD signal line is positive and above the zero line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:48.180911", "processing_time": 5.281006, "llm_used": true}, "processing_time": 5.281006, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 13.362548}}, {"timestamp": "2025-07-05T22:18:49.075678", "output_id": "output_20250705_221849_2e8f131d", "input_id": "input_20250705_221842_40d77516", "prompt_id": "prompt_20250705_221843_7b76cd63", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "The RSI is above 70, indicating the stock is overbought and may be due for a pullback."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "The MACD line is above the signal line and the histogram is positive, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 145, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:49.075678", "processing_time": 6.226085, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "The RSI is above 70, indicating the stock is overbought and may be due for a pullback."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "The MACD line is above the signal line and the histogram is positive, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 145, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:49.075678", "processing_time": 6.226085, "llm_used": true}, "processing_time": 6.226085, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 19.588633}}, {"timestamp": "2025-07-05T22:18:49.365145", "output_id": "output_20250705_221849_7944bbea", "input_id": "input_20250705_221843_700ed230", "prompt_id": "prompt_20250705_221843_7c6cc51e", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.4, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "The RSI is neither overbought nor oversold, indicating a neutral trend."}, "MACD": {"signal_line": 0, "analysis": "The MACD line is close to the signal line, suggesting no clear trend direction."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 170.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, indicating a lack of strong trend direction."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:49.365145", "processing_time": 6.413688, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.4, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "analysis": "The RSI is neither overbought nor oversold, indicating a neutral trend."}, "MACD": {"signal_line": 0, "analysis": "The MACD line is close to the signal line, suggesting no clear trend direction."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 170.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, indicating a lack of strong trend direction."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:49.365145", "processing_time": 6.413688, "llm_used": true}, "processing_time": 6.413688, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 26.002321000000002}}, {"timestamp": "2025-07-05T22:18:49.519878", "output_id": "output_20250705_221849_63636891", "input_id": "input_20250705_221843_c98de4ab", "prompt_id": "prompt_20250705_221843_c5c7ce3e", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60.0, "analysis": "Neutral - The RSI is above 50 but below 70, suggesting a balanced market."}, "MACD": {"current_value": "0.00", "analysis": "Neutral - The MACD line is close to the signal line, indicating a lack of strong momentum."}, "Moving_Averages": {"50_MA": 153.0, "200_MA": 165.0, "analysis": "Neutral - The stock is trading between its 50-day and 200-day moving averages, suggesting a stable trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:49.519878", "processing_time": 6.529244, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60.0, "analysis": "Neutral - The RSI is above 50 but below 70, suggesting a balanced market."}, "MACD": {"current_value": "0.00", "analysis": "Neutral - The MACD line is close to the signal line, indicating a lack of strong momentum."}, "Moving_Averages": {"50_MA": 153.0, "200_MA": 165.0, "analysis": "Neutral - The stock is trading between its 50-day and 200-day moving averages, suggesting a stable trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:49.519878", "processing_time": 6.529244, "llm_used": true}, "processing_time": 6.529244, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 32.531565}}, {"timestamp": "2025-07-05T22:18:50.045248", "output_id": "output_20250705_221850_434cb4b3", "input_id": "input_20250705_221842_9dcfbdf2", "prompt_id": "prompt_20250705_221842_c009e434", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "interpretation": "MACD line crossing above signal line indicates bullish trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above 50-day MA and 200-day MA, indicating long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:50.045248", "processing_time": 7.263365, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "interpretation": "MACD line crossing above signal line indicates bullish trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above 50-day MA and 200-day MA, indicating long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:50.045248", "processing_time": 7.263365, "llm_used": true}, "processing_time": 7.263365, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 39.79493}}, {"timestamp": "2025-07-05T22:18:50.561966", "output_id": "output_20250705_221850_5ae13e93", "input_id": "input_20250705_221843_b9022443", "prompt_id": "prompt_20250705_221843_9c2c16ea", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "crossover above 200_day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:50.561966", "processing_time": 7.533123, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "crossover above 200_day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:50.561966", "processing_time": 7.533123, "llm_used": true}, "processing_time": 7.533123, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 47.328053}}, {"timestamp": "2025-07-05T22:18:52.393738", "output_id": "output_20250705_221852_de8830e6", "input_id": "input_20250705_221842_a28a7c8a", "prompt_id": "prompt_20250705_221842_be6993a0", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:52.393738", "processing_time": 9.591225, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:52.393738", "processing_time": 9.591225, "llm_used": true}, "processing_time": 9.591225, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 56.919278}}, {"timestamp": "2025-07-05T22:18:53.004143", "output_id": "output_20250705_221853_ef0bf90b", "input_id": "input_20250705_221848_2a7a3298", "prompt_id": "prompt_20250705_221848_572848e1", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 135.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:53.004143", "processing_time": 4.498497, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 135.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:53.004143", "processing_time": 4.498497, "llm_used": true}, "processing_time": 4.498497, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 61.417775}}, {"timestamp": "2025-07-05T22:18:53.308437", "output_id": "output_20250705_221853_54a51aa6", "input_id": "input_20250705_221848_7f0b283e", "prompt_id": "prompt_20250705_221848_2c9a1931", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"current_value": "positive", "analysis": "MACD is showing a bullish crossover, suggesting an upward trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:53.308437", "processing_time": 4.503205, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"current_value": "positive", "analysis": "MACD is showing a bullish crossover, suggesting an upward trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, which is a bullish sign."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:53.308437", "processing_time": 4.503205, "llm_used": true}, "processing_time": 4.503205, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 65.92098}}, {"timestamp": "2025-07-05T22:18:54.348276", "output_id": "output_20250705_221854_583c02df", "input_id": "input_20250705_221848_2ad53841", "prompt_id": "prompt_20250705_221848_769da2e0", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:54.348276", "processing_time": 5.895969, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:54.348276", "processing_time": 5.895969, "llm_used": true}, "processing_time": 5.895969, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 71.816949}}, {"timestamp": "2025-07-05T22:18:54.916858", "output_id": "output_20250705_221854_813a924a", "input_id": "input_20250705_221850_15e95b63", "prompt_id": "prompt_20250705_221850_e8885134", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50_day_MA above 200_day_MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:54.916858", "processing_time": 4.217337, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "50_day_MA above 200_day_MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:54.916858", "processing_time": 4.217337, "llm_used": true}, "processing_time": 4.217337, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 76.034286}}, {"timestamp": "2025-07-05T22:18:55.151474", "output_id": "output_20250705_221855_6e0af1e3", "input_id": "input_20250705_221849_df5ee949", "prompt_id": "prompt_20250705_221849_3fa02ba2", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 69, "analysis": "The RSI is above 70, indicating that the stock is overbought and may be due for a pullback."}, "MACD": {"value": "positive crossover", "analysis": "The MACD line has crossed above the signal line, suggesting bullish momentum."}, "Moving Averages": {"50-day MA": "above price", "200-day MA": "above price", "analysis": "Long-term and short-term moving averages are both above the current price, indicating a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:55.151474", "processing_time": 5.566526, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 69, "analysis": "The RSI is above 70, indicating that the stock is overbought and may be due for a pullback."}, "MACD": {"value": "positive crossover", "analysis": "The MACD line has crossed above the signal line, suggesting bullish momentum."}, "Moving Averages": {"50-day MA": "above price", "200-day MA": "above price", "analysis": "Long-term and short-term moving averages are both above the current price, indicating a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:55.151474", "processing_time": 5.566526, "llm_used": true}, "processing_time": 5.566526, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 81.60081199999999}}, {"timestamp": "2025-07-05T22:18:55.467078", "output_id": "output_20250705_221855_40649d36", "input_id": "input_20250705_221850_b72461be", "prompt_id": "prompt_20250705_221850_accc7929", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 129.0, "resistance_level": 144.0, "indicators": {"RSI": {"current_value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 130.5, "200_day_MA": 120.0, "signal": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:55.467078", "processing_time": 4.993127, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 129.0, "resistance_level": 144.0, "indicators": {"RSI": {"current_value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 130.5, "200_day_MA": 120.0, "signal": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:55.467078", "processing_time": 4.993127, "llm_used": true}, "processing_time": 4.993127, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 86.59393899999999}}, {"timestamp": "2025-07-05T22:18:55.958369", "output_id": "output_20250705_221855_b89cfcc9", "input_id": "input_20250705_221848_1e7b4553", "prompt_id": "prompt_20250705_221848_16db0f20", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70.5, "analysis": "RSI is above 70, indicating overbought conditions, but the upward trend suggests continued strength."}, "MACD": {"current_value": 0.15, "analysis": "MACD line is above the signal line, indicating bullish momentum."}, "Moving Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, suggesting a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:55.958369", "processing_time": 7.720004, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70.5, "analysis": "RSI is above 70, indicating overbought conditions, but the upward trend suggests continued strength."}, "MACD": {"current_value": 0.15, "analysis": "MACD line is above the signal line, indicating bullish momentum."}, "Moving Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, suggesting a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:55.958369", "processing_time": 7.720004, "llm_used": true}, "processing_time": 7.720004, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 94.313943}}, {"timestamp": "2025-07-05T22:18:57.409776", "output_id": "output_20250705_221857_1a180d97", "input_id": "input_20250705_221849_047146cf", "prompt_id": "prompt_20250705_221849_37523359", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:57.409776", "processing_time": 8.003132, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:57.409776", "processing_time": 8.003132, "llm_used": true}, "processing_time": 8.003132, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 102.31707499999999}}, {"timestamp": "2025-07-05T22:18:57.702331", "output_id": "output_20250705_221857_ceaab2b8", "input_id": "input_20250705_221849_7bbdc5f4", "prompt_id": "prompt_20250705_221849_dc3dc517", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 70, indicating that the stock may be overbought."}, "MACD": {"signal_line": 0, "histogram": 0.02, "comment": "MACD is positive and crossing above the signal line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 140, "200_day_MA": 130, "comment": "The stock price is above both the 50-day and 200-day moving averages, supporting a bullish outlook."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:57.702331", "processing_time": 8.691657, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "comment": "RSI is above 70, indicating that the stock may be overbought."}, "MACD": {"signal_line": 0, "histogram": 0.02, "comment": "MACD is positive and crossing above the signal line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 140, "200_day_MA": 130, "comment": "The stock price is above both the 50-day and 200-day moving averages, supporting a bullish outlook."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:57.702331", "processing_time": 8.691657, "llm_used": true}, "processing_time": 8.691657, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 111.00873199999998}}, {"timestamp": "2025-07-05T22:18:58.186320", "output_id": "output_20250705_221858_f9b2116e", "input_id": "input_20250705_221851_3f8194af", "prompt_id": "prompt_20250705_221851_5039cad3", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "interpretation": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:58.138083", "processing_time": 6.194106, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "interpretation": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:18:58.138083", "processing_time": 6.194106, "llm_used": true}, "processing_time": 6.194106, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 117.20283799999999}}, {"timestamp": "2025-07-05T22:19:10.412559", "output_id": "output_20250705_221910_d39bb5b4", "input_id": "input_20250705_221904_bb3d356b", "prompt_id": "prompt_20250705_221904_dc00726d", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is above 70, indicating the stock may be overbought, but the overall trend is still bullish."}, "MACD": {"current_value": 0.2, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:10.402051", "processing_time": 6.321974, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "The RSI is above 70, indicating the stock may be overbought, but the overall trend is still bullish."}, "MACD": {"current_value": 0.2, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:10.402051", "processing_time": 6.321974, "llm_used": true}, "processing_time": 6.321974, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 123.52481199999998}}, {"timestamp": "2025-07-05T22:19:10.838427", "output_id": "output_20250705_221910_adae5ac2", "input_id": "input_20250705_221903_e6b288e0", "prompt_id": "prompt_20250705_221903_78c3564e", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:10.838427", "processing_time": 7.404707, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:10.838427", "processing_time": 7.404707, "llm_used": true}, "processing_time": 7.404707, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 130.92951899999997}}, {"timestamp": "2025-07-05T22:19:11.040809", "output_id": "output_20250705_221911_68d39166", "input_id": "input_20250705_221905_492a13cc", "prompt_id": "prompt_20250705_221905_1a0c7706", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 60, indicating that the stock may be overbought, but the positive sentiment from news could justify the high RSI."}, "MACD": {"current_value": "positive crossover", "analysis": "The MACD is showing a positive crossover, suggesting a bullish trend."}, "Moving Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "analysis": "The stock is currently above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:11.036270", "processing_time": 5.108036, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 60, indicating that the stock may be overbought, but the positive sentiment from news could justify the high RSI."}, "MACD": {"current_value": "positive crossover", "analysis": "The MACD is showing a positive crossover, suggesting a bullish trend."}, "Moving Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "analysis": "The stock is currently above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:11.036270", "processing_time": 5.108036, "llm_used": true}, "processing_time": 5.108036, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 136.03755499999997}}, {"timestamp": "2025-07-05T22:19:13.623299", "output_id": "output_20250705_221913_a879dfe1", "input_id": "input_20250705_221908_fa8afe26", "prompt_id": "prompt_20250705_221908_28822c4f", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "Neutral - RSI is close to the middle line, indicating no strong overbought or oversold conditions."}, "MACD": {"current_value": "0.00", "analysis": "Neutral - MACD line is close to the signal line, suggesting a lack of strong trend."}, "Moving_Average": {"short_term_MA": 152.5, "long_term_MA": 153.0, "analysis": "Neutral - Price is between the short-term and long-term moving averages, indicating a lack of a clear trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:13.623299", "processing_time": 4.770413, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "Neutral - RSI is close to the middle line, indicating no strong overbought or oversold conditions."}, "MACD": {"current_value": "0.00", "analysis": "Neutral - MACD line is close to the signal line, suggesting a lack of strong trend."}, "Moving_Average": {"short_term_MA": 152.5, "long_term_MA": 153.0, "analysis": "Neutral - Price is between the short-term and long-term moving averages, indicating a lack of a clear trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:13.623299", "processing_time": 4.770413, "llm_used": true}, "processing_time": 4.770413, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 140.80796799999996}}, {"timestamp": "2025-07-05T22:19:16.296437", "output_id": "output_20250705_221916_99148842", "input_id": "input_20250705_221910_d0deb1f5", "prompt_id": "prompt_20250705_221910_113fb5be", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 72, "analysis": "RSI is above 70, indicating overbought conditions but not yet in extreme territory."}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "analysis": "MACD is in positive territory with a rising histogram, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:16.295438", "processing_time": 6.022383, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 72, "analysis": "RSI is above 70, indicating overbought conditions but not yet in extreme territory."}, "MACD": {"signal_line": 0.2, "histogram": 0.1, "analysis": "MACD is in positive territory with a rising histogram, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:16.295438", "processing_time": 6.022383, "llm_used": true}, "processing_time": 6.022383, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 146.83035099999995}}, {"timestamp": "2025-07-05T22:19:20.103531", "output_id": "output_20250705_221920_3844f381", "input_id": "input_20250705_221913_6b9f7e6e", "prompt_id": "prompt_20250705_221913_5096340d", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 70, indicating the stock may be overbought but still in a strong uptrend."}, "MACD": {"signal_line": 15, "histogram": 5, "analysis": "MACD is above the signal line and the histogram is positive, suggesting a strong bullish trend."}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 145, "analysis": "The stock price is above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:20.103531", "processing_time": 6.608134, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 70, indicating the stock may be overbought but still in a strong uptrend."}, "MACD": {"signal_line": 15, "histogram": 5, "analysis": "MACD is above the signal line and the histogram is positive, suggesting a strong bullish trend."}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 145, "analysis": "The stock price is above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:20.103531", "processing_time": 6.608134, "llm_used": true}, "processing_time": 6.608134, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 153.43848499999996}}, {"timestamp": "2025-07-05T22:19:20.531573", "output_id": "output_20250705_221920_3e5fd24a", "input_id": "input_20250705_221916_9f21ccb6", "prompt_id": "prompt_20250705_221916_1f4c0b27", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:20.531573", "processing_time": 3.720121, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:20.531573", "processing_time": 3.720121, "llm_used": true}, "processing_time": 3.720121, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 157.15860599999996}}, {"timestamp": "2025-07-05T22:19:23.340834", "output_id": "output_20250705_221923_9d3f360b", "input_id": "input_20250705_221914_60333777", "prompt_id": "prompt_20250705_221914_a5038e9d", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.005, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "interpretation": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:23.340834", "processing_time": 8.733007, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.005, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "interpretation": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:23.340834", "processing_time": 8.733007, "llm_used": true}, "processing_time": 8.733007, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 165.89161299999995}}, {"timestamp": "2025-07-05T22:19:23.530209", "output_id": "output_20250705_221923_29548fb6", "input_id": "input_20250705_221918_ff73f90d", "prompt_id": "prompt_20250705_221918_734b723f", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 70, "comment": "RSI is overbought, suggesting a possible pullback or consolidation."}, "MACD": {"signal": "positive", "comment": "MACD line is above the zero line, indicating a bullish trend."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "comment": "The stock is trading above both the 50-day and 200-day moving averages, supporting a bullish outlook."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:23.530209", "processing_time": 5.009959, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 70, "comment": "RSI is overbought, suggesting a possible pullback or consolidation."}, "MACD": {"signal": "positive", "comment": "MACD line is above the zero line, indicating a bullish trend."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "comment": "The stock is trading above both the 50-day and 200-day moving averages, supporting a bullish outlook."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:23.530209", "processing_time": 5.009959, "llm_used": true}, "processing_time": 5.009959, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 170.90157199999996}}, {"timestamp": "2025-07-05T22:19:23.927965", "output_id": "output_20250705_221923_7b0af4db", "input_id": "input_20250705_221915_96149f80", "prompt_id": "prompt_20250705_221915_29c21b5f", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought, but the strong bullish news may justify this."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:23.927965", "processing_time": 8.757424, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought, but the strong bullish news may justify this."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:23.927965", "processing_time": 8.757424, "llm_used": true}, "processing_time": 8.757424, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 179.65899599999995}}, {"timestamp": "2025-07-05T22:19:25.283499", "output_id": "output_20250705_221925_5c61e90c", "input_id": "input_20250705_221917_b91cadf3", "prompt_id": "prompt_20250705_221917_5f0e89ce", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:25.283499", "processing_time": 7.780256, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:25.283499", "processing_time": 7.780256, "llm_used": true}, "processing_time": 7.780256, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 187.43925199999995}}, {"timestamp": "2025-07-05T22:19:26.961677", "output_id": "output_20250705_221926_c4934b80", "input_id": "input_20250705_221916_408558a0", "prompt_id": "prompt_20250705_221916_7d41afdf", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 70, indicating the stock is overbought but not extremely so."}, "MACD": {"current_value": 0.03, "analysis": "The MACD is close to the signal line, suggesting a possible trend continuation."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 120.0, "analysis": "The stock is currently trading above its 50-day and 200-day moving averages, indicating a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:26.961677", "processing_time": 10.33542, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 70, indicating the stock is overbought but not extremely so."}, "MACD": {"current_value": 0.03, "analysis": "The MACD is close to the signal line, suggesting a possible trend continuation."}, "Moving_Averages": {"50-Day_MA": 140.0, "200-Day_MA": 120.0, "analysis": "The stock is currently trading above its 50-day and 200-day moving averages, indicating a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:26.961677", "processing_time": 10.33542, "llm_used": true}, "processing_time": 10.33542, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 197.77467199999995}}, {"timestamp": "2025-07-05T22:19:32.578837", "output_id": "output_20250705_221932_7e4cf54d", "input_id": "input_20250705_221923_1e0bc217", "prompt_id": "prompt_20250705_221924_0c23cceb", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "interpretation": "price above long-term MA, bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:32.578837", "processing_time": 8.620616, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "interpretation": "price above long-term MA, bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:32.578837", "processing_time": 8.620616, "llm_used": true}, "processing_time": 8.620616, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 206.39528799999997}}, {"timestamp": "2025-07-05T22:19:34.078915", "output_id": "output_20250705_221934_e6db495b", "input_id": "input_20250705_221922_258a02f0", "prompt_id": "prompt_20250705_221922_94399b0c", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:34.078915", "processing_time": 11.356553, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "50-day MA above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:19:34.078915", "processing_time": 11.356553, "llm_used": true}, "processing_time": 11.356553, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 217.75184099999996}}]