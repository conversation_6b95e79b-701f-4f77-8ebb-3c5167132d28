#!/usr/bin/env python3
"""
双轨实验系统 (Dual Track Experiment System)

本模块实现了支持原始提示词和优化提示词并行实验的双轨系统，提供：
1. 双轨并行实验管理
2. 实验数据完全分离
3. 实时性能跟踪和监控
4. 自动化结果比较和分析
5. 统计显著性检验

主要功能：
- 轨道A：原始提示词对照组实验
- 轨道B：优化提示词实验组实验
- 独立的数据存储和处理流程
- 实时性能指标计算和比较
- 自动化的实验结果评估

作者: AI Assistant
创建时间: 2025-07-06
"""

import os
import json
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import uuid
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

@dataclass
class ExperimentTrack:
    """实验轨道配置"""
    track_id: str
    track_name: str
    description: str
    prompt_type: str  # "original" or "optimized"
    agents: List[str]
    start_date: str
    duration_days: int
    is_active: bool = True

@dataclass
class ExperimentMetrics:
    """实验指标"""
    daily_returns: List[float]
    cumulative_return: float
    sharpe_ratio: float
    volatility: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    successful_trades: int

@dataclass
class DailyExperimentData:
    """每日实验数据"""
    date: str
    track_id: str
    agent_performances: Dict[str, Any]
    market_conditions: Dict[str, Any]
    trading_decisions: Dict[str, Any]
    daily_return: float
    cumulative_return: float
    metadata: Dict[str, Any]

class DualTrackExperimentSystem:
    """
    双轨实验系统
    
    管理原始提示词和优化提示词的并行实验，提供完整的实验数据分离、
    性能跟踪和自动化比较功能
    """
    
    def __init__(self, 
                 base_data_dir: str = "data/trading",
                 logger: Optional[logging.Logger] = None):
        """
        初始化双轨实验系统
        
        参数:
            base_data_dir: 基础数据目录
            logger: 日志记录器
        """
        self.base_data_dir = Path(base_data_dir)
        self.logger = logger or self._create_default_logger()
        
        # 实验状态跟踪
        self.active_experiments = {}  # {experiment_id: experiment_config}
        self.experiment_tracks = {}   # {track_id: ExperimentTrack}
        self.daily_data = {}         # {experiment_id: {track_id: [DailyExperimentData]}}
        self.performance_metrics = {} # {experiment_id: {track_id: ExperimentMetrics}}
        
        # 线程安全锁
        self.data_lock = threading.Lock()
        
        # 创建必要的目录结构
        self._setup_directory_structure()
        
        self.logger.info("双轨实验系统初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.DualTrackExperimentSystem")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _setup_directory_structure(self):
        """设置目录结构"""
        directories = [
            self.base_data_dir / "dual_track_experiments",
            self.base_data_dir / "experiment_results",
            self.base_data_dir / "performance_analysis"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def create_dual_track_experiment(self, 
                                   experiment_config: Dict[str, Any]) -> str:
        """
        创建双轨实验
        
        参数:
            experiment_config: 实验配置
            
        返回:
            实验ID
        """
        experiment_id = experiment_config.get("experiment_id", f"dual_track_{uuid.uuid4().hex[:8]}")
        
        self.logger.info(f"🧪 创建双轨实验: {experiment_id}")
        
        try:
            # 创建实验轨道
            track_a = ExperimentTrack(
                track_id=f"{experiment_id}_track_a",
                track_name="原始提示词对照组",
                description="使用原始提示词的对照组实验",
                prompt_type="original",
                agents=experiment_config.get("target_agents", experiment_config.get("optimized_agents", [])),
                start_date=experiment_config["start_date"],
                duration_days=experiment_config["duration_days"]
            )
            
            track_b = ExperimentTrack(
                track_id=f"{experiment_id}_track_b",
                track_name="优化提示词实验组",
                description="使用优化提示词的实验组实验",
                prompt_type="optimized",
                agents=experiment_config.get("target_agents", experiment_config.get("optimized_agents", [])),
                start_date=experiment_config["start_date"],
                duration_days=experiment_config["duration_days"]
            )
            
            # 保存实验配置
            with self.data_lock:
                self.active_experiments[experiment_id] = experiment_config
                self.experiment_tracks[track_a.track_id] = track_a
                self.experiment_tracks[track_b.track_id] = track_b
                self.daily_data[experiment_id] = {
                    track_a.track_id: [],
                    track_b.track_id: []
                }
                self.performance_metrics[experiment_id] = {
                    track_a.track_id: None,
                    track_b.track_id: None
                }
            
            # 创建实验目录
            experiment_dir = self.base_data_dir / "dual_track_experiments" / experiment_id
            experiment_dir.mkdir(exist_ok=True)
            
            # 保存实验配置文件
            config_file = experiment_dir / "experiment_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "experiment_id": experiment_id,
                    "config": experiment_config,
                    "tracks": {
                        "track_a": asdict(track_a),
                        "track_b": asdict(track_b)
                    },
                    "created_at": datetime.now().isoformat()
                }, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ 双轨实验创建成功: {experiment_id}")
            return experiment_id
            
        except Exception as e:
            self.logger.error(f"❌ 双轨实验创建失败: {e}")
            raise
    
    def record_daily_experiment_data(self, 
                                   experiment_id: str,
                                   date: str,
                                   track_data: Dict[str, Any]) -> bool:
        """
        记录每日实验数据
        
        参数:
            experiment_id: 实验ID
            date: 日期
            track_data: 轨道数据 {track_id: data}
            
        返回:
            是否记录成功
        """
        if experiment_id not in self.active_experiments:
            self.logger.error(f"实验不存在: {experiment_id}")
            return False
        
        try:
            with self.data_lock:
                for track_id, data in track_data.items():
                    if track_id not in self.experiment_tracks:
                        continue
                    
                    # 创建每日实验数据
                    daily_data = DailyExperimentData(
                        date=date,
                        track_id=track_id,
                        agent_performances=data.get("agent_performances", {}),
                        market_conditions=data.get("market_conditions", {}),
                        trading_decisions=data.get("trading_decisions", {}),
                        daily_return=data.get("daily_return", 0.0),
                        cumulative_return=data.get("cumulative_return", 0.0),
                        metadata=data.get("metadata", {})
                    )
                    
                    # 添加到数据存储
                    self.daily_data[experiment_id][track_id].append(daily_data)
                    
                    # 保存到文件
                    self._save_daily_data(experiment_id, track_id, daily_data)
            
            self.logger.debug(f"📊 记录每日实验数据: {experiment_id} - {date}")
            return True
            
        except Exception as e:
            self.logger.error(f"记录每日实验数据失败: {e}")
            return False
    
    def _save_daily_data(self, 
                        experiment_id: str,
                        track_id: str,
                        daily_data: DailyExperimentData):
        """保存每日数据到文件"""
        experiment_dir = self.base_data_dir / "dual_track_experiments" / experiment_id
        track_dir = experiment_dir / track_id
        track_dir.mkdir(exist_ok=True)
        
        daily_file = track_dir / f"{daily_data.date}_data.json"
        with open(daily_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(daily_data), f, ensure_ascii=False, indent=2)
    
    def calculate_experiment_metrics(self, experiment_id: str) -> Dict[str, ExperimentMetrics]:
        """
        计算实验指标
        
        参数:
            experiment_id: 实验ID
            
        返回:
            各轨道的实验指标
        """
        if experiment_id not in self.active_experiments:
            raise ValueError(f"实验不存在: {experiment_id}")
        
        self.logger.info(f"📈 计算实验指标: {experiment_id}")
        
        metrics = {}
        
        with self.data_lock:
            for track_id, daily_data_list in self.daily_data[experiment_id].items():
                if not daily_data_list:
                    continue
                
                # 提取收益数据
                daily_returns = [data.daily_return for data in daily_data_list]
                cumulative_returns = [data.cumulative_return for data in daily_data_list]
                
                # 计算性能指标
                metrics[track_id] = self._calculate_track_metrics(
                    daily_returns, cumulative_returns, daily_data_list
                )
                
                # 更新缓存
                self.performance_metrics[experiment_id][track_id] = metrics[track_id]
        
        self.logger.info(f"✅ 实验指标计算完成: {experiment_id}")
        return metrics
    
    def _calculate_track_metrics(self, 
                               daily_returns: List[float],
                               cumulative_returns: List[float],
                               daily_data_list: List[DailyExperimentData]) -> ExperimentMetrics:
        """计算轨道指标"""
        if not daily_returns:
            return ExperimentMetrics(
                daily_returns=[], cumulative_return=0.0, sharpe_ratio=0.0,
                volatility=0.0, max_drawdown=0.0, win_rate=0.0,
                total_trades=0, successful_trades=0
            )
        
        # 基本统计
        total_return = cumulative_returns[-1] if cumulative_returns else 0.0
        avg_return = np.mean(daily_returns)
        volatility = np.std(daily_returns)
        
        # Sharpe比率 (假设无风险利率为0)
        sharpe_ratio = avg_return / (volatility + 1e-8) * np.sqrt(252)
        
        # 最大回撤
        max_drawdown = self._calculate_max_drawdown(cumulative_returns)
        
        # 胜率
        positive_returns = [r for r in daily_returns if r > 0]
        win_rate = len(positive_returns) / len(daily_returns) if daily_returns else 0
        
        # 交易统计
        total_trades = len(daily_returns)
        successful_trades = len(positive_returns)
        
        return ExperimentMetrics(
            daily_returns=daily_returns,
            cumulative_return=total_return,
            sharpe_ratio=sharpe_ratio,
            volatility=float(volatility),
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            total_trades=total_trades,
            successful_trades=successful_trades
        )
    
    def _calculate_max_drawdown(self, cumulative_returns: List[float]) -> float:
        """计算最大回撤"""
        if not cumulative_returns:
            return 0.0
        
        peak = cumulative_returns[0]
        max_dd = 0.0
        
        for value in cumulative_returns:
            if value > peak:
                peak = value
            drawdown = (peak - value) / (peak + 1e-8)
            max_dd = max(max_dd, drawdown)
        
        return max_dd

    def compare_track_performance(self,
                                experiment_id: str,
                                statistical_significance_level: float = 0.05) -> Dict[str, Any]:
        """
        比较轨道性能

        参数:
            experiment_id: 实验ID
            statistical_significance_level: 统计显著性水平

        返回:
            性能比较结果
        """
        if experiment_id not in self.active_experiments:
            raise ValueError(f"实验不存在: {experiment_id}")

        self.logger.info(f"📊 开始轨道性能比较: {experiment_id}")

        # 计算最新指标
        metrics = self.calculate_experiment_metrics(experiment_id)

        if len(metrics) < 2:
            raise ValueError("需要至少两个轨道进行比较")

        # 获取轨道数据
        track_ids = list(metrics.keys())
        track_a_id, track_b_id = track_ids[0], track_ids[1]

        track_a_metrics = metrics[track_a_id]
        track_b_metrics = metrics[track_b_id]

        # 进行统计比较
        comparison_result = self._perform_statistical_comparison(
            track_a_metrics, track_b_metrics, statistical_significance_level
        )

        # 生成比较报告
        comparison_report = {
            "experiment_id": experiment_id,
            "comparison_date": datetime.now().isoformat(),
            "tracks": {
                track_a_id: {
                    "track_info": asdict(self.experiment_tracks[track_a_id]),
                    "metrics": asdict(track_a_metrics)
                },
                track_b_id: {
                    "track_info": asdict(self.experiment_tracks[track_b_id]),
                    "metrics": asdict(track_b_metrics)
                }
            },
            "statistical_comparison": comparison_result,
            "recommendation": self._generate_track_recommendation(comparison_result)
        }

        # 保存比较结果
        self._save_comparison_result(experiment_id, comparison_report)

        self.logger.info(f"✅ 轨道性能比较完成: {experiment_id}")
        return comparison_report

    def _perform_statistical_comparison(self,
                                      track_a_metrics: ExperimentMetrics,
                                      track_b_metrics: ExperimentMetrics,
                                      significance_level: float) -> Dict[str, Any]:
        """执行统计比较"""
        from scipy import stats

        # 获取收益数据
        returns_a = track_a_metrics.daily_returns
        returns_b = track_b_metrics.daily_returns

        # t检验
        if len(returns_a) > 1 and len(returns_b) > 1:
            t_stat, p_value = stats.ttest_ind(returns_b, returns_a)
        else:
            t_stat, p_value = 0.0, 1.0

        # 计算改进指标
        return_improvement = (track_b_metrics.cumulative_return - track_a_metrics.cumulative_return) / abs(track_a_metrics.cumulative_return + 1e-8)
        sharpe_improvement = track_b_metrics.sharpe_ratio - track_a_metrics.sharpe_ratio
        volatility_change = track_b_metrics.volatility - track_a_metrics.volatility

        # 统计显著性判断
        p_val = p_value if isinstance(p_value, (int, float)) else p_value[0] if isinstance(p_value, tuple) else 1.0
        is_significant = p_val < significance_level

        return {
            "t_statistic": float(t_stat) if isinstance(t_stat, (int, float)) else float(t_stat[0]) if isinstance(t_stat, tuple) else 0.0,
            "p_value": p_val,
            "is_statistically_significant": is_significant,
            "confidence_level": 1 - significance_level,
            "return_improvement": return_improvement,
            "sharpe_improvement": sharpe_improvement,
            "volatility_change": volatility_change,
            "track_a_performance": {
                "cumulative_return": track_a_metrics.cumulative_return,
                "sharpe_ratio": track_a_metrics.sharpe_ratio,
                "volatility": track_a_metrics.volatility,
                "max_drawdown": track_a_metrics.max_drawdown,
                "win_rate": track_a_metrics.win_rate
            },
            "track_b_performance": {
                "cumulative_return": track_b_metrics.cumulative_return,
                "sharpe_ratio": track_b_metrics.sharpe_ratio,
                "volatility": track_b_metrics.volatility,
                "max_drawdown": track_b_metrics.max_drawdown,
                "win_rate": track_b_metrics.win_rate
            }
        }

    def _generate_track_recommendation(self, comparison_result: Dict[str, Any]) -> str:
        """生成轨道推荐"""
        statistical_comparison = comparison_result["statistical_comparison"]

        if (statistical_comparison["is_statistically_significant"] and
            statistical_comparison["return_improvement"] > 0.01 and  # 至少1%改进
            statistical_comparison["sharpe_improvement"] > 0):
            return "推荐采用优化提示词（轨道B）"
        elif statistical_comparison["return_improvement"] < -0.01:
            return "推荐保持原始提示词（轨道A）"
        else:
            return "两个轨道性能差异不显著，建议保持原始提示词"

    def _save_comparison_result(self, experiment_id: str, comparison_report: Dict[str, Any]):
        """保存比较结果"""
        result_dir = self.base_data_dir / "experiment_results" / experiment_id
        result_dir.mkdir(exist_ok=True)

        result_file = result_dir / f"comparison_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(comparison_report, f, ensure_ascii=False, indent=2)

    def get_experiment_status(self, experiment_id: str) -> Dict[str, Any]:
        """
        获取实验状态

        参数:
            experiment_id: 实验ID

        返回:
            实验状态信息
        """
        if experiment_id not in self.active_experiments:
            return {"status": "not_found", "message": f"实验不存在: {experiment_id}"}

        config = self.active_experiments[experiment_id]

        # 计算实验进度
        start_date = datetime.strptime(config["start_date"], "%Y-%m-%d")
        current_date = datetime.now()
        days_elapsed = (current_date - start_date).days
        total_days = config["duration_days"]
        progress = min(days_elapsed / total_days, 1.0) if total_days > 0 else 0

        # 获取数据统计
        data_stats = {}
        with self.data_lock:
            for track_id, daily_data_list in self.daily_data[experiment_id].items():
                data_stats[track_id] = {
                    "total_data_points": len(daily_data_list),
                    "latest_date": daily_data_list[-1].date if daily_data_list else None
                }

        return {
            "status": "active" if progress < 1.0 else "completed",
            "experiment_id": experiment_id,
            "progress": progress,
            "days_elapsed": days_elapsed,
            "total_days": total_days,
            "remaining_days": max(0, total_days - days_elapsed),
            "tracks": {track_id: asdict(track) for track_id, track in self.experiment_tracks.items()
                      if track_id.startswith(experiment_id)},
            "data_statistics": data_stats,
            "has_performance_metrics": experiment_id in self.performance_metrics
        }

    def finalize_experiment(self, experiment_id: str) -> Dict[str, Any]:
        """
        完成实验

        参数:
            experiment_id: 实验ID

        返回:
            实验最终结果
        """
        if experiment_id not in self.active_experiments:
            raise ValueError(f"实验不存在: {experiment_id}")

        self.logger.info(f"🏁 完成实验: {experiment_id}")

        try:
            # 计算最终指标
            final_metrics = self.calculate_experiment_metrics(experiment_id)

            # 执行最终比较
            final_comparison = self.compare_track_performance(experiment_id)

            # 生成实验总结
            experiment_summary = {
                "experiment_id": experiment_id,
                "finalization_date": datetime.now().isoformat(),
                "experiment_config": self.active_experiments[experiment_id],
                "final_metrics": {track_id: asdict(metrics) for track_id, metrics in final_metrics.items()},
                "final_comparison": final_comparison,
                "experiment_status": self.get_experiment_status(experiment_id)
            }

            # 保存实验总结
            summary_dir = self.base_data_dir / "experiment_results" / experiment_id
            summary_dir.mkdir(exist_ok=True)

            summary_file = summary_dir / "experiment_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(experiment_summary, f, ensure_ascii=False, indent=2)

            # 标记实验为已完成
            with self.data_lock:
                for track_id in self.experiment_tracks:
                    if track_id.startswith(experiment_id):
                        self.experiment_tracks[track_id].is_active = False

            self.logger.info(f"✅ 实验完成: {experiment_id}")
            return experiment_summary

        except Exception as e:
            self.logger.error(f"❌ 实验完成失败: {e}")
            raise

    def cleanup_experiment(self, experiment_id: str, keep_data: bool = True) -> bool:
        """
        清理实验

        参数:
            experiment_id: 实验ID
            keep_data: 是否保留数据文件

        返回:
            是否清理成功
        """
        if experiment_id not in self.active_experiments:
            return False

        self.logger.info(f"🧹 清理实验: {experiment_id}")

        try:
            with self.data_lock:
                # 从内存中移除
                del self.active_experiments[experiment_id]

                # 移除轨道信息
                tracks_to_remove = [track_id for track_id in self.experiment_tracks
                                  if track_id.startswith(experiment_id)]
                for track_id in tracks_to_remove:
                    del self.experiment_tracks[track_id]

                # 移除数据缓存
                if experiment_id in self.daily_data:
                    del self.daily_data[experiment_id]

                if experiment_id in self.performance_metrics:
                    del self.performance_metrics[experiment_id]

            # 可选：删除数据文件
            if not keep_data:
                import shutil
                experiment_dir = self.base_data_dir / "dual_track_experiments" / experiment_id
                if experiment_dir.exists():
                    shutil.rmtree(experiment_dir)

                result_dir = self.base_data_dir / "experiment_results" / experiment_id
                if result_dir.exists():
                    shutil.rmtree(result_dir)

            self.logger.info(f"✅ 实验清理完成: {experiment_id}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 实验清理失败: {e}")
            return False

    def list_active_experiments(self) -> List[Dict[str, Any]]:
        """
        列出活跃实验

        返回:
            活跃实验列表
        """
        active_experiments = []

        with self.data_lock:
            for experiment_id, config in self.active_experiments.items():
                status = self.get_experiment_status(experiment_id)
                if status["status"] == "active":
                    active_experiments.append({
                        "experiment_id": experiment_id,
                        "config": config,
                        "status": status
                    })

        return active_experiments

    def get_system_statistics(self) -> Dict[str, Any]:
        """
        获取系统统计信息

        返回:
            系统统计信息
        """
        with self.data_lock:
            total_experiments = len(self.active_experiments)
            active_experiments = len([exp for exp in self.active_experiments.values()
                                    if self.get_experiment_status(exp.get("experiment_id", ""))["status"] == "active"])
            total_tracks = len(self.experiment_tracks)
            active_tracks = len([track for track in self.experiment_tracks.values() if track.is_active])

            total_data_points = sum(
                sum(len(track_data) for track_data in exp_data.values())
                for exp_data in self.daily_data.values()
            )

        return {
            "total_experiments": total_experiments,
            "active_experiments": active_experiments,
            "completed_experiments": total_experiments - active_experiments,
            "total_tracks": total_tracks,
            "active_tracks": active_tracks,
            "total_data_points": total_data_points,
            "system_uptime": datetime.now().isoformat()
        }
