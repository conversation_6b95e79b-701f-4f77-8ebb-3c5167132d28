[{"timestamp": "2025-07-05T22:35:47.116006", "output_id": "output_20250705_223547_ac9e2677", "input_id": "input_20250705_223542_4a9a1603", "prompt_id": "prompt_20250705_223542_293dd07c", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 56, "interpretation": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.01, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 153.5, "200_day_MA": 160.0, "interpretation": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:35:47.116006", "processing_time": 4.475898, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 56, "interpretation": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.01, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 153.5, "200_day_MA": 160.0, "interpretation": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:35:47.116006", "processing_time": 4.475898, "llm_used": true}, "processing_time": 4.475898, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 4.475898}}, {"timestamp": "2025-07-05T22:36:28.966502", "output_id": "output_20250705_223628_7a054b91", "input_id": "input_20250705_223624_553ef81f", "prompt_id": "prompt_20250705_223625_de545dc2", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50.2, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:28.966502", "processing_time": 4.671725, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50.2, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:28.966502", "processing_time": 4.671725, "llm_used": true}, "processing_time": 4.671725, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 9.147623}}, {"timestamp": "2025-07-05T22:36:29.187329", "output_id": "output_20250705_223629_8fb69cb1", "input_id": "input_20250705_223624_a55d3553", "prompt_id": "prompt_20250705_223624_3983e585", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "reading": "overbought", "comment": "The RSI is above 70, indicating that the stock may be overbought and due for a pullback."}, "MACD": {"signal_line": 10, "histogram": 0.5, "reading": "bullish crossover", "comment": "The MACD signal line has crossed above the MACD line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155, "200_day_MA": 145, "reading": "bullish", "comment": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:29.187329", "processing_time": 5.059199, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "reading": "overbought", "comment": "The RSI is above 70, indicating that the stock may be overbought and due for a pullback."}, "MACD": {"signal_line": 10, "histogram": 0.5, "reading": "bullish crossover", "comment": "The MACD signal line has crossed above the MACD line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155, "200_day_MA": 145, "reading": "bullish", "comment": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:29.187329", "processing_time": 5.059199, "llm_used": true}, "processing_time": 5.059199, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 14.206821999999999}}, {"timestamp": "2025-07-05T22:36:29.278292", "output_id": "output_20250705_223629_a5e2b4f7", "input_id": "input_20250705_223624_b60d5f2c", "prompt_id": "prompt_20250705_223624_93ceb2f9", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 61, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:29.278292", "processing_time": 4.990176, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 61, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:29.278292", "processing_time": 4.990176, "llm_used": true}, "processing_time": 4.990176, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 19.196998}}, {"timestamp": "2025-07-05T22:36:29.479181", "output_id": "output_20250705_223629_1a698496", "input_id": "input_20250705_223624_b85797ec", "prompt_id": "prompt_20250705_223624_bce606c3", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "analysis": "The RSI is near the middle, indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.0, "histogram": -0.05, "analysis": "The MACD is close to the signal line, suggesting a neutral trend with no clear bullish or bearish momentum."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 160.0, "analysis": "The stock is trading below its 50-day and 200-day moving averages, indicating a potential bearish trend in the long-term."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:29.478665", "processing_time": 5.230391, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "analysis": "The RSI is near the middle, indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.0, "histogram": -0.05, "analysis": "The MACD is close to the signal line, suggesting a neutral trend with no clear bullish or bearish momentum."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 160.0, "analysis": "The stock is trading below its 50-day and 200-day moving averages, indicating a potential bearish trend in the long-term."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:29.478665", "processing_time": 5.230391, "llm_used": true}, "processing_time": 5.230391, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 24.427389}}, {"timestamp": "2025-07-05T22:36:29.752965", "output_id": "output_20250705_223629_6489760a", "input_id": "input_20250705_223624_b3f821cd", "prompt_id": "prompt_20250705_223624_33859490", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "RSI is in the middle of the overbought/oversold range, indicating no strong trend."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD signal line is slightly above the zero line with a negative histogram, suggesting a weak bullish trend."}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 140.0, "analysis": "The stock is currently above both the 50-day and 200-day moving averages, indicating a neutral to slightly bullish trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:29.733976", "processing_time": 5.595553, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "RSI is in the middle of the overbought/oversold range, indicating no strong trend."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD signal line is slightly above the zero line with a negative histogram, suggesting a weak bullish trend."}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 140.0, "analysis": "The stock is currently above both the 50-day and 200-day moving averages, indicating a neutral to slightly bullish trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:29.733976", "processing_time": 5.595553, "llm_used": true}, "processing_time": 5.595553, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 30.022942}}, {"timestamp": "2025-07-05T22:36:30.863983", "output_id": "output_20250705_223630_02825147", "input_id": "input_20250705_223624_3071ba85", "prompt_id": "prompt_20250705_223624_d905054d", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 153.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:30.863983", "processing_time": 6.761705, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 153.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:30.863983", "processing_time": 6.761705, "llm_used": true}, "processing_time": 6.761705, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 36.784647}}, {"timestamp": "2025-07-05T22:36:31.000141", "output_id": "output_20250705_223631_7715d135", "input_id": "input_20250705_223624_0179994e", "prompt_id": "prompt_20250705_223625_ac7c5b40", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is above 50, indicating a neutral trend."}, "MACD": {"signal_line": 0.0, "analysis": "MACD signal line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, which suggests a neutral trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:31.000141", "processing_time": 6.60189, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is above 50, indicating a neutral trend."}, "MACD": {"signal_line": 0.0, "analysis": "MACD signal line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, which suggests a neutral trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:31.000141", "processing_time": 6.60189, "llm_used": true}, "processing_time": 6.60189, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 43.386537}}, {"timestamp": "2025-07-05T22:36:33.036078", "output_id": "output_20250705_223633_6f228415", "input_id": "input_20250705_223629_6116fc48", "prompt_id": "prompt_20250705_223629_c894bb10", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:33.036078", "processing_time": 3.687741, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:33.036078", "processing_time": 3.687741, "llm_used": true}, "processing_time": 3.687741, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 47.074278}}, {"timestamp": "2025-07-05T22:36:33.402898", "output_id": "output_20250705_223633_08ca8042", "input_id": "input_20250705_223629_15b97003", "prompt_id": "prompt_20250705_223629_f1416a14", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Average": {"50-Day_MA": 150.0, "200-Day_MA": 145.0, "signal": "price above 50-Day and 200-Day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:33.402898", "processing_time": 4.244879, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Average": {"50-Day_MA": 150.0, "200-Day_MA": 145.0, "signal": "price above 50-Day and 200-Day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:33.402898", "processing_time": 4.244879, "llm_used": true}, "processing_time": 4.244879, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 51.319157}}, {"timestamp": "2025-07-05T22:36:33.662754", "output_id": "output_20250705_223633_3b5fa093", "input_id": "input_20250705_223629_60206c93", "prompt_id": "prompt_20250705_223629_c04080cc", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:33.662754", "processing_time": 3.961486, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:33.662754", "processing_time": 3.961486, "llm_used": true}, "processing_time": 3.961486, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 55.280643}}, {"timestamp": "2025-07-05T22:36:34.014766", "output_id": "output_20250705_223634_1f5fef57", "input_id": "input_20250705_223629_5eb1737e", "prompt_id": "prompt_20250705_223629_fca59e21", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:34.014766", "processing_time": 4.306495, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:34.014766", "processing_time": 4.306495, "llm_used": true}, "processing_time": 4.306495, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 59.587137999999996}}, {"timestamp": "2025-07-05T22:36:34.054459", "output_id": "output_20250705_223634_0e2763b0", "input_id": "input_20250705_223630_337067ed", "prompt_id": "prompt_20250705_223630_0e90d2e5", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:34.054459", "processing_time": 3.807309, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "bullish"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:34.054459", "processing_time": 3.807309, "llm_used": true}, "processing_time": 3.807309, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 63.394447}}, {"timestamp": "2025-07-05T22:36:34.541595", "output_id": "output_20250705_223634_615cf33d", "input_id": "input_20250705_223624_ef1afbf0", "prompt_id": "prompt_20250705_223625_0a5526b3", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "trend": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 153.0, "200_day_MA": 148.0, "trend": "bearish"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:34.541595", "processing_time": 10.153865, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "trend": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 153.0, "200_day_MA": 148.0, "trend": "bearish"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:34.541595", "processing_time": 10.153865, "llm_used": true}, "processing_time": 10.153865, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 73.548312}}, {"timestamp": "2025-07-05T22:36:34.613014", "output_id": "output_20250705_223634_db79dadb", "input_id": "input_20250705_223630_b800f37b", "prompt_id": "prompt_20250705_223630_96f503a2", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:34.613014", "processing_time": 3.635254, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 140.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:34.613014", "processing_time": 3.635254, "llm_used": true}, "processing_time": 3.635254, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 77.183566}}, {"timestamp": "2025-07-05T22:36:35.278545", "output_id": "output_20250705_223635_76a08486", "input_id": "input_20250705_223630_9ec8a5b6", "prompt_id": "prompt_20250705_223630_f427ccab", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:35.278545", "processing_time": 4.871885, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:35.278545", "processing_time": 4.871885, "llm_used": true}, "processing_time": 4.871885, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 82.055451}}, {"timestamp": "2025-07-05T22:36:36.821952", "output_id": "output_20250705_223636_305d6c7d", "input_id": "input_20250705_223632_73463f08", "prompt_id": "prompt_20250705_223632_04319175", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:36.821952", "processing_time": 4.645393, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:36.821952", "processing_time": 4.645393, "llm_used": true}, "processing_time": 4.645393, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 86.700844}}, {"timestamp": "2025-07-05T22:36:36.848616", "output_id": "output_20250705_223636_732a888c", "input_id": "input_20250705_223629_90c746b5", "prompt_id": "prompt_20250705_223629_911141d3", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "interpretation": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:36.847610", "processing_time": 7.200834, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "interpretation": "50-day MA above 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:36.847610", "processing_time": 7.200834, "llm_used": true}, "processing_time": 7.200834, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 93.901678}}, {"timestamp": "2025-07-05T22:36:36.931995", "output_id": "output_20250705_223636_9b550859", "input_id": "input_20250705_223629_9c472519", "prompt_id": "prompt_20250705_223629_0ce10fa1", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought, indicating potential price pullback"}, "MACD": {"signal_line": 20, "histogram": 0.5, "interpretation": "MACD line above signal line, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 150, "200_day_MA": 140, "interpretation": "Stock price above both 50-day and 200-day moving averages, strong bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:36.931995", "processing_time": 7.030328, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought, indicating potential price pullback"}, "MACD": {"signal_line": 20, "histogram": 0.5, "interpretation": "MACD line above signal line, indicating bullish momentum"}, "Moving_Averages": {"50_day_MA": 150, "200_day_MA": 140, "interpretation": "Stock price above both 50-day and 200-day moving averages, strong bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:36.931995", "processing_time": 7.030328, "llm_used": true}, "processing_time": 7.030328, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 100.932006}}, {"timestamp": "2025-07-05T22:36:48.924709", "output_id": "output_20250705_223648_ede85880", "input_id": "input_20250705_223642_2b7230a5", "prompt_id": "prompt_20250705_223642_7f78cb93", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 145.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 120.0, "signal": "50-day MA crossing above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:48.924709", "processing_time": 6.02868, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 145.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 120.0, "signal": "50-day MA crossing above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:48.924709", "processing_time": 6.02868, "llm_used": true}, "processing_time": 6.02868, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 106.960686}}, {"timestamp": "2025-07-05T22:36:49.091324", "output_id": "output_20250705_223649_ba9f110a", "input_id": "input_20250705_223644_3e74586a", "prompt_id": "prompt_20250705_223644_d31f13a2", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal": "neutral", "histogram": {"current_value": 0.1, "trend": "flat"}}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "trend": "flat"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:49.084181", "processing_time": 4.217283, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal": "neutral", "histogram": {"current_value": 0.1, "trend": "flat"}}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "trend": "flat"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:49.084181", "processing_time": 4.217283, "llm_used": true}, "processing_time": 4.217283, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 111.17796899999999}}, {"timestamp": "2025-07-05T22:36:49.664025", "output_id": "output_20250705_223649_3846224e", "input_id": "input_20250705_223644_cd184226", "prompt_id": "prompt_20250705_223644_086fba06", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:49.664025", "processing_time": 4.827515, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:49.664025", "processing_time": 4.827515, "llm_used": true}, "processing_time": 4.827515, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 116.005484}}, {"timestamp": "2025-07-05T22:36:54.566184", "output_id": "output_20250705_223654_7783f891", "input_id": "input_20250705_223647_93044e66", "prompt_id": "prompt_20250705_223647_25c61a1e", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "RSI is in the middle of the neutral zone, indicating that the stock may not have strong momentum in either direction."}, "MACD": {"current_value": "0.01", "analysis": "The MACD line is just above the signal line, suggesting a lack of strong momentum and indicating a neutral trend."}, "Moving_Averages": {"50-Day_MA": 153.0, "200-Day_MA": 145.0, "analysis": "The stock is currently above the 50-day moving average but below the 200-day moving average, which suggests a short-term bullish trend but a long-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:54.550002", "processing_time": 7.093871, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "analysis": "RSI is in the middle of the neutral zone, indicating that the stock may not have strong momentum in either direction."}, "MACD": {"current_value": "0.01", "analysis": "The MACD line is just above the signal line, suggesting a lack of strong momentum and indicating a neutral trend."}, "Moving_Averages": {"50-Day_MA": 153.0, "200-Day_MA": 145.0, "analysis": "The stock is currently above the 50-day moving average but below the 200-day moving average, which suggests a short-term bullish trend but a long-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:54.550002", "processing_time": 7.093871, "llm_used": true}, "processing_time": 7.093871, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 123.099355}}, {"timestamp": "2025-07-05T22:36:54.756396", "output_id": "output_20250705_223654_6ae32f83", "input_id": "input_20250705_223649_964ec0ef", "prompt_id": "prompt_20250705_223649_662c5684", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 63, "interpretation": "slightly above neutral, suggesting a slight bullish bias"}, "MACD": {"signal_line": 0.0, "histogram": 0.01, "interpretation": "close to zero, indicating a neutral trend"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA, suggesting a slightly bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:54.756396", "processing_time": 5.478408, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 63, "interpretation": "slightly above neutral, suggesting a slight bullish bias"}, "MACD": {"signal_line": 0.0, "histogram": 0.01, "interpretation": "close to zero, indicating a neutral trend"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "50-day MA above 200-day MA, suggesting a slightly bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:54.756396", "processing_time": 5.478408, "llm_used": true}, "processing_time": 5.478408, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 128.577763}}, {"timestamp": "2025-07-05T22:36:54.920663", "output_id": "output_20250705_223654_3e2aa1b5", "input_id": "input_20250705_223651_57206980", "prompt_id": "prompt_20250705_223651_d3503330", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:54.920663", "processing_time": 3.678226, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:54.920663", "processing_time": 3.678226, "llm_used": true}, "processing_time": 3.678226, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 132.255989}}, {"timestamp": "2025-07-05T22:36:56.210834", "output_id": "output_20250705_223656_3c339060", "input_id": "input_20250705_223649_fd5fab09", "prompt_id": "prompt_20250705_223649_82d45ce8", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "interpretation": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "neutral"}, "Moving_Averages": {"50-Day_MA": 153.0, "200-Day_MA": 145.0, "current_price": 154.0, "interpretation": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:56.210834", "processing_time": 6.507223, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "interpretation": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "neutral"}, "Moving_Averages": {"50-Day_MA": 153.0, "200-Day_MA": 145.0, "current_price": 154.0, "interpretation": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:56.210834", "processing_time": 6.507223, "llm_used": true}, "processing_time": 6.507223, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 138.763212}}, {"timestamp": "2025-07-05T22:36:57.846161", "output_id": "output_20250705_223657_e8dfbeba", "input_id": "input_20250705_223654_aed78e94", "prompt_id": "prompt_20250705_223654_ceb80bfd", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover", "histogram": "positive divergence"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "50_day_MA above 200_day_MA"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:57.846161", "processing_time": 3.326053, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover", "histogram": "positive divergence"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "50_day_MA above 200_day_MA"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:57.846161", "processing_time": 3.326053, "llm_used": true}, "processing_time": 3.326053, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 142.089265}}, {"timestamp": "2025-07-05T22:36:58.956046", "output_id": "output_20250705_223658_8a80a67f", "input_id": "input_20250705_223652_629d667b", "prompt_id": "prompt_20250705_223652_6c52d948", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought, indicating potential price pullback"}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "interpretation": "Positive crossover, suggesting bullish momentum"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 135.0, "interpretation": "50-day MA above 200-day MA, indicating long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:58.956046", "processing_time": 6.643957, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought, indicating potential price pullback"}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "interpretation": "Positive crossover, suggesting bullish momentum"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 135.0, "interpretation": "50-day MA above 200-day MA, indicating long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:58.956046", "processing_time": 6.643957, "llm_used": true}, "processing_time": 6.643957, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 148.733222}}, {"timestamp": "2025-07-05T22:36:59.231710", "output_id": "output_20250705_223659_59483e8d", "input_id": "input_20250705_223652_0ac690e6", "prompt_id": "prompt_20250705_223652_589fd7d8", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52.3, "analysis": "The RSI is neutral, indicating that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, which are both sloping upwards, indicating a slightly bullish trend but with room for potential downward movement."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:59.231710", "processing_time": 6.768623, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52.3, "analysis": "The RSI is neutral, indicating that the stock is neither overbought nor oversold."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, which are both sloping upwards, indicating a slightly bullish trend but with room for potential downward movement."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:36:59.231710", "processing_time": 6.768623, "llm_used": true}, "processing_time": 6.768623, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 155.501845}}, {"timestamp": "2025-07-05T22:37:00.518314", "output_id": "output_20250705_223700_73fd759c", "input_id": "input_20250705_223650_658a54d5", "prompt_id": "prompt_20250705_223650_2c5e2fad", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 68, "trend": "upward"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "trend": "neutral"}, "Moving_Average": {"50_day": 140.0, "200_day": 150.0, "current_price": 140.5, "trend": "downward"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:37:00.518314", "processing_time": 10.01008, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 68, "trend": "upward"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "trend": "neutral"}, "Moving_Average": {"50_day": 140.0, "200_day": 150.0, "current_price": 140.5, "trend": "downward"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:37:00.518314", "processing_time": 10.01008, "llm_used": true}, "processing_time": 10.01008, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 165.511925}}, {"timestamp": "2025-07-05T22:37:00.803110", "output_id": "output_20250705_223700_32396df9", "input_id": "input_20250705_223654_ba2a8edb", "prompt_id": "prompt_20250705_223654_1ad8de49", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 70, "comment": "RSI is in the overbought territory, suggesting a potential pullback."}, "MACD": {"signal_line": 0.02, "histogram": 0.04, "comment": "MACD histogram is positive and rising, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "The stock is above both the 50-day and 200-day moving averages, supporting a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:37:00.794114", "processing_time": 6.019658, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 70, "comment": "RSI is in the overbought territory, suggesting a potential pullback."}, "MACD": {"signal_line": 0.02, "histogram": 0.04, "comment": "MACD histogram is positive and rising, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "comment": "The stock is above both the 50-day and 200-day moving averages, supporting a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:37:00.794114", "processing_time": 6.019658, "llm_used": true}, "processing_time": 6.019658, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 171.53158299999998}}, {"timestamp": "2025-07-05T22:37:01.212537", "output_id": "output_20250705_223701_11bbdbdc", "input_id": "input_20250705_223656_fae1e123", "prompt_id": "prompt_20250705_223656_f06397e8", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 52, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "Slightly <PERSON>ish"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 160.0, "interpretation": "Currently below the 50-day MA but above the 200-day MA, indicating a possible short-term bearish trend with a long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:37:01.171635", "processing_time": 4.508807, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 52, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "Slightly <PERSON>ish"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 160.0, "interpretation": "Currently below the 50-day MA but above the 200-day MA, indicating a possible short-term bearish trend with a long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:37:01.171635", "processing_time": 4.508807, "llm_used": true}, "processing_time": 4.508807, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 176.04038999999997}}, {"timestamp": "2025-07-05T22:37:03.836397", "output_id": "output_20250705_223703_bfffaeb7", "input_id": "input_20250705_223654_dc6403e1", "prompt_id": "prompt_20250705_223654_f78a02ad", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is in the middle of its range, indicating neither strong buying nor selling pressure."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD signal line is close to zero, suggesting a lack of strong trend momentum."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently trading above the 50-day MA but below the 200-day MA, indicating a short-term bullish trend but a long-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:37:03.836397", "processing_time": 9.724273, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is in the middle of its range, indicating neither strong buying nor selling pressure."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD signal line is close to zero, suggesting a lack of strong trend momentum."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently trading above the 50-day MA but below the 200-day MA, indicating a short-term bullish trend but a long-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:37:03.836397", "processing_time": 9.724273, "llm_used": true}, "processing_time": 9.724273, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 185.76466299999998}}]