[{"timestamp": "2025-07-05T22:38:54.370029", "output_id": "output_20250705_223854_604a51f5", "input_id": "input_20250705_223850_90c9d439", "prompt_id": "prompt_20250705_223850_d9267d98", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.03, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "crossover": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:38:54.370029", "processing_time": 3.929976, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.03, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "crossover": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:38:54.370029", "processing_time": 3.929976, "llm_used": true}, "processing_time": 3.929976, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 3.929976}}, {"timestamp": "2025-07-05T22:39:33.242145", "output_id": "output_20250705_223933_1bb32199", "input_id": "input_20250705_223928_e9a8ebdb", "prompt_id": "prompt_20250705_223928_a939868d", "raw_response": {"trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "analysis": "The RSI is close to the middle of its range, suggesting a neutral trend."}, "MACD": {"signal_line": 0.1, "analysis": "The MACD line is slightly above the signal line, indicating a weak bullish trend."}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 148.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, which might suggest a bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:33.241141", "processing_time": 5.117304, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "analysis": "The RSI is close to the middle of its range, suggesting a neutral trend."}, "MACD": {"signal_line": 0.1, "analysis": "The MACD line is slightly above the signal line, indicating a weak bullish trend."}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 148.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, which might suggest a bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:33.241141", "processing_time": 5.117304, "llm_used": true}, "processing_time": 5.117304, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 9.04728}}, {"timestamp": "2025-07-05T22:39:33.723360", "output_id": "output_20250705_223933_145986dc", "input_id": "input_20250705_223927_fa9ab18c", "prompt_id": "prompt_20250705_223927_e26ff796", "raw_response": {"analysis_date": "2025-01-15", "trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52.5, "interpretation": "Neutral - The RSI is neither overbought nor oversold, indicating a balanced market condition."}, "MACD": {"signal_line": 0.0, "histogram": -0.01, "interpretation": "Neutral - The MACD signal line is close to zero, and the histogram is negative, suggesting no clear trend direction."}, "Moving_Averages": {"50_day_MA": 153.5, "200_day_MA": 160.0, "interpretation": "Neutral - The stock is currently trading below its 50-day and 200-day moving averages, suggesting a bearish trend in the short term but a stable trend in the long term."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:33.723360", "processing_time": 5.769231, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-15", "trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52.5, "interpretation": "Neutral - The RSI is neither overbought nor oversold, indicating a balanced market condition."}, "MACD": {"signal_line": 0.0, "histogram": -0.01, "interpretation": "Neutral - The MACD signal line is close to zero, and the histogram is negative, suggesting no clear trend direction."}, "Moving_Averages": {"50_day_MA": 153.5, "200_day_MA": 160.0, "interpretation": "Neutral - The stock is currently trading below its 50-day and 200-day moving averages, suggesting a bearish trend in the short term but a stable trend in the long term."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:33.723360", "processing_time": 5.769231, "llm_used": true}, "processing_time": 5.769231, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 14.816511000000002}}, {"timestamp": "2025-07-05T22:39:35.137768", "output_id": "output_20250705_223935_55b332a8", "input_id": "input_20250705_223927_3729e9ff", "prompt_id": "prompt_20250705_223927_89944026", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "analysis": "RSI is in the middle of the range, indicating neither strong buying nor selling pressure."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "analysis": "MACD is close to the center line, suggesting a lack of strong trend."}, "Moving_Averages": {"50_day_MA": 153.0, "200_day_MA": 160.0, "analysis": "The stock is currently below its 50-day moving average but above its 200-day moving average, suggesting a potential long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:35.137768", "processing_time": 7.239063, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "analysis": "RSI is in the middle of the range, indicating neither strong buying nor selling pressure."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "analysis": "MACD is close to the center line, suggesting a lack of strong trend."}, "Moving_Averages": {"50_day_MA": 153.0, "200_day_MA": 160.0, "analysis": "The stock is currently below its 50-day moving average but above its 200-day moving average, suggesting a potential long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:35.137768", "processing_time": 7.239063, "llm_used": true}, "processing_time": 7.239063, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 22.055574}}, {"timestamp": "2025-07-05T22:39:35.825033", "output_id": "output_20250705_223935_6bfeb90e", "input_id": "input_20250705_223927_851a9229", "prompt_id": "prompt_20250705_223927_a6d6c9e6", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:35.825033", "processing_time": 8.09189, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:35.825033", "processing_time": 8.09189, "llm_used": true}, "processing_time": 8.09189, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 30.147464}}, {"timestamp": "2025-07-05T22:39:36.648586", "output_id": "output_20250705_223936_1291ca63", "input_id": "input_20250705_223928_47d82b2a", "prompt_id": "prompt_20250705_223928_df612b7f", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:36.648586", "processing_time": 8.524749, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:36.648586", "processing_time": 8.524749, "llm_used": true}, "processing_time": 8.524749, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 38.672213}}, {"timestamp": "2025-07-05T22:39:37.341426", "output_id": "output_20250705_223937_65cddbad", "input_id": "input_20250705_223928_f8015758", "prompt_id": "prompt_20250705_223928_da669093", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:37.341426", "processing_time": 9.216598, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:37.341426", "processing_time": 9.216598, "llm_used": true}, "processing_time": 9.216598, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 47.888811}}, {"timestamp": "2025-07-05T22:39:38.205820", "output_id": "output_20250705_223938_31da5d68", "input_id": "input_20250705_223933_beeb0936", "prompt_id": "prompt_20250705_223933_e57b662e", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "interpretation": "neutral"}, "MACD": {"signal_line": 0.02, "interpretation": "neutral"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 170.0, "interpretation": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:38.204821", "processing_time": 4.864489, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 52, "interpretation": "neutral"}, "MACD": {"signal_line": 0.02, "interpretation": "neutral"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 170.0, "interpretation": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:38.204821", "processing_time": 4.864489, "llm_used": true}, "processing_time": 4.864489, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 52.753299999999996}}, {"timestamp": "2025-07-05T22:39:38.994105", "output_id": "output_20250705_223938_5d7cb3c5", "input_id": "input_20250705_223934_11213935", "prompt_id": "prompt_20250705_223934_4275bfb9", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:38.994105", "processing_time": 4.336679, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:38.994105", "processing_time": 4.336679, "llm_used": true}, "processing_time": 4.336679, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 57.089979}}, {"timestamp": "2025-07-05T22:39:39.092462", "output_id": "output_20250705_223939_42c3af04", "input_id": "input_20250705_223934_1011dd49", "prompt_id": "prompt_20250705_223934_a7419796", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:39.092462", "processing_time": 4.702107, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:39.092462", "processing_time": 4.702107, "llm_used": true}, "processing_time": 4.702107, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 61.792086}}, {"timestamp": "2025-07-05T22:39:39.432318", "output_id": "output_20250705_223939_bb943c08", "input_id": "input_20250705_223934_d6954bc8", "prompt_id": "prompt_20250705_223934_8b5dfa1c", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating that the stock is in a bullish trend."}, "MACD": {"current_value": "positive", "analysis": "MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, supporting the bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:39.418391", "processing_time": 4.800269, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating that the stock is in a bullish trend."}, "MACD": {"current_value": "positive", "analysis": "MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, supporting the bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:39.418391", "processing_time": 4.800269, "llm_used": true}, "processing_time": 4.800269, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 66.592355}}, {"timestamp": "2025-07-05T22:39:39.461508", "output_id": "output_20250705_223939_921b035c", "input_id": "input_20250705_223934_3a94a50d", "prompt_id": "prompt_20250705_223934_592a08cf", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.25, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 63, "analysis": "The RSI is in the middle of the overbought/oversold range, suggesting a neutral trend."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD is close to zero, indicating a lack of strong trend direction."}, "Moving Averages": {"50-Day MA": 160.0, "200-Day MA": 180.0, "analysis": "The stock is currently trading below its 50-day and 200-day moving averages, which suggests a bearish trend in the short and medium term."}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:39.444831", "processing_time": 5.33565, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.25, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 63, "analysis": "The RSI is in the middle of the overbought/oversold range, suggesting a neutral trend."}, "MACD": {"signal_line": 0.02, "analysis": "The MACD is close to zero, indicating a lack of strong trend direction."}, "Moving Averages": {"50-Day MA": 160.0, "200-Day MA": 180.0, "analysis": "The stock is currently trading below its 50-day and 200-day moving averages, which suggests a bearish trend in the short and medium term."}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:39.444831", "processing_time": 5.33565, "llm_used": true}, "processing_time": 5.33565, "llm_used": true, "confidence": 0.75, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 71.928005}}, {"timestamp": "2025-07-05T22:39:40.039106", "output_id": "output_20250705_223940_5b5fbddf", "input_id": "input_20250705_223928_1e980c5a", "prompt_id": "prompt_20250705_223928_a589c729", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.2, "trend": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": {"bullish": false, "bearish": false}}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 160.0, "crossover": "50-Day_MA above 200-Day_MA"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:40.039106", "processing_time": 12.05226, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.2, "trend": "neutral"}, "MACD": {"signal_line": 0.1, "histogram": {"bullish": false, "bearish": false}}, "Moving_Averages": {"50-Day_MA": 152.5, "200-Day_MA": 160.0, "crossover": "50-Day_MA above 200-Day_MA"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:40.039106", "processing_time": 12.05226, "llm_used": true}, "processing_time": 12.05226, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 83.980265}}, {"timestamp": "2025-07-05T22:39:40.170440", "output_id": "output_20250705_223940_b84e45c6", "input_id": "input_20250705_223934_3ff8062b", "prompt_id": "prompt_20250705_223934_942819d7", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 63, "interpretation": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 140.0, "interpretation": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:40.170440", "processing_time": 5.820101, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 63, "interpretation": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 140.0, "interpretation": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:40.170440", "processing_time": 5.820101, "llm_used": true}, "processing_time": 5.820101, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 89.800366}}, {"timestamp": "2025-07-05T22:39:40.398394", "output_id": "output_20250705_223940_ee74b4b3", "input_id": "input_20250705_223927_3f8a5776", "prompt_id": "prompt_20250705_223928_10c65112", "raw_response": {"trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.01, "interpretation": "Neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:40.398394", "processing_time": 12.420598, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "Neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.01, "interpretation": "Neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:40.398394", "processing_time": 12.420598, "llm_used": true}, "processing_time": 12.420598, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 102.220964}}, {"timestamp": "2025-07-05T22:39:42.125376", "output_id": "output_20250705_223942_9e16149d", "input_id": "input_20250705_223934_a6cd8aee", "prompt_id": "prompt_20250705_223934_a2c8bc71", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating the stock is overbought, but the trend is strong."}, "MACD": {"signal_line": 0.1, "analysis": "MACD signal line is above the zero line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:42.125376", "processing_time": 8.030735, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating the stock is overbought, but the trend is strong."}, "MACD": {"signal_line": 0.1, "analysis": "MACD signal line is above the zero line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 140.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:42.125376", "processing_time": 8.030735, "llm_used": true}, "processing_time": 8.030735, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 110.251699}}, {"timestamp": "2025-07-05T22:39:42.308484", "output_id": "output_20250705_223942_833bbd20", "input_id": "input_20250705_223936_f577770d", "prompt_id": "prompt_20250705_223936_6754f919", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"current_value": "positive crossover", "analysis": "MACD shows a positive crossover, suggesting upward momentum."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "analysis": "Stock price is above both 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:42.308484", "processing_time": 5.434974, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"current_value": "positive crossover", "analysis": "MACD shows a positive crossover, suggesting upward momentum."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "analysis": "Stock price is above both 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:42.308484", "processing_time": 5.434974, "llm_used": true}, "processing_time": 5.434974, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 115.686673}}, {"timestamp": "2025-07-05T22:39:43.437516", "output_id": "output_20250705_223943_281a88b4", "input_id": "input_20250705_223934_2b4e0d8b", "prompt_id": "prompt_20250705_223934_f5aca39f", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "The RSI is above 70, indicating that the stock may be overbought, but it is still within a healthy range."}, "MACD": {"signal_line": 0.1, "histogram": {"current_value": 0.05, "trend": "upward"}, "interpretation": "The MACD signal line is above the zero line, and the histogram is rising, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:43.437516", "processing_time": 9.353819, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "The RSI is above 70, indicating that the stock may be overbought, but it is still within a healthy range."}, "MACD": {"signal_line": 0.1, "histogram": {"current_value": 0.05, "trend": "upward"}, "interpretation": "The MACD signal line is above the zero line, and the histogram is rising, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:43.437516", "processing_time": 9.353819, "llm_used": true}, "processing_time": 9.353819, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 125.040492}}, {"timestamp": "2025-07-05T22:39:43.551782", "output_id": "output_20250705_223943_3e39c88f", "input_id": "input_20250705_223933_9c3e1583", "prompt_id": "prompt_20250705_223933_dcd52ada", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 62, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 180.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:43.550777", "processing_time": 10.18764, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 62, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 180.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:43.550777", "processing_time": 10.18764, "llm_used": true}, "processing_time": 10.18764, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 135.228132}}, {"timestamp": "2025-07-05T22:39:51.861247", "output_id": "output_20250705_223951_34c58c4a", "input_id": "input_20250705_223945_14ac3b04", "prompt_id": "prompt_20250705_223945_7bf7f265", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:51.861247", "processing_time": 6.805316, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:51.861247", "processing_time": 6.805316, "llm_used": true}, "processing_time": 6.805316, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 142.033448}}, {"timestamp": "2025-07-05T22:39:58.358712", "output_id": "output_20250705_223958_904035cf", "input_id": "input_20250705_223948_6a5f253a", "prompt_id": "prompt_20250705_223948_be8cad5c", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "Indicates neutral market conditions."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "MACD is near zero, suggesting a lack of strong trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Stock is currently between its 50-day and 200-day moving averages, indicating a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:58.358712", "processing_time": 9.817433, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "Indicates neutral market conditions."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "MACD is near zero, suggesting a lack of strong trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Stock is currently between its 50-day and 200-day moving averages, indicating a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:58.358712", "processing_time": 9.817433, "llm_used": true}, "processing_time": 9.817433, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 151.850881}}, {"timestamp": "2025-07-05T22:39:59.285128", "output_id": "output_20250705_223959_46f523b0", "input_id": "input_20250705_223951_411a68f3", "prompt_id": "prompt_20250705_223951_a0e58819", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "analysis": "The RSI is above 60, indicating that the stock may be overbought, but the strong bullish trend suggests continued upward momentum."}, "MACD": {"value": "positive crossover", "analysis": "The MACD is showing a positive crossover, which is a bullish signal."}, "Moving Averages": {"50-day MA": {"value": 155.0, "analysis": "The 50-day moving average is above the current price, suggesting a long-term bullish trend."}, "200-day MA": {"value": 165.0, "analysis": "The 200-day moving average is also above the current price, reinforcing the bullish trend."}}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:59.285128", "processing_time": 8.232907, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "analysis": "The RSI is above 60, indicating that the stock may be overbought, but the strong bullish trend suggests continued upward momentum."}, "MACD": {"value": "positive crossover", "analysis": "The MACD is showing a positive crossover, which is a bullish signal."}, "Moving Averages": {"50-day MA": {"value": 155.0, "analysis": "The 50-day moving average is above the current price, suggesting a long-term bullish trend."}, "200-day MA": {"value": 165.0, "analysis": "The 200-day moving average is also above the current price, reinforcing the bullish trend."}}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:39:59.285128", "processing_time": 8.232907, "llm_used": true}, "processing_time": 8.232907, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 160.083788}}, {"timestamp": "2025-07-05T22:40:02.589121", "output_id": "output_20250705_224002_6c47c7ac", "input_id": "input_20250705_223954_040e439b", "prompt_id": "prompt_20250705_223954_9f7b789c", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 55, "analysis": "RSI is in the neutral zone, indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.0, "histogram": -0.01, "analysis": "The MACD line is close to the signal line and the histogram is slightly negative, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 175.0, "analysis": "The stock is currently below its 50-day moving average but above its 200-day moving average, which suggests a long-term bullish trend but a short-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:02.587112", "processing_time": 8.486314, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 55, "analysis": "RSI is in the neutral zone, indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.0, "histogram": -0.01, "analysis": "The MACD line is close to the signal line and the histogram is slightly negative, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 175.0, "analysis": "The stock is currently below its 50-day moving average but above its 200-day moving average, which suggests a long-term bullish trend but a short-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:02.587112", "processing_time": 8.486314, "llm_used": true}, "processing_time": 8.486314, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 168.570102}}, {"timestamp": "2025-07-05T22:40:03.768949", "output_id": "output_20250705_224003_b1840bf5", "input_id": "input_20250705_223958_47bc7dcc", "prompt_id": "prompt_20250705_223958_035f8eaa", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "analysis": "RSI is neutral; not overbought or oversold."}, "MACD": {"signal_line": 0.01, "analysis": "MACD signal line is close to the center, indicating a neutral trend."}, "Moving_Averages": {"50_day_MA": 153.5, "200_day_MA": 145.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, suggesting a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:03.753429", "processing_time": 5.285934, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "analysis": "RSI is neutral; not overbought or oversold."}, "MACD": {"signal_line": 0.01, "analysis": "MACD signal line is close to the center, indicating a neutral trend."}, "Moving_Averages": {"50_day_MA": 153.5, "200_day_MA": 145.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, suggesting a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:03.753429", "processing_time": 5.285934, "llm_used": true}, "processing_time": 5.285934, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 173.856036}}, {"timestamp": "2025-07-05T22:40:06.009919", "output_id": "output_20250705_224006_e82ac32e", "input_id": "input_20250705_223959_c4d5eedf", "prompt_id": "prompt_20250705_223959_998349dd", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "analysis": "RSI is above 50, indicating neither strong buying nor strong selling pressure."}, "MACD": {"current_value": "0.01", "analysis": "MACD is close to zero, suggesting that the trend is neutral and there is no strong momentum in either direction."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, which could suggest a bearish trend, but the 200-day MA is still above the current price, offering some support."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:06.009919", "processing_time": 6.305822, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "analysis": "RSI is above 50, indicating neither strong buying nor strong selling pressure."}, "MACD": {"current_value": "0.01", "analysis": "MACD is close to zero, suggesting that the trend is neutral and there is no strong momentum in either direction."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, which could suggest a bearish trend, but the 200-day MA is still above the current price, offering some support."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:06.009919", "processing_time": 6.305822, "llm_used": true}, "processing_time": 6.305822, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 180.161858}}, {"timestamp": "2025-07-05T22:40:07.032143", "output_id": "output_20250705_224007_6c74e427", "input_id": "input_20250705_223959_cc8fee8f", "prompt_id": "prompt_20250705_223959_162f8dc7", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:07.032143", "processing_time": 7.503431, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 140.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:07.032143", "processing_time": 7.503431, "llm_used": true}, "processing_time": 7.503431, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 187.665289}}, {"timestamp": "2025-07-05T22:40:07.556928", "output_id": "output_20250705_224007_3846e471", "input_id": "input_20250705_224003_2635c8e6", "prompt_id": "prompt_20250705_224003_56f32d97", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:07.556928", "processing_time": 4.303416, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:07.556928", "processing_time": 4.303416, "llm_used": true}, "processing_time": 4.303416, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 191.968705}}, {"timestamp": "2025-07-05T22:40:08.673138", "output_id": "output_20250705_224008_0e54f3dd", "input_id": "input_20250705_224000_b6778771", "prompt_id": "prompt_20250705_224000_563f7bef", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:08.673138", "processing_time": 8.582196, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:08.673138", "processing_time": 8.582196, "llm_used": true}, "processing_time": 8.582196, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 200.550901}}, {"timestamp": "2025-07-05T22:40:10.727942", "output_id": "output_20250705_224010_072c950f", "input_id": "input_20250705_224006_14a5efaa", "prompt_id": "prompt_20250705_224006_e215b8e5", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.02, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:10.725938", "processing_time": 4.238763, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.02, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:10.725938", "processing_time": 4.238763, "llm_used": true}, "processing_time": 4.238763, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 204.78966400000002}}, {"timestamp": "2025-07-05T22:40:11.261364", "output_id": "output_20250705_224011_39ddf453", "input_id": "input_20250705_223959_27d880fb", "prompt_id": "prompt_20250705_223959_85391aef", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 61, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "crossover": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:11.261364", "processing_time": 11.456292, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 61, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "crossover": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:11.261364", "processing_time": 11.456292, "llm_used": true}, "processing_time": 11.456292, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 216.245956}}, {"timestamp": "2025-07-05T22:40:12.642694", "output_id": "output_20250705_224012_fae21bf7", "input_id": "input_20250705_224004_777e1314", "prompt_id": "prompt_20250705_224004_a47bf623", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "histogram_positive": true, "signal": "bullish"}, "Moving_Averages": {"50_day_ma_crossover_200_day_ma": true, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:12.642694", "processing_time": 7.768201, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "histogram_positive": true, "signal": "bullish"}, "Moving_Averages": {"50_day_ma_crossover_200_day_ma": true, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:12.642694", "processing_time": 7.768201, "llm_used": true}, "processing_time": 7.768201, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 224.014157}}, {"timestamp": "2025-07-05T22:40:14.465520", "output_id": "output_20250705_224014_6afdb3bd", "input_id": "input_20250705_224002_5cb0a613", "prompt_id": "prompt_20250705_224002_74b8220a", "raw_response": {"analysis_date": "2025-01-15", "trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56, "trend": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.01, "trend": "neutral"}, "moving_averages": {"50_day": 155.0, "200_day": 145.0, "crossover": "neutral"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:14.465520", "processing_time": 11.826968, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-15", "trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56, "trend": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.01, "trend": "neutral"}, "moving_averages": {"50_day": 155.0, "200_day": 145.0, "crossover": "neutral"}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:14.465520", "processing_time": 11.826968, "llm_used": true}, "processing_time": 11.826968, "llm_used": true, "confidence": 0.75, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 235.841125}}, {"timestamp": "2025-07-05T22:40:16.332734", "output_id": "output_20250705_224016_6fe196fb", "input_id": "input_20250705_224005_f0c06173", "prompt_id": "prompt_20250705_224005_47b6f6a2", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:16.332734", "processing_time": 11.190777, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:40:16.332734", "processing_time": 11.190777, "llm_used": true}, "processing_time": 11.190777, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 247.031902}}]