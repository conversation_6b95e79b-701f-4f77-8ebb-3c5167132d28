#!/usr/bin/env python3
"""
简化的集成测试脚本

用于验证增强Shapley值OPRP工作流程的基本功能
"""

import os
import sys
import tempfile
import shutil
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def setup_test_environment():
    """设置测试环境"""
    # 创建临时目录
    test_dir = tempfile.mkdtemp()
    test_data_dir = Path(test_dir) / "test_data"
    test_data_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建测试配置
    test_config = {
        "enhanced_shapley_oprp": {
            "cycle_length_days": 14,
            "optimization_phase_days": 7,
            "validation_phase_days": 7,
            "underperforming_threshold": 0.3,
            "statistical_significance_level": 0.05
        },
        "use_only_winning_data": True,
        "min_data_points_per_agent": 3,
        "data_quality_threshold": 0.6,
        "enable_historical_tracking": True,
        "max_historical_weeks": 8,
        "storage": {
            "comprehensive_storage": {
                "base_dir": str(test_data_dir),
                "enable_database": False
            }
        }
    }
    
    return test_dir, test_data_dir, test_config

def test_basic_imports():
    """测试基本导入"""
    print("🧪 测试基本导入...")
    
    try:
        from contribution_assessment.iterative_shapley_calculator import IterativeShapleyCalculator, IterativeCalculationConfig
        print("✅ IterativeShapleyCalculator 导入成功")
        
        from contribution_assessment.dual_track_experiment_system import DualTrackExperimentSystem
        print("✅ DualTrackExperimentSystem 导入成功")
        
        from data.ab_testing_framework import ABTestingFramework
        print("✅ ABTestingFramework 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_iterative_shapley_calculator():
    """测试迭代Shapley计算器"""
    print("\n🧪 测试迭代Shapley计算器...")
    
    try:
        from contribution_assessment.iterative_shapley_calculator import IterativeShapleyCalculator, IterativeCalculationConfig
        
        test_dir, test_data_dir, test_config = setup_test_environment()
        
        # 创建配置
        config = IterativeCalculationConfig(
            use_only_winning_data=True,
            min_data_points_per_agent=3,
            data_quality_threshold=0.6
        )
        
        # 创建计算器
        calculator = IterativeShapleyCalculator(
            base_data_dir=str(test_data_dir),
            config=config,
            logger=logging.getLogger("test")
        )
        
        print("✅ 迭代Shapley计算器初始化成功")
        
        # 测试实验结果注册
        experiment_id = "test_exp_001"
        ab_test_results = {
            "NAA": {
                "agent_selections": {
                    "NAA": {
                        "selected_prompt": "optimized",
                        "reason": "Better performance",
                        "statistical_data": {"p_value": 0.03, "effect_size": 0.15},
                        "prompt_content": "Optimized NAA prompt"
                    }
                }
            }
        }
        
        performance_data = {
            "NAA": {
                "sharpe_ratio": 1.25,
                "total_return": 0.08,
                "volatility": 0.15,
                "max_drawdown": -0.05
            }
        }
        
        success = calculator.register_experiment_results(
            experiment_id=experiment_id,
            ab_test_results=ab_test_results,
            performance_data=performance_data
        )
        
        if success:
            print("✅ 实验结果注册成功")
        else:
            print("❌ 实验结果注册失败")
            return False
        
        # 清理
        shutil.rmtree(test_dir)
        return True
        
    except Exception as e:
        print(f"❌ 迭代Shapley计算器测试失败: {e}")
        return False

def test_dual_track_system():
    """测试双轨实验系统"""
    print("\n🧪 测试双轨实验系统...")
    
    try:
        from contribution_assessment.dual_track_experiment_system import DualTrackExperimentSystem
        
        test_dir, test_data_dir, test_config = setup_test_environment()
        
        # 创建双轨系统
        dual_track = DualTrackExperimentSystem(
            base_data_dir=str(test_data_dir),
            logger=logging.getLogger("test")
        )
        
        print("✅ 双轨实验系统初始化成功")
        
        # 创建实验
        from datetime import datetime
        experiment_config = {
            "experiment_name": "test_dual_track",
            "target_agents": ["NAA", "TAA"],
            "duration_days": 7,
            "start_date": datetime.now().strftime("%Y-%m-%d"),
            "original_prompts": {
                "NAA": "Original NAA prompt",
                "TAA": "Original TAA prompt"
            },
            "optimized_prompts": {
                "NAA": "Optimized NAA prompt",
                "TAA": "Optimized TAA prompt"
            }
        }
        
        experiment_id = dual_track.create_dual_track_experiment(experiment_config)
        
        if experiment_id:
            print("✅ 双轨实验创建成功")
        else:
            print("❌ 双轨实验创建失败")
            return False
        
        # 记录测试数据
        daily_data = {
            "original_track": {
                "NAA": {"return": 0.01, "sharpe_ratio": 0.8},
                "TAA": {"return": 0.008, "sharpe_ratio": 0.7}
            },
            "optimized_track": {
                "NAA": {"return": 0.012, "sharpe_ratio": 0.9},
                "TAA": {"return": 0.009, "sharpe_ratio": 0.75}
            }
        }
        
        success = dual_track.record_daily_experiment_data(
            experiment_id, "1", daily_data
        )
        
        if success:
            print("✅ 实验数据记录成功")
        else:
            print("❌ 实验数据记录失败")
            return False
        
        # 清理
        shutil.rmtree(test_dir)
        return True
        
    except Exception as e:
        print(f"❌ 双轨实验系统测试失败: {e}")
        return False

def test_ab_testing_framework():
    """测试A/B测试框架"""
    print("\n🧪 测试A/B测试框架...")
    
    try:
        from data.ab_testing_framework import ABTestingFramework
        from data.comprehensive_storage_manager import ComprehensiveStorageManager
        
        test_dir, test_data_dir, test_config = setup_test_environment()
        
        # 创建存储管理器
        storage_config = {
            "base_dir": str(test_data_dir),
            "enable_database": False
        }
        storage_manager = ComprehensiveStorageManager(storage_config)
        
        # 创建A/B测试框架
        ab_framework = ABTestingFramework(
            storage_manager=storage_manager,
            logger=logging.getLogger("test")
        )
        
        print("✅ A/B测试框架初始化成功")
        
        # 清理
        shutil.rmtree(test_dir)
        return True
        
    except Exception as e:
        print(f"❌ A/B测试框架测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始简化集成测试")
    print("="*50)
    
    # 设置日志
    logging.basicConfig(level=logging.WARNING)
    
    test_results = []
    
    # 运行测试
    test_results.append(("基本导入", test_basic_imports()))
    test_results.append(("迭代Shapley计算器", test_iterative_shapley_calculator()))
    test_results.append(("双轨实验系统", test_dual_track_system()))
    test_results.append(("A/B测试框架", test_ab_testing_framework()))
    
    # 输出结果
    print("\n" + "="*50)
    print("📊 测试结果摘要:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / total * 100) if total > 0 else 0
    print(f"\n总体结果: {passed}/{total} 通过 ({success_rate:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！系统集成验证成功")
        return 0
    else:
        print("⚠️  部分测试失败，需要进一步调试")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
