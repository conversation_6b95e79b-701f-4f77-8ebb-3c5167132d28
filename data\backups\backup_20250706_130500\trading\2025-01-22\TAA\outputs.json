[{"timestamp": "2025-07-05T22:54:34.144615", "output_id": "output_20250705_225434_436438ff", "input_id": "input_20250705_225429_f98cff77", "prompt_id": "prompt_20250705_225429_710764cc", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:54:34.144615", "processing_time": 4.529698, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:54:34.144615", "processing_time": 4.529698, "llm_used": true}, "processing_time": 4.529698, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 4.529698}}, {"timestamp": "2025-07-05T22:55:22.718229", "output_id": "output_20250705_225522_8a777af7", "input_id": "input_20250705_225518_57568d5f", "prompt_id": "prompt_20250705_225519_a922f3f5", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:22.718229", "processing_time": 4.009196, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:22.718229", "processing_time": 4.009196, "llm_used": true}, "processing_time": 4.009196, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 8.538893999999999}}, {"timestamp": "2025-07-05T22:55:24.446758", "output_id": "output_20250705_225524_db1cf373", "input_id": "input_20250705_225519_c4a87016", "prompt_id": "prompt_20250705_225519_ec6db76c", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 63, "analysis": "Neutral with a slight upward trend"}, "MACD": {"signal_line": 0.01, "histogram": -0.03, "analysis": "Slightly bearish crossover, but not strong enough to signal a trend reversal"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 150.0, "analysis": "Currently trading below the 50-day and 200-day moving averages, indicating a possible bearish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:24.446758", "processing_time": 5.622656, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 135.0, "resistance_level": 145.0, "indicators": {"RSI": {"current_value": 63, "analysis": "Neutral with a slight upward trend"}, "MACD": {"signal_line": 0.01, "histogram": -0.03, "analysis": "Slightly bearish crossover, but not strong enough to signal a trend reversal"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 150.0, "analysis": "Currently trading below the 50-day and 200-day moving averages, indicating a possible bearish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:24.446758", "processing_time": 5.622656, "llm_used": true}, "processing_time": 5.622656, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 14.161549999999998}}, {"timestamp": "2025-07-05T22:55:25.043688", "output_id": "output_20250705_225525_234dd0a7", "input_id": "input_20250705_225518_6803d4ae", "prompt_id": "prompt_20250705_225519_97bee68f", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 54, "signal": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:25.043688", "processing_time": 6.283164, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 54, "signal": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:25.043688", "processing_time": 6.283164, "llm_used": true}, "processing_time": 6.283164, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 20.444713999999998}}, {"timestamp": "2025-07-05T22:55:25.202882", "output_id": "output_20250705_225525_a25ae522", "input_id": "input_20250705_225518_4e993255", "prompt_id": "prompt_20250705_225519_995286c7", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"current_value": "0.01", "analysis": "The MACD line is close to the signal line, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, which indicates a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:25.191367", "processing_time": 6.468244, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"current_value": "0.01", "analysis": "The MACD line is close to the signal line, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently between the 50-day and 200-day moving averages, which indicates a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:25.191367", "processing_time": 6.468244, "llm_used": true}, "processing_time": 6.468244, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 26.912957999999996}}, {"timestamp": "2025-07-05T22:55:27.875630", "output_id": "output_20250705_225527_beca25de", "input_id": "input_20250705_225523_4be570d5", "prompt_id": "prompt_20250705_225523_c59fd89e", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 67, "signal": "overbought"}, "MACD": {"signal_line": 20, "histogram": 0.5, "signal": "bullish crossover"}, "MA": {"50_day": 155.0, "200_day": 145.0, "crossover": "50_day above 200_day"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:27.875630", "processing_time": 4.285152, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 67, "signal": "overbought"}, "MACD": {"signal_line": 20, "histogram": 0.5, "signal": "bullish crossover"}, "MA": {"50_day": 155.0, "200_day": 145.0, "crossover": "50_day above 200_day"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:27.875630", "processing_time": 4.285152, "llm_used": true}, "processing_time": 4.285152, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 31.198109999999996}}, {"timestamp": "2025-07-05T22:55:28.567378", "output_id": "output_20250705_225528_74e80037", "input_id": "input_20250705_225523_3550d963", "prompt_id": "prompt_20250705_225523_6a0eb6d7", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:28.567378", "processing_time": 4.905966, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:28.567378", "processing_time": 4.905966, "llm_used": true}, "processing_time": 4.905966, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 36.104076}}, {"timestamp": "2025-07-05T22:55:28.608630", "output_id": "output_20250705_225528_79ee91f3", "input_id": "input_20250705_225518_d779938b", "prompt_id": "prompt_20250705_225519_dacef964", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "interpretation": "slightly above neutral, indicating a balanced market"}, "MACD": {"signal_line": 0.0, "histogram": -0.02, "interpretation": "signal line close to zero with a slightly negative histogram, suggesting a weak trend"}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "50-day MA is above the 200-day MA, suggesting a bullish long-term trend, but short-term volatility"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:28.607626", "processing_time": 9.8521, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "interpretation": "slightly above neutral, indicating a balanced market"}, "MACD": {"signal_line": 0.0, "histogram": -0.02, "interpretation": "signal line close to zero with a slightly negative histogram, suggesting a weak trend"}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "50-day MA is above the 200-day MA, suggesting a bullish long-term trend, but short-term volatility"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:28.607626", "processing_time": 9.8521, "llm_used": true}, "processing_time": 9.8521, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 45.956176}}, {"timestamp": "2025-07-05T22:55:28.927447", "output_id": "output_20250705_225528_11f78a9c", "input_id": "input_20250705_225518_4bd4eac2", "prompt_id": "prompt_20250705_225518_cd456add", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "interpretation": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "bearish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "interpretation": "short-term MA crossing below long-term MA"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:28.927447", "processing_time": 10.278145, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "interpretation": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "bearish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "interpretation": "short-term MA crossing below long-term MA"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:28.927447", "processing_time": 10.278145, "llm_used": true}, "processing_time": 10.278145, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 56.234321}}, {"timestamp": "2025-07-05T22:55:28.952403", "output_id": "output_20250705_225528_684439e7", "input_id": "input_20250705_225518_14e3bb7b", "prompt_id": "prompt_20250705_225518_f472a318", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 60, "interpretation": "slightly overbought"}, "MACD": {"signal_line": 0.01, "histogram": -0.05, "interpretation": "weak bearish signal"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 150.0, "interpretation": "50-day MA crossing below 200-day MA, indicating potential bearish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:28.952403", "processing_time": 10.293577, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 60, "interpretation": "slightly overbought"}, "MACD": {"signal_line": 0.01, "histogram": -0.05, "interpretation": "weak bearish signal"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 150.0, "interpretation": "50-day MA crossing below 200-day MA, indicating potential bearish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:28.952403", "processing_time": 10.293577, "llm_used": true}, "processing_time": 10.293577, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 66.52789800000001}}, {"timestamp": "2025-07-05T22:55:30.522109", "output_id": "output_20250705_225530_6fddcd5b", "input_id": "input_20250705_225525_da1cd84c", "prompt_id": "prompt_20250705_225525_86e00fe7", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1, "support_level": 149.5, "resistance_level": 153.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "MA_50": {"current_value": 150.0, "signal": "above"}, "MA_200": {"current_value": 140.0, "signal": "above"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:30.522109", "processing_time": 5.35168, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1, "support_level": 149.5, "resistance_level": 153.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "MA_50": {"current_value": 150.0, "signal": "above"}, "MA_200": {"current_value": 140.0, "signal": "above"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:30.522109", "processing_time": 5.35168, "llm_used": true}, "processing_time": 5.35168, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 71.87957800000001}}, {"timestamp": "2025-07-05T22:55:30.559253", "output_id": "output_20250705_225530_ad13c231", "input_id": "input_20250705_225525_60ef2760", "prompt_id": "prompt_20250705_225525_a85d956e", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 15, "analysis": "MACD signal line is above the MACD line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 155, "200_day_MA": 145, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is bullish."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:30.559253", "processing_time": 5.326899, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a bullish trend."}, "MACD": {"signal_line": 15, "analysis": "MACD signal line is above the MACD line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 155, "200_day_MA": 145, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which is bullish."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:30.559253", "processing_time": 5.326899, "llm_used": true}, "processing_time": 5.326899, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 77.206477}}, {"timestamp": "2025-07-05T22:55:30.865878", "output_id": "output_20250705_225530_4524e9fc", "input_id": "input_20250705_225524_9f1c2161", "prompt_id": "prompt_20250705_225524_d28ee70e", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.5, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:30.865878", "processing_time": 6.544047, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.5, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:30.865878", "processing_time": 6.544047, "llm_used": true}, "processing_time": 6.544047, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 83.75052400000001}}, {"timestamp": "2025-07-05T22:55:31.052551", "output_id": "output_20250705_225531_1406bdb5", "input_id": "input_20250705_225519_8d6da7cc", "prompt_id": "prompt_20250705_225519_02f0c43f", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": -0.03}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:31.052551", "processing_time": 12.232464, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": -0.03}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:31.052551", "processing_time": 12.232464, "llm_used": true}, "processing_time": 12.232464, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 95.982988}}, {"timestamp": "2025-07-05T22:55:31.608890", "output_id": "output_20250705_225531_01ca1d3a", "input_id": "input_20250705_225524_93f43c54", "prompt_id": "prompt_20250705_225524_c844bab2", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:31.607886", "processing_time": 7.12414, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:31.607886", "processing_time": 7.12414, "llm_used": true}, "processing_time": 7.12414, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 103.107128}}, {"timestamp": "2025-07-05T22:55:33.475279", "output_id": "output_20250705_225533_29395d46", "input_id": "input_20250705_225529_d6c689a6", "prompt_id": "prompt_20250705_225529_3caf6a84", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought but still in a strong uptrend."}, "MACD": {"signal_line": 0.05, "analysis": "The MACD line is above the signal line and is increasing, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:33.475279", "processing_time": 4.440149, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought but still in a strong uptrend."}, "MACD": {"signal_line": 0.05, "analysis": "The MACD line is above the signal line and is increasing, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:33.475279", "processing_time": 4.440149, "llm_used": true}, "processing_time": 4.440149, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 107.54727700000001}}, {"timestamp": "2025-07-05T22:55:33.614975", "output_id": "output_20250705_225533_2bd284ff", "input_id": "input_20250705_225528_c584b22c", "prompt_id": "prompt_20250705_225528_efc38516", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 50, indicating that the stock is in a strong uptrend."}, "MACD": {"signal_line": 0.02, "analysis": "MACD is in positive territory, suggesting a bullish trend."}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 160.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, supporting a bullish outlook."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:33.614975", "processing_time": 5.146704, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 50, indicating that the stock is in a strong uptrend."}, "MACD": {"signal_line": 0.02, "analysis": "MACD is in positive territory, suggesting a bullish trend."}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 160.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, supporting a bullish outlook."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:33.614975", "processing_time": 5.146704, "llm_used": true}, "processing_time": 5.146704, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 112.69398100000001}}, {"timestamp": "2025-07-05T22:55:34.964624", "output_id": "output_20250705_225534_7704a88b", "input_id": "input_20250705_225526_2a52fa12", "prompt_id": "prompt_20250705_225526_4b43a12d", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "crossover": "bullish"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "crossover": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:34.964624", "processing_time": 8.380321, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line_above_zero_line": true, "crossover": "bullish"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "crossover": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:34.964624", "processing_time": 8.380321, "llm_used": true}, "processing_time": 8.380321, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 121.074302}}, {"timestamp": "2025-07-05T22:55:35.115977", "output_id": "output_20250705_225535_135ed095", "input_id": "input_20250705_225527_f9da2d71", "prompt_id": "prompt_20250705_225527_02b07190", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is in the middle to high range, indicating a strong bullish momentum."}, "MACD": {"signal_line": 100, "histogram": 0.5, "analysis": "The MACD signal line is above the zero line with a positive histogram, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 140, "200_day_MA": 130, "analysis": "The stock price is above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:35.115977", "processing_time": 7.550282, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is in the middle to high range, indicating a strong bullish momentum."}, "MACD": {"signal_line": 100, "histogram": 0.5, "analysis": "The MACD signal line is above the zero line with a positive histogram, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 140, "200_day_MA": 130, "analysis": "The stock price is above both the 50-day and 200-day moving averages, confirming a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:35.115977", "processing_time": 7.550282, "llm_used": true}, "processing_time": 7.550282, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 128.624584}}, {"timestamp": "2025-07-05T22:55:43.133907", "output_id": "output_20250705_225543_1d480842", "input_id": "input_20250705_225536_876c3acc", "prompt_id": "prompt_20250705_225536_b3109352", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 69, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "interpretation": "Positive crossover and rising histogram indicates bullish momentum"}, "Moving_Averages": {"50_day_MA": 145.0, "200_day_MA": 120.0, "interpretation": "Stock price above 50-day and 200-day moving averages, indicating a strong bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:43.133907", "processing_time": 7.123242, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 155.0, "indicators": {"RSI": {"value": 69, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.03, "interpretation": "Positive crossover and rising histogram indicates bullish momentum"}, "Moving_Averages": {"50_day_MA": 145.0, "200_day_MA": 120.0, "interpretation": "Stock price above 50-day and 200-day moving averages, indicating a strong bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:43.133907", "processing_time": 7.123242, "llm_used": true}, "processing_time": 7.123242, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 135.747826}}, {"timestamp": "2025-07-05T22:55:51.516876", "output_id": "output_20250705_225551_8fdda4d9", "input_id": "input_20250705_225546_a14ea8a9", "prompt_id": "prompt_20250705_225546_503afa00", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 60.5, "interpretation": "Neutral, market is neither overbought nor oversold."}, "MACD": {"signal_line": 0.0, "histogram": 0.02, "interpretation": "Signal line is close to zero, indicating a neutral trend."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "interpretation": "The stock is currently trading below its 50-day MA but above its 200-day MA, suggesting a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:51.516876", "processing_time": 4.546677, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 60.5, "interpretation": "Neutral, market is neither overbought nor oversold."}, "MACD": {"signal_line": 0.0, "histogram": 0.02, "interpretation": "Signal line is close to zero, indicating a neutral trend."}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "interpretation": "The stock is currently trading below its 50-day MA but above its 200-day MA, suggesting a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:51.516876", "processing_time": 4.546677, "llm_used": true}, "processing_time": 4.546677, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 140.294503}}, {"timestamp": "2025-07-05T22:55:54.040470", "output_id": "output_20250705_225554_e494e5bf", "input_id": "input_20250705_225545_6d599c5f", "prompt_id": "prompt_20250705_225545_47d0425d", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.02, "analysis": "MACD signal line is above the zero line, suggesting bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "Stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:54.040470", "processing_time": 8.83894, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 0.02, "analysis": "MACD signal line is above the zero line, suggesting bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "Stock price is above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:54.040470", "processing_time": 8.83894, "llm_used": true}, "processing_time": 8.83894, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 149.133443}}, {"timestamp": "2025-07-05T22:55:56.117442", "output_id": "output_20250705_225556_7a42632c", "input_id": "input_20250705_225543_0edd936a", "prompt_id": "prompt_20250705_225543_88408333", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:56.077733", "processing_time": 12.550922, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:56.077733", "processing_time": 12.550922, "llm_used": true}, "processing_time": 12.550922, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 161.684365}}, {"timestamp": "2025-07-05T22:55:57.621380", "output_id": "output_20250705_225557_c214bfe4", "input_id": "input_20250705_225551_b0d1578d", "prompt_id": "prompt_20250705_225551_bf4e74a7", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 149.75, "resistance_level": 153.25, "indicators": {"RSI": {"current_value": 55.3, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": -0.1, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 151.5, "200_day_MA": 142.0, "trend": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:57.610474", "processing_time": 6.486425, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 149.75, "resistance_level": 153.25, "indicators": {"RSI": {"current_value": 55.3, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": -0.1, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 151.5, "200_day_MA": 142.0, "trend": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:55:57.610474", "processing_time": 6.486425, "llm_used": true}, "processing_time": 6.486425, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 168.17079}}, {"timestamp": "2025-07-05T22:56:01.118028", "output_id": "output_20250705_225601_6ab1270b", "input_id": "input_20250705_225552_f0bcb79b", "prompt_id": "prompt_20250705_225552_d69e600f", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "analysis": "The MACD signal line is slightly positive and the histogram is negative, suggesting a slight bullish trend but with caution."}, "Moving_Averages": {"50-Day_MA": 152.0, "200-Day_MA": 145.0, "analysis": "The stock is currently above the 50-day MA but below the 200-day MA, suggesting a potential bullish trend in the short term."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:01.118028", "processing_time": 8.570012, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is in the middle of the overbought/oversold range, indicating a neutral trend."}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "analysis": "The MACD signal line is slightly positive and the histogram is negative, suggesting a slight bullish trend but with caution."}, "Moving_Averages": {"50-Day_MA": 152.0, "200-Day_MA": 145.0, "analysis": "The stock is currently above the 50-day MA but below the 200-day MA, suggesting a potential bullish trend in the short term."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:01.118028", "processing_time": 8.570012, "llm_used": true}, "processing_time": 8.570012, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 176.740802}}, {"timestamp": "2025-07-05T22:56:01.645675", "output_id": "output_20250705_225601_bfbb6af0", "input_id": "input_20250705_225553_ec7ebe11", "prompt_id": "prompt_20250705_225553_62bedab9", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:01.645675", "processing_time": 7.892679, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:01.645675", "processing_time": 7.892679, "llm_used": true}, "processing_time": 7.892679, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 184.633481}}, {"timestamp": "2025-07-05T22:56:03.204376", "output_id": "output_20250705_225603_51899ee3", "input_id": "input_20250705_225552_c7c7373b", "prompt_id": "prompt_20250705_225552_3c7eff6c", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52.3, "analysis": "RSI is neutral, suggesting no strong overbought or oversold conditions."}, "MACD": {"current_value": 0.02, "analysis": "MACD is close to zero, indicating a lack of strong momentum in either direction."}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 160.0, "analysis": "The stock is currently below the 50-day and 200-day moving averages, suggesting a potential bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:03.204376", "processing_time": 10.331583, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52.3, "analysis": "RSI is neutral, suggesting no strong overbought or oversold conditions."}, "MACD": {"current_value": 0.02, "analysis": "MACD is close to zero, indicating a lack of strong momentum in either direction."}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 160.0, "analysis": "The stock is currently below the 50-day and 200-day moving averages, suggesting a potential bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:03.204376", "processing_time": 10.331583, "llm_used": true}, "processing_time": 10.331583, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 194.96506399999998}}, {"timestamp": "2025-07-05T22:56:04.092870", "output_id": "output_20250705_225604_48c2a027", "input_id": "input_20250705_225551_96eef211", "prompt_id": "prompt_20250705_225551_7c0e09ff", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought, but considering the bullish news, it's still supportive of the trend."}, "MACD": {"current_value": "positive", "analysis": "The MACD line is above the signal line, suggesting upward momentum."}, "Moving_Average": {"short_term": {"current_value": 155.0, "trend": "upward"}, "long_term": {"current_value": 145.0, "trend": "upward"}, "analysis": "Both short-term and long-term moving averages are trending upward, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:04.092870", "processing_time": 12.738828, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought, but considering the bullish news, it's still supportive of the trend."}, "MACD": {"current_value": "positive", "analysis": "The MACD line is above the signal line, suggesting upward momentum."}, "Moving_Average": {"short_term": {"current_value": 155.0, "trend": "upward"}, "long_term": {"current_value": 145.0, "trend": "upward"}, "analysis": "Both short-term and long-term moving averages are trending upward, indicating a strong bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:04.092870", "processing_time": 12.738828, "llm_used": true}, "processing_time": 12.738828, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 207.703892}}, {"timestamp": "2025-07-05T22:56:09.019210", "output_id": "output_20250705_225609_e42b6d25", "input_id": "input_20250705_225601_7cc7d812", "prompt_id": "prompt_20250705_225601_3a692033", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56, "interpretation": "indicating a neutral trend"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "signal line close to zero with a slightly bearish histogram, suggesting a weak trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "interpretation": "prices hovering around the 50-day MA with a downward trend towards the 200-day MA, suggesting a neutral to bearish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:09.019210", "processing_time": 7.194489, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56, "interpretation": "indicating a neutral trend"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "interpretation": "signal line close to zero with a slightly bearish histogram, suggesting a weak trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "interpretation": "prices hovering around the 50-day MA with a downward trend towards the 200-day MA, suggesting a neutral to bearish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:09.019210", "processing_time": 7.194489, "llm_used": true}, "processing_time": 7.194489, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 214.898381}}, {"timestamp": "2025-07-05T22:56:09.325406", "output_id": "output_20250705_225609_9106ead2", "input_id": "input_20250705_225559_4f07d4f2", "prompt_id": "prompt_20250705_225559_fd0d709e", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is in the middle of the range, indicating neither overbought nor oversold conditions."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD signal line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently trading above its 50-day MA but below its 200-day MA, indicating a short-term bullish trend but a long-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:09.325406", "processing_time": 9.450828, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 62, "analysis": "RSI is in the middle of the range, indicating neither overbought nor oversold conditions."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD signal line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently trading above its 50-day MA but below its 200-day MA, indicating a short-term bullish trend but a long-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:09.325406", "processing_time": 9.450828, "llm_used": true}, "processing_time": 9.450828, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 224.349209}}, {"timestamp": "2025-07-05T22:56:10.107012", "output_id": "output_20250705_225610_19cbe524", "input_id": "input_20250705_225605_ad46f18f", "prompt_id": "prompt_20250705_225605_4d7ce74e", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 60, indicating that the stock is in a growth phase."}, "MACD": {"current_value": "positive", "analysis": "MACD is showing a positive slope, suggesting upward momentum."}, "Moving_Average": {"short_term": 5, "long_term": 50, "analysis": "The stock price is above both the short-term and long-term moving averages, indicating a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:10.107012", "processing_time": 4.818666, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 60, indicating that the stock is in a growth phase."}, "MACD": {"current_value": "positive", "analysis": "MACD is showing a positive slope, suggesting upward momentum."}, "Moving_Average": {"short_term": 5, "long_term": 50, "analysis": "The stock price is above both the short-term and long-term moving averages, indicating a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:10.107012", "processing_time": 4.818666, "llm_used": true}, "processing_time": 4.818666, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 229.167875}}, {"timestamp": "2025-07-05T22:56:10.226102", "output_id": "output_20250705_225610_48e140b1", "input_id": "input_20250705_225555_3f9bd257", "prompt_id": "prompt_20250705_225555_2b95c38c", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:10.225101", "processing_time": 14.919956, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 135.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:10.225101", "processing_time": 14.919956, "llm_used": true}, "processing_time": 14.919956, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 244.08783100000002}}, {"timestamp": "2025-07-05T22:56:10.375354", "output_id": "output_20250705_225610_a0a10966", "input_id": "input_20250705_225604_52588f5b", "prompt_id": "prompt_20250705_225604_19318560", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:10.375354", "processing_time": 5.42928, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:56:10.375354", "processing_time": 5.42928, "llm_used": true}, "processing_time": 5.42928, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 249.51711100000003}}]