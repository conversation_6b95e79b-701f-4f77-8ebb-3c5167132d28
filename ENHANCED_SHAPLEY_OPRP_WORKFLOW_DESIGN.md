# 增强的基于Shapley值的OPRP智能体优化工作流程设计

## 系统概述

本文档详细描述了增强的基于Shapley值的OPRP（Optimization by PROmpting with Preference）智能体优化工作流程，实现了一个完整的14天双轨实验系统，旨在通过智能化的性能分析和提示词优化来提升多智能体交易系统的整体表现。

## 核心工作流程架构

### 14天双轨实验周期

```
第1-7天：Shapley分析与OPRP优化周期
├── 日常交易执行（所有智能体使用当前提示词）
├── 第7天：触发Shapley值计算
├── 识别低表现智能体（底部20-30%）
├── 对低表现智能体执行OPRP优化
└── 准备双轨实验配置

第8-14天：A/B测试验证周期
├── 双轨并行实验
│   ├── 轨道A：原始提示词实验
│   └── 轨道B：优化提示词实验
├── 实时性能监控和数据收集
├── 第14天：性能比较与决策
└── 自动选择最优提示词版本
```

### 关键组件架构

```
EnhancedShapleyOPRPSystem
├── EnhancedWeeklyOPROManager          # 增强的周期管理器
├── DualTrackExperimentSystem          # 双轨实验系统
├── AdvancedABTestingFramework         # 增强A/B测试框架
├── IterativeShapleyCalculator         # 迭代Shapley计算器
├── PerformanceBasedPromptSelector     # 基于性能的提示词选择器
└── ComprehensiveDataManager           # 综合数据管理器
```

## 详细工作流程设计

### 阶段1：周期性Shapley分析（第1-7天）

#### 1.1 日常交易执行
- 所有智能体使用当前活跃提示词执行日常交易
- 记录每日交易决策、市场条件和性能指标
- 数据存储路径：`/data/trading/{date}/{agent_name}/`

#### 1.2 Shapley值计算触发（第7天）
```python
# 触发条件检查
if current_day == 7 and week_complete:
    shapley_trigger.trigger_weekly_analysis(week_number)
```

#### 1.3 低表现智能体识别
```python
# 基于Shapley值排序识别底部20-30%智能体
underperforming_threshold = 0.3  # 底部30%
sorted_agents = sort_by_shapley_values(shapley_results)
underperforming_agents = sorted_agents[-int(len(sorted_agents) * underperforming_threshold):]
```

#### 1.4 OPRP优化执行
- 仅对识别出的低表现智能体执行OPRP优化
- 生成优化后的提示词候选
- 保存原始提示词和优化提示词到独立文件

### 阶段2：双轨A/B测试验证（第8-14天）

#### 2.1 双轨实验配置
```python
experiment_config = {
    "optimized_agents": underperforming_agents,
    "non_optimized_agents": normal_performing_agents,
    "tracks": {
        "track_a": "original_prompts",
        "track_b": "optimized_prompts"
    },
    "duration_days": 7
}
```

#### 2.2 并行实验执行
- **轨道A**：使用原始提示词的实验组
- **轨道B**：使用优化提示词的实验组
- 非优化智能体继续使用现有提示词
- 实验数据完全分离存储

#### 2.3 实时性能监控
- 收集每日收益率、Sharpe比率、交易决策质量
- 计算累积性能指标
- 监控统计显著性

### 阶段3：性能比较与决策（第14天）

#### 3.1 性能指标比较
```python
performance_metrics = {
    "returns": compare_returns(track_a_data, track_b_data),
    "sharpe_ratio": compare_sharpe_ratios(track_a_data, track_b_data),
    "volatility": compare_volatility(track_a_data, track_b_data),
    "max_drawdown": compare_drawdowns(track_a_data, track_b_data)
}
```

#### 3.2 统计显著性检验
- 使用t检验验证性能差异的统计显著性
- 设置显著性水平（α = 0.05）
- 计算置信区间

#### 3.3 自动提示词选择
```python
def select_winning_prompt(agent_id, performance_comparison):
    if performance_comparison["significant"] and performance_comparison["improvement"] > 0:
        return "optimized_prompt"
    else:
        return "original_prompt"
```

## 数据存储结构设计

### 增强的存储层次结构

```
data/trading/
├── shapley_analysis/
│   ├── week_1/
│   │   ├── coalition_experiments/
│   │   ├── shapley_results.json
│   │   ├── underperforming_agents.json
│   │   └── optimization_targets.json
│   └── week_2/
├── oprp_experiments/
│   ├── week_1_optimization/
│   │   ├── {agent_name}/
│   │   │   ├── original_prompt.json
│   │   │   ├── optimized_prompt.json
│   │   │   └── optimization_metadata.json
│   └── week_2_validation/
│       ├── track_a_original/
│       │   └── {agent_name}/
│       │       ├── daily_performance/
│       │       └── experiment_logs/
│       └── track_b_optimized/
│           └── {agent_name}/
│               ├── daily_performance/
│               └── experiment_logs/
└── performance_comparisons/
    ├── week_1_vs_week_2/
    │   ├── statistical_analysis.json
    │   ├── performance_metrics.json
    │   └── winning_prompts.json
    └── historical_performance/
```

## 核心算法设计

### 1. 智能体性能评估算法

```python
def evaluate_agent_performance(agent_data, time_window=7):
    """
    评估智能体在指定时间窗口内的综合性能
    """
    metrics = {
        "shapley_contribution": calculate_shapley_value(agent_data),
        "individual_returns": calculate_returns(agent_data),
        "risk_adjusted_returns": calculate_sharpe_ratio(agent_data),
        "consistency_score": calculate_consistency(agent_data)
    }
    
    # 加权综合评分
    weights = {"shapley": 0.4, "returns": 0.3, "sharpe": 0.2, "consistency": 0.1}
    composite_score = sum(metrics[key] * weights[key.split('_')[0]] for key in metrics)
    
    return composite_score, metrics
```

### 2. 迭代Shapley计算逻辑

```python
def calculate_iterative_shapley(week_number, experiment_results=None):
    """
    迭代Shapley值计算，考虑实验结果
    """
    if experiment_results and week_number > 1:
        # 使用获胜实验的数据进行计算
        winning_data = extract_winning_experiment_data(experiment_results)
        return calculate_shapley_values(winning_data)
    else:
        # 使用标准数据进行计算
        return calculate_standard_shapley_values(week_number)
```

### 3. 自适应优化策略

```python
def adaptive_optimization_strategy(agent_performance_history):
    """
    基于历史性能的自适应优化策略
    """
    if len(agent_performance_history) < 3:
        return "standard_opro"
    
    recent_trend = analyze_performance_trend(agent_performance_history[-3:])
    
    if recent_trend == "declining":
        return "aggressive_optimization"
    elif recent_trend == "stable_low":
        return "focused_optimization"
    else:
        return "maintenance_optimization"
```

## 系统集成点

### 与现有OPRP系统的集成

1. **run_opro_system.py** 集成点
   - 在 `run_weekly_opro_optimization()` 函数中集成新的工作流程
   - 替换现有的简单周期性优化逻辑

2. **ContributionAssessor** 扩展
   - 添加双轨实验支持
   - 集成增强的性能评估方法

3. **数据存储系统** 扩展
   - 扩展现有的 `ComprehensiveStorageManager`
   - 添加实验数据分离和管理功能

## 配置参数设计

```json
{
  "enhanced_shapley_oprp": {
    "enabled": true,
    "cycle_length_days": 14,
    "optimization_phase_days": 7,
    "validation_phase_days": 7,
    "underperforming_threshold": 0.3,
    "statistical_significance_level": 0.05,
    "minimum_improvement_threshold": 0.02,
    "max_concurrent_experiments": 4,
    "auto_prompt_selection": true,
    "backup_original_prompts": true
  }
}
```

## 错误处理和恢复机制

### 1. 实验失败处理
- 自动重试机制
- 降级到传统优化方法
- 数据完整性检查

### 2. 数据一致性保障
- 事务性数据操作
- 自动备份机制
- 版本控制和回滚功能

### 3. 性能监控
- 实时系统性能监控
- 异常检测和告警
- 资源使用优化

## 下一步实施计划

1. **创建核心组件类**
2. **实现数据存储扩展**
3. **开发测试套件**
4. **集成现有系统**
5. **性能验证和优化**

本设计文档为后续的具体实现提供了详细的架构指导和技术规范。
