#!/usr/bin/env python3
"""
A/B测试框架 (A/B Testing Framework)

为提示词优化提供A/B测试和实验验证功能：
1. 对比实验设计和执行
2. 统计显著性测试
3. 性能指标对比分析
4. 实验结果报告生成
5. 多变体测试支持

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from scipy import stats
import uuid

from .comprehensive_storage_manager import ComprehensiveStorageManager

@dataclass
class ABTestConfig:
    """A/B测试配置"""
    test_name: str
    agent_id: str
    variants: List[str]  # 提示词变体列表
    test_duration_hours: int = 24
    min_sample_size: int = 10
    significance_level: float = 0.05
    success_metric: str = "shapley_value"
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ABTestResult:
    """A/B测试结果"""
    variant_id: str
    sample_size: int
    mean_performance: float
    std_performance: float
    confidence_interval: Tuple[float, float]
    raw_data: List[float]

@dataclass
class ABTestAnalysis:
    """A/B测试分析结果"""
    test_id: str
    test_name: str
    agent_id: str
    start_time: str
    end_time: str
    total_samples: int
    variants_results: List[ABTestResult]
    statistical_analysis: Dict[str, Any]
    recommendations: List[str]
    best_variant: str
    confidence_level: float

class ABTestingFramework:
    """
    A/B测试框架
    
    提供完整的A/B测试功能，用于验证提示词优化效果
    """
    
    def __init__(self, 
                 storage_manager: ComprehensiveStorageManager,
                 logger: Optional[logging.Logger] = None):
        """
        初始化A/B测试框架
        
        参数:
            storage_manager: 存储管理器实例
            logger: 日志记录器
        """
        self.storage_manager = storage_manager
        self.logger = logger or self._create_default_logger()
        
        # 活跃测试跟踪
        self.active_tests = {}  # {test_id: ABTestConfig}
        self.test_results = {}  # {test_id: List[performance_data]}
        
        # 加载现有测试
        self._load_existing_tests()
        
        self.logger.info("A/B测试框架初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.ABTestingFramework")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _load_existing_tests(self):
        """加载现有的A/B测试"""
        try:
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT test_id, agent_id, test_name, variants, start_time, 
                           end_time, status, results
                    FROM ab_tests
                    WHERE status = 'active'
                """)
                
                rows = cursor.fetchall()
                
                for row in rows:
                    test_id, agent_id, test_name, variants_json, start_time, end_time, status, results_json = row
                    
                    try:
                        variants = json.loads(variants_json) if variants_json else []
                        
                        # 检查测试是否过期
                        if end_time and datetime.fromisoformat(end_time) < datetime.now():
                            # 自动结束过期测试
                            self._finalize_expired_test(test_id)
                            continue
                        
                        # 重建测试配置
                        config = ABTestConfig(
                            test_name=test_name,
                            agent_id=agent_id,
                            variants=variants
                        )
                        
                        self.active_tests[test_id] = config
                        
                        # 加载测试结果
                        if results_json:
                            self.test_results[test_id] = json.loads(results_json)
                        else:
                            self.test_results[test_id] = []
                        
                    except Exception as e:
                        self.logger.error(f"加载测试失败 {test_id}: {e}")
                
                self.logger.info(f"加载了 {len(self.active_tests)} 个活跃A/B测试")
                
        except Exception as e:
            self.logger.error(f"加载现有A/B测试失败: {e}")
    
    def start_ab_test(self, config: ABTestConfig) -> str:
        """
        启动A/B测试
        
        参数:
            config: A/B测试配置
            
        返回:
            测试ID
        """
        try:
            test_id = f"ab_test_{config.agent_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
            
            # 验证配置
            if not self._validate_test_config(config):
                raise ValueError("A/B测试配置无效")
            
            # 计算结束时间
            start_time = datetime.now()
            end_time = start_time + timedelta(hours=config.test_duration_hours)
            
            # 保存到数据库
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO ab_tests 
                    (test_id, agent_id, test_name, variants, start_time, end_time, 
                     status, results, statistical_analysis)
                    VALUES (?, ?, ?, ?, ?, ?, 'active', '[]', '{}')
                """, (
                    test_id,
                    config.agent_id,
                    config.test_name,
                    json.dumps(config.variants, ensure_ascii=False),
                    start_time.isoformat(),
                    end_time.isoformat()
                ))
                conn.commit()
            
            # 添加到活跃测试
            self.active_tests[test_id] = config
            self.test_results[test_id] = []
            
            self.logger.info(f"A/B测试已启动: {test_id} ({config.test_name})")
            return test_id
            
        except Exception as e:
            self.logger.error(f"启动A/B测试失败: {e}")
            return ""
    
    def record_test_result(self, 
                         test_id: str, 
                         variant_id: str, 
                         performance_score: float,
                         metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        记录测试结果
        
        参数:
            test_id: 测试ID
            variant_id: 变体ID
            performance_score: 性能分数
            metadata: 元数据
            
        返回:
            是否记录成功
        """
        try:
            if test_id not in self.active_tests:
                self.logger.error(f"测试不存在或已结束: {test_id}")
                return False
            
            # 验证变体ID
            config = self.active_tests[test_id]
            if variant_id not in [f"variant_{i}" for i in range(len(config.variants))]:
                self.logger.error(f"无效的变体ID: {variant_id}")
                return False
            
            # 记录结果
            result_record = {
                "variant_id": variant_id,
                "performance_score": performance_score,
                "timestamp": datetime.now().isoformat(),
                "metadata": metadata or {}
            }
            
            self.test_results[test_id].append(result_record)
            
            # 更新数据库
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE ab_tests 
                    SET results = ?
                    WHERE test_id = ?
                """, (
                    json.dumps(self.test_results[test_id], ensure_ascii=False),
                    test_id
                ))
                conn.commit()
            
            self.logger.debug(f"记录测试结果: {test_id} -> {variant_id} = {performance_score}")
            
            # 检查是否达到最小样本量
            self._check_test_completion(test_id)
            
            return True
            
        except Exception as e:
            self.logger.error(f"记录测试结果失败: {e}")
            return False
    
    def analyze_test_results(self, test_id: str) -> Optional[ABTestAnalysis]:
        """
        分析测试结果
        
        参数:
            test_id: 测试ID
            
        返回:
            分析结果
        """
        try:
            if test_id not in self.test_results:
                self.logger.error(f"测试结果不存在: {test_id}")
                return None
            
            config = self.active_tests.get(test_id)
            if not config:
                self.logger.error(f"测试配置不存在: {test_id}")
                return None
            
            results_data = self.test_results[test_id]
            if not results_data:
                self.logger.warning(f"测试暂无数据: {test_id}")
                return None
            
            # 按变体分组数据
            variant_data = {}
            for result in results_data:
                variant_id = result["variant_id"]
                if variant_id not in variant_data:
                    variant_data[variant_id] = []
                variant_data[variant_id].append(result["performance_score"])
            
            # 计算每个变体的统计信息
            variants_results = []
            for variant_id, scores in variant_data.items():
                if len(scores) > 0:
                    mean_score = np.mean(scores)
                    std_score = np.std(scores, ddof=1) if len(scores) > 1 else 0.0
                    
                    # 计算置信区间
                    if len(scores) > 1:
                        ci = stats.t.interval(
                            1 - config.significance_level,
                            len(scores) - 1,
                            loc=mean_score,
                            scale=stats.sem(scores)
                        )
                    else:
                        ci = (mean_score, mean_score)
                    
                    variants_results.append(ABTestResult(
                        variant_id=variant_id,
                        sample_size=len(scores),
                        mean_performance=mean_score,
                        std_performance=float(std_score),
                        confidence_interval=ci,
                        raw_data=scores
                    ))
            
            # 统计显著性测试
            statistical_analysis = self._perform_statistical_tests(variants_results, config.significance_level)
            
            # 确定最佳变体
            best_variant = max(variants_results, key=lambda x: x.mean_performance).variant_id
            
            # 生成建议
            recommendations = self._generate_test_recommendations(variants_results, statistical_analysis)
            
            # 计算置信水平
            confidence_level = self._calculate_overall_confidence(variants_results, statistical_analysis)
            
            analysis = ABTestAnalysis(
                test_id=test_id,
                test_name=config.test_name,
                agent_id=config.agent_id,
                start_time=datetime.now().isoformat(),  # 简化处理
                end_time=datetime.now().isoformat(),
                total_samples=len(results_data),
                variants_results=variants_results,
                statistical_analysis=statistical_analysis,
                recommendations=recommendations,
                best_variant=best_variant,
                confidence_level=confidence_level
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析测试结果失败: {e}")
            return None
    
    def _perform_statistical_tests(self, variants_results: List[ABTestResult], significance_level: float) -> Dict[str, Any]:
        """执行统计显著性测试"""
        analysis = {
            "significance_level": significance_level,
            "tests_performed": [],
            "significant_differences": []
        }
        
        try:
            # 如果只有两个变体，执行t检验
            if len(variants_results) == 2:
                var1, var2 = variants_results[0], variants_results[1]
                
                if len(var1.raw_data) > 1 and len(var2.raw_data) > 1:
                    # 独立样本t检验
                    t_stat, p_value = stats.ttest_ind(var1.raw_data, var2.raw_data)

                    # 处理可能的tuple类型
                    t_val = float(t_stat) if isinstance(t_stat, (int, float)) else float(t_stat[0]) if isinstance(t_stat, tuple) else 0.0
                    p_val = float(p_value) if isinstance(p_value, (int, float)) else float(p_value[0]) if isinstance(p_value, tuple) else 1.0

                    analysis["tests_performed"].append({
                        "test_type": "independent_t_test",
                        "variants": [var1.variant_id, var2.variant_id],
                        "t_statistic": t_val,
                        "p_value": p_val,
                        "significant": p_val < significance_level
                    })

                    if p_val < significance_level:
                        better_variant = var1.variant_id if var1.mean_performance > var2.mean_performance else var2.variant_id
                        analysis["significant_differences"].append({
                            "better_variant": better_variant,
                            "p_value": p_val,
                            "effect_size": abs(var1.mean_performance - var2.mean_performance)
                        })
            
            # 如果有多个变体，执行ANOVA
            elif len(variants_results) > 2:
                all_data = [var.raw_data for var in variants_results if len(var.raw_data) > 1]
                
                if len(all_data) >= 2:
                    f_stat, p_value = stats.f_oneway(*all_data)
                    
                    analysis["tests_performed"].append({
                        "test_type": "one_way_anova",
                        "variants": [var.variant_id for var in variants_results],
                        "f_statistic": float(f_stat),
                        "p_value": float(p_value),
                        "significant": p_value < significance_level
                    })
                    
                    # 如果ANOVA显著，进行事后检验
                    if p_value < significance_level:
                        analysis["significant_differences"].append({
                            "test_type": "anova",
                            "p_value": float(p_value),
                            "note": "存在显著差异，建议进行事后检验"
                        })
            
        except Exception as e:
            self.logger.error(f"统计测试失败: {e}")
            analysis["error"] = str(e)
        
        return analysis
    
    def _generate_test_recommendations(self, variants_results: List[ABTestResult], statistical_analysis: Dict[str, Any]) -> List[str]:
        """生成测试建议"""
        recommendations = []
        
        # 检查样本量
        min_sample_size = min(var.sample_size for var in variants_results)
        if min_sample_size < 10:
            recommendations.append(f"样本量较小（最小: {min_sample_size}），建议增加测试时间以获得更可靠的结果")
        
        # 检查显著性
        significant_tests = [test for test in statistical_analysis.get("tests_performed", []) if test.get("significant", False)]
        if significant_tests:
            recommendations.append("检测到统计显著差异，可以采用表现最佳的变体")
        else:
            recommendations.append("未检测到统计显著差异，建议继续测试或重新设计实验")
        
        # 检查效果大小
        best_var = max(variants_results, key=lambda x: x.mean_performance)
        worst_var = min(variants_results, key=lambda x: x.mean_performance)
        effect_size = abs(best_var.mean_performance - worst_var.mean_performance)
        
        if effect_size > 0.1:
            recommendations.append(f"效果差异较大（{effect_size:.3f}），建议采用最佳变体")
        elif effect_size < 0.05:
            recommendations.append("效果差异较小，可能需要更长时间的测试")
        
        return recommendations
    
    def _calculate_overall_confidence(self, variants_results: List[ABTestResult], statistical_analysis: Dict[str, Any]) -> float:
        """计算整体置信水平"""
        try:
            # 基于样本量和统计测试结果计算置信度
            total_samples = sum(var.sample_size for var in variants_results)
            
            # 基础置信度（基于样本量）
            base_confidence = min(total_samples / 100.0, 0.8)
            
            # 统计显著性加成
            significant_tests = [test for test in statistical_analysis.get("tests_performed", []) if test.get("significant", False)]
            if significant_tests:
                base_confidence += 0.2
            
            return min(base_confidence, 1.0)
            
        except Exception:
            return 0.5  # 默认置信度
    
    def _validate_test_config(self, config: ABTestConfig) -> bool:
        """验证测试配置"""
        if not config.test_name or not config.agent_id:
            return False
        
        if len(config.variants) < 2:
            return False
        
        if config.test_duration_hours <= 0:
            return False
        
        if config.min_sample_size <= 0:
            return False
        
        return True
    
    def _check_test_completion(self, test_id: str):
        """检查测试是否完成"""
        try:
            config = self.active_tests.get(test_id)
            if not config:
                return
            
            results_data = self.test_results.get(test_id, [])
            
            # 按变体统计样本量
            variant_counts = {}
            for result in results_data:
                variant_id = result["variant_id"]
                variant_counts[variant_id] = variant_counts.get(variant_id, 0) + 1
            
            # 检查是否所有变体都达到最小样本量
            min_samples_met = all(count >= config.min_sample_size for count in variant_counts.values())
            
            if min_samples_met:
                self.logger.info(f"测试 {test_id} 达到最小样本量要求，可以进行分析")
        
        except Exception as e:
            self.logger.error(f"检查测试完成状态失败: {e}")
    
    def _finalize_expired_test(self, test_id: str):
        """结束过期测试"""
        try:
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE ab_tests 
                    SET status = 'completed'
                    WHERE test_id = ?
                """, (test_id,))
                conn.commit()
            
            self.logger.info(f"过期测试已结束: {test_id}")
            
        except Exception as e:
            self.logger.error(f"结束过期测试失败: {e}")
    
    def get_active_tests(self) -> List[Dict[str, Any]]:
        """获取活跃测试列表"""
        active_tests_info = []
        
        for test_id, config in self.active_tests.items():
            results_count = len(self.test_results.get(test_id, []))
            
            active_tests_info.append({
                "test_id": test_id,
                "test_name": config.test_name,
                "agent_id": config.agent_id,
                "variants_count": len(config.variants),
                "results_count": results_count,
                "status": "active"
            })
        
        return active_tests_info

    def start_multi_agent_ab_test(self,
                                 test_name: str,
                                 agent_configs: Dict[str, ABTestConfig],
                                 coordination_config: Optional[Dict[str, Any]] = None) -> str:
        """
        启动多智能体A/B测试

        参数:
            test_name: 测试名称
            agent_configs: 智能体配置字典 {agent_id: ABTestConfig}
            coordination_config: 协调配置

        返回:
            多智能体测试ID
        """
        try:
            multi_test_id = f"multi_ab_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

            self.logger.info(f"🚀 启动多智能体A/B测试: {multi_test_id}")

            # 验证所有智能体配置
            for agent_id, config in agent_configs.items():
                if not self._validate_test_config(config):
                    raise ValueError(f"智能体 {agent_id} 的A/B测试配置无效")

            # 为每个智能体启动独立的A/B测试
            individual_test_ids = {}
            for agent_id, config in agent_configs.items():
                # 修改测试名称以包含多智能体信息
                config.test_name = f"{test_name}_{agent_id}"
                individual_test_id = self.start_ab_test(config)
                individual_test_ids[agent_id] = individual_test_id

            # 保存多智能体测试配置
            multi_test_config = {
                "multi_test_id": multi_test_id,
                "test_name": test_name,
                "individual_tests": individual_test_ids,
                "agent_configs": {agent_id: asdict(config) for agent_id, config in agent_configs.items()},
                "coordination_config": coordination_config or {},
                "start_time": datetime.now().isoformat(),
                "status": "active"
            }

            # 保存到数据库
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO multi_agent_ab_tests
                    (multi_test_id, test_name, individual_tests, agent_configs,
                     coordination_config, start_time, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    multi_test_id,
                    test_name,
                    json.dumps(individual_test_ids, ensure_ascii=False),
                    json.dumps(multi_test_config["agent_configs"], ensure_ascii=False),
                    json.dumps(coordination_config or {}, ensure_ascii=False),
                    multi_test_config["start_time"],
                    "active"
                ))
                conn.commit()

            self.logger.info(f"✅ 多智能体A/B测试启动成功: {multi_test_id}")
            return multi_test_id

        except Exception as e:
            self.logger.error(f"❌ 启动多智能体A/B测试失败: {e}")
            return ""

    def record_multi_agent_test_result(self,
                                     multi_test_id: str,
                                     agent_results: Dict[str, Dict[str, Any]]) -> bool:
        """
        记录多智能体测试结果

        参数:
            multi_test_id: 多智能体测试ID
            agent_results: 智能体结果 {agent_id: {variant_id, performance_score, metadata}}

        返回:
            是否记录成功
        """
        try:
            # 获取多智能体测试配置
            multi_test_config = self._get_multi_test_config(multi_test_id)
            if not multi_test_config:
                self.logger.error(f"多智能体测试不存在: {multi_test_id}")
                return False

            individual_tests = multi_test_config["individual_tests"]
            success_count = 0

            # 为每个智能体记录结果
            for agent_id, result_data in agent_results.items():
                if agent_id not in individual_tests:
                    self.logger.warning(f"智能体 {agent_id} 不在测试配置中")
                    continue

                individual_test_id = individual_tests[agent_id]

                success = self.record_test_result(
                    test_id=individual_test_id,
                    variant_id=result_data.get("variant_id", "variant_0"),
                    performance_score=result_data.get("performance_score", 0.0),
                    metadata=result_data.get("metadata", {})
                )

                if success:
                    success_count += 1
                else:
                    self.logger.error(f"记录智能体 {agent_id} 结果失败")

            # 记录协调结果（如果有）
            if "coordination_metrics" in agent_results:
                self._record_coordination_metrics(multi_test_id, agent_results["coordination_metrics"])

            self.logger.debug(f"多智能体测试结果记录: {multi_test_id} - 成功 {success_count}/{len(agent_results)}")
            return success_count > 0

        except Exception as e:
            self.logger.error(f"记录多智能体测试结果失败: {e}")
            return False

    def analyze_multi_agent_test_results(self, multi_test_id: str) -> Optional[Dict[str, Any]]:
        """
        分析多智能体测试结果

        参数:
            multi_test_id: 多智能体测试ID

        返回:
            多智能体分析结果
        """
        try:
            # 获取多智能体测试配置
            multi_test_config = self._get_multi_test_config(multi_test_id)
            if not multi_test_config:
                return None

            individual_tests = multi_test_config["individual_tests"]

            # 分析每个智能体的测试结果
            individual_analyses = {}
            for agent_id, individual_test_id in individual_tests.items():
                analysis = self.analyze_test_results(individual_test_id)
                if analysis:
                    individual_analyses[agent_id] = asdict(analysis)

            # 计算整体协调效果
            coordination_analysis = self._analyze_coordination_effects(individual_analyses)

            # 生成多智能体推荐
            multi_agent_recommendations = self._generate_multi_agent_recommendations(
                individual_analyses, coordination_analysis
            )

            # 计算整体置信度
            overall_confidence = self._calculate_multi_agent_confidence(individual_analyses)

            multi_agent_analysis = {
                "multi_test_id": multi_test_id,
                "test_name": multi_test_config["test_name"],
                "analysis_date": datetime.now().isoformat(),
                "individual_analyses": individual_analyses,
                "coordination_analysis": coordination_analysis,
                "multi_agent_recommendations": multi_agent_recommendations,
                "overall_confidence": overall_confidence,
                "summary": self._generate_multi_agent_summary(individual_analyses)
            }

            # 保存分析结果
            self._save_multi_agent_analysis(multi_test_id, multi_agent_analysis)

            return multi_agent_analysis

        except Exception as e:
            self.logger.error(f"分析多智能体测试结果失败: {e}")
            return None

    def _get_multi_test_config(self, multi_test_id: str) -> Optional[Dict[str, Any]]:
        """获取多智能体测试配置"""
        try:
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT test_name, individual_tests, agent_configs,
                           coordination_config, start_time, status
                    FROM multi_agent_ab_tests
                    WHERE multi_test_id = ?
                """, (multi_test_id,))

                row = cursor.fetchone()
                if not row:
                    return None

                test_name, individual_tests_json, agent_configs_json, coordination_config_json, start_time, status = row

                return {
                    "multi_test_id": multi_test_id,
                    "test_name": test_name,
                    "individual_tests": json.loads(individual_tests_json),
                    "agent_configs": json.loads(agent_configs_json),
                    "coordination_config": json.loads(coordination_config_json),
                    "start_time": start_time,
                    "status": status
                }

        except Exception as e:
            self.logger.error(f"获取多智能体测试配置失败: {e}")
            return None

    def _record_coordination_metrics(self, multi_test_id: str, coordination_metrics: Dict[str, Any]):
        """记录协调指标"""
        try:
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO coordination_metrics
                    (multi_test_id, timestamp, metrics)
                    VALUES (?, ?, ?)
                """, (
                    multi_test_id,
                    datetime.now().isoformat(),
                    json.dumps(coordination_metrics, ensure_ascii=False)
                ))
                conn.commit()

        except Exception as e:
            self.logger.error(f"记录协调指标失败: {e}")

    def _analyze_coordination_effects(self, individual_analyses: Dict[str, Any]) -> Dict[str, Any]:
        """分析协调效果"""
        try:
            if len(individual_analyses) < 2:
                return {"note": "需要至少两个智能体进行协调分析"}

            # 计算智能体间的性能相关性
            agent_performances = {}
            for agent_id, analysis in individual_analyses.items():
                if "variants_results" in analysis:
                    best_variant = max(analysis["variants_results"],
                                     key=lambda x: x["mean_performance"])
                    agent_performances[agent_id] = best_variant["mean_performance"]

            if len(agent_performances) < 2:
                return {"note": "协调分析数据不足"}

            # 计算性能方差（衡量智能体间的一致性）
            performances = list(agent_performances.values())
            performance_variance = np.var(performances)
            performance_mean = np.mean(performances)

            # 协调效果评估
            coordination_score = 1.0 / (1.0 + performance_variance)  # 方差越小，协调性越好

            return {
                "agent_performances": agent_performances,
                "performance_variance": float(performance_variance),
                "performance_mean": float(performance_mean),
                "coordination_score": float(coordination_score),
                "coordination_level": self._classify_coordination_level(float(coordination_score))
            }

        except Exception as e:
            self.logger.error(f"协调效果分析失败: {e}")
            return {"error": str(e)}

    def _classify_coordination_level(self, coordination_score: float) -> str:
        """分类协调水平"""
        if coordination_score >= 0.8:
            return "高度协调"
        elif coordination_score >= 0.6:
            return "中等协调"
        elif coordination_score >= 0.4:
            return "低度协调"
        else:
            return "协调性差"

    def _generate_multi_agent_recommendations(self,
                                            individual_analyses: Dict[str, Any],
                                            coordination_analysis: Dict[str, Any]) -> List[str]:
        """生成多智能体推荐"""
        recommendations = []

        # 个体推荐汇总
        successful_optimizations = 0
        total_agents = len(individual_analyses)

        for agent_id, analysis in individual_analyses.items():
            if analysis.get("recommendations"):
                if any("采用" in rec for rec in analysis["recommendations"]):
                    successful_optimizations += 1

        if successful_optimizations > 0:
            recommendations.append(f"有 {successful_optimizations}/{total_agents} 个智能体的优化显示积极效果")

        # 协调性推荐
        coordination_score = coordination_analysis.get("coordination_score", 0)
        if coordination_score >= 0.6:
            recommendations.append("智能体间协调性良好，建议同时部署优化")
        elif coordination_score < 0.4:
            recommendations.append("智能体间协调性较差，建议分阶段部署优化")

        # 整体策略推荐
        if successful_optimizations >= total_agents * 0.7:
            recommendations.append("大部分智能体优化效果显著，建议全面采用优化提示词")
        elif successful_optimizations >= total_agents * 0.3:
            recommendations.append("部分智能体优化效果显著，建议选择性采用优化提示词")
        else:
            recommendations.append("优化效果有限，建议重新评估优化策略")

        return recommendations

    def _calculate_multi_agent_confidence(self, individual_analyses: Dict[str, Any]) -> float:
        """计算多智能体置信度"""
        try:
            if not individual_analyses:
                return 0.0

            # 计算各智能体置信度的加权平均
            total_confidence = 0.0
            total_weight = 0.0

            for agent_id, analysis in individual_analyses.items():
                confidence = analysis.get("confidence_level", 0.5)
                sample_size = analysis.get("total_samples", 1)

                # 使用样本量作为权重
                weight = min(sample_size / 10.0, 1.0)
                total_confidence += confidence * weight
                total_weight += weight

            return total_confidence / total_weight if total_weight > 0 else 0.5

        except Exception:
            return 0.5

    def _generate_multi_agent_summary(self, individual_analyses: Dict[str, Any]) -> Dict[str, Any]:
        """生成多智能体摘要"""
        try:
            total_agents = len(individual_analyses)
            successful_tests = sum(1 for analysis in individual_analyses.values()
                                 if analysis.get("statistical_analysis", {}).get("significant_differences"))

            total_samples = sum(analysis.get("total_samples", 0) for analysis in individual_analyses.values())

            avg_performance_improvement = 0.0
            improvement_count = 0

            for analysis in individual_analyses.values():
                variants = analysis.get("variants_results", [])
                if len(variants) >= 2:
                    best_performance = max(var["mean_performance"] for var in variants)
                    worst_performance = min(var["mean_performance"] for var in variants)
                    improvement = (best_performance - worst_performance) / abs(worst_performance + 1e-8)
                    avg_performance_improvement += improvement
                    improvement_count += 1

            if improvement_count > 0:
                avg_performance_improvement /= improvement_count

            return {
                "total_agents_tested": total_agents,
                "successful_tests": successful_tests,
                "success_rate": successful_tests / total_agents if total_agents > 0 else 0,
                "total_samples_collected": total_samples,
                "average_performance_improvement": avg_performance_improvement,
                "test_completion_rate": 1.0  # 简化处理
            }

        except Exception as e:
            self.logger.error(f"生成多智能体摘要失败: {e}")
            return {"error": str(e)}

    def _save_multi_agent_analysis(self, multi_test_id: str, analysis: Dict[str, Any]):
        """保存多智能体分析结果"""
        try:
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE multi_agent_ab_tests
                    SET analysis_result = ?, status = 'analyzed'
                    WHERE multi_test_id = ?
                """, (
                    json.dumps(analysis, ensure_ascii=False),
                    multi_test_id
                ))
                conn.commit()

        except Exception as e:
            self.logger.error(f"保存多智能体分析结果失败: {e}")

    def get_multi_agent_test_status(self, multi_test_id: str) -> Optional[Dict[str, Any]]:
        """获取多智能体测试状态"""
        try:
            multi_test_config = self._get_multi_test_config(multi_test_id)
            if not multi_test_config:
                return None

            individual_tests = multi_test_config["individual_tests"]

            # 获取各个智能体测试状态
            agent_statuses = {}
            for agent_id, individual_test_id in individual_tests.items():
                if individual_test_id in self.active_tests:
                    results_count = len(self.test_results.get(individual_test_id, []))
                    agent_statuses[agent_id] = {
                        "test_id": individual_test_id,
                        "results_count": results_count,
                        "status": "active"
                    }
                else:
                    agent_statuses[agent_id] = {
                        "test_id": individual_test_id,
                        "status": "completed_or_not_found"
                    }

            return {
                "multi_test_id": multi_test_id,
                "test_name": multi_test_config["test_name"],
                "overall_status": multi_test_config["status"],
                "start_time": multi_test_config["start_time"],
                "agent_statuses": agent_statuses,
                "total_agents": len(individual_tests)
            }

        except Exception as e:
            self.logger.error(f"获取多智能体测试状态失败: {e}")
            return None
