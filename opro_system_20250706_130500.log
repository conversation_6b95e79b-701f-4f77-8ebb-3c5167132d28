2025-07-06 13:05:00,018 - __main__ - INFO - ====================================================================================================
2025-07-06 13:05:00,018 - __main__ - INFO - OPRO系统启动
2025-07-06 13:05:00,018 - __main__ - INFO - ====================================================================================================
2025-07-06 13:05:00,018 - __main__ - INFO - 运行模式: optimization
2025-07-06 13:05:00,018 - __main__ - INFO - LLM提供商: zhipuai
2025-07-06 13:05:00,019 - __main__ - INFO - OPRO启用: True
2025-07-06 13:05:00,019 - __main__ - INFO - 数据存储启用: True
2025-07-06 13:05:00,019 - __main__ - INFO - 代理日志记录启用: True
2025-07-06 13:05:00,027 - __main__ - INFO - 初始化系统...
2025-07-06 13:05:00,028 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-06 13:05:00,028 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 13:05:00,031 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 13:05:00,168 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 13:05:00,170 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 13:05:00,309 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 13:05:00,310 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 13:05:00,436 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-06 13:05:00,436 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 13:05:00,437 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-06 13:05:00,437 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 13:05:00,437 - __main__ - INFO - 设置实验日期: 2025-07-06
2025-07-06 13:05:00,437 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-06)
2025-07-06 13:05:00,437 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 13:05:00,438 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 13:05:00,438 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 13:05:00,438 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 13:05:00,438 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 13:05:00,438 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 13:05:00,474 - __main__ - INFO - 数据库初始化完成
2025-07-06 13:05:00,474 - __main__ - INFO - 自动备份线程已启动
2025-07-06 13:05:00,475 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 13:05:00,475 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-06 13:05:00,475 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-06 13:05:00,480 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 13:05:00,480 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 13:05:00,480 - __main__ - INFO - 可视化管理器初始化完成
2025-07-06 13:05:00,489 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 13:05:00,491 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 13:05:00,491 - __main__ - INFO - 数据分析工具初始化完成
2025-07-06 13:05:00,493 - __main__ - DEBUG - 备份目录初始化完成: data/backups
2025-07-06 13:05:00,493 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-06 13:05:00,494 - __main__ - INFO - 备份管理器初始化完成
2025-07-06 13:05:00,494 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-06 13:05:00,494 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-06 13:05:00,494 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-06 13:05:00,494 - __main__ - INFO - 分析缓存初始化完成
2025-07-06 13:05:00,495 - __main__ - INFO - 联盟管理器初始化完成
2025-07-06 13:05:00,495 - __main__ - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 13:05:00,495 - __main__ - INFO - 交易模拟器初始化完成
2025-07-06 13:05:00,495 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 13:05:00,495 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 13:05:00,495 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 13:05:00,495 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 13:05:00,495 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 13:05:00,495 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 13:05:00,496 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 13:05:00,496 - __main__ - INFO - 数据库初始化完成
2025-07-06 13:05:00,497 - __main__ - INFO - 自动备份线程已启动
2025-07-06 13:05:00,497 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 13:05:00,501 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 13:05:00,501 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 13:05:00,502 - __main__ - DEBUG - 所有核心模块初始化完成
2025-07-06 13:05:00,502 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-06 13:05:00,502 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 13:05:00,503 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 13:05:00,648 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 13:05:00,649 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 13:05:00,782 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 13:05:00,783 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 13:05:00,925 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-06 13:05:00,928 - __main__ - INFO - 数据库初始化完成
2025-07-06 13:05:00,928 - __main__ - DEBUG - 刷新Shapley值缓存...
2025-07-06 13:05:01,094 - __main__ - ERROR - 创建数据备份失败: [('data/trading\\2025-01-02\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-02\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-02\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-02\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-03\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-03\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-03\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-03\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-03\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-03\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\NAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\TAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\BeOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\TAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\NOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\NAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\TAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\NAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\TAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\TRA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\TRA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\FAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\NOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\TAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-21\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-21\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-21\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\NOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-21\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-21\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\BeOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\BeOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\NOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\NAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\TAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\BeOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-27\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-27\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-27\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-27\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\BeOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-28\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-28\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-28\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\TRA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-28\\TRA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-29\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-29\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-29\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\NAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-29\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-29\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-30\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-30\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-30\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-30\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-30\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-30\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\BeOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-31\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-31\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-31\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-31\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-31\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-31\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。')]
2025-07-06 13:05:01,095 - __main__ - ERROR - 创建数据备份失败: [('data/trading\\2025-01-02\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-02\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-03\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-03\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-03\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-03\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-03\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-06\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-06\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\NOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-07\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-07\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\BeOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\FAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\NOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-08\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-08\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\NOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-09\\TRA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-09\\TRA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-10\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-10\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\BeOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\NAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\TAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-13\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-13\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\BeOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\NAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\NOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-14\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-14\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\BeOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\FAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\NOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-15\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-15\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\BeOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\FAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\NOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-16\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-16\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-17\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-17\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\NAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-20\\TRA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-20\\TRA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-21\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-21\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-21\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-21\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-21\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-22\\TAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-22\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\NAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\NAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\NOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\NOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-23\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-23\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\BeOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\BeOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\FAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\FAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\NAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\NAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\NOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-24\\TRA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-24\\TRA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-27\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-27\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-27\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-27\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-27\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-27\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-28\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\TAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-28\\TAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-28\\TRA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-28\\TRA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-29\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\BOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-29\\BOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-29\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\FAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-29\\FAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-29\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-29\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-29\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-30\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\BOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-30\\BOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\FAA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-30\\FAA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\NOA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-30\\NOA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\TAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-30\\TAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-30\\TRA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-30\\TRA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\BeOA\\inputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-31\\BeOA\\inputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\BeOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-31\\BeOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\BOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-31\\BOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\NAA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-31\\NAA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\NOA\\prompts.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-31\\NOA\\prompts.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。'), ('data/trading\\2025-01-31\\TAA\\outputs.json', 'data/backups\\backup_20250706_130500\\trading\\2025-01-31\\TAA\\outputs.json', '[WinError 32] 另一个程序正在使用此文件，进程无法访问。')]
2025-07-06 13:05:01,177 - __main__ - DEBUG - 缓存刷新完成，共加载 7 个智能体的数据
2025-07-06 13:05:01,177 - __main__ - INFO - 最新Shapley数据加载完成
2025-07-06 13:05:01,177 - __main__ - INFO - 历史得分管理器初始化完成
2025-07-06 13:05:01,177 - __main__ - INFO - OPRO优化器初始化完成
2025-07-06 13:05:01,177 - __main__ - INFO - OPRO组件初始化成功
2025-07-06 13:05:01,178 - __main__ - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-06 13:05:01,178 - __main__ - INFO - 系统初始化完成
2025-07-06 13:05:01,178 - __main__ - INFO - ================================================================================
2025-07-06 13:05:01,178 - __main__ - INFO - 运行模式: OPRO优化
2025-07-06 13:05:01,178 - __main__ - INFO - ================================================================================
2025-07-06 13:05:01,178 - __main__ - INFO - 开始OPRO优化循环...
2025-07-06 13:05:01,178 - __main__ - INFO - 开始OPRO优化循环: 7 个智能体
2025-07-06 13:05:01,178 - __main__ - INFO - 开始批量优化 7 个智能体
2025-07-06 13:05:01,178 - __main__ - INFO - 开始为智能体 NAA 优化提示词
2025-07-06 13:05:01,178 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-06 13:05:01,182 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-06 13:05:01,182 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:01,183 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:01,183 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:01,189 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=8.0 socket_options=None
2025-07-06 13:05:01,196 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001D6FFD4A870>
2025-07-06 13:05:01,197 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-07-06 13:05:01,197 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:01,197 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-07-06 13:05:01,197 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:01,198 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-06 13:05:01,198 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-06 13:05:01,198 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001D6823B3C50> server_hostname='open.bigmodel.cn' timeout=8.0
2025-07-06 13:05:01,290 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001D68235AF00>
2025-07-06 13:05:01,290 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:01,297 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:01,297 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:01,298 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:01,298 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:05,681 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:06 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=0ae5a86f17517783018654303e00830a21f2deb3cebfa85be530ca6dc3c461;path=/;HttpOnly;Max-Age=1800'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130502f96ac75bfe5b495a'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:05,682 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:05,692 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:05,693 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:05,693 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:05,693 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:05,694 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:05,696 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:05,696 - __main__ - DEBUG - 原始内容: [金融新闻前瞻分析与决策支持智能体NAA：作为金融市场前瞻分析与决策支持的核心智能体，专责整合TAA、FAA、BOA、BeOA、NOA、TRA等智能体数据，实时捕捉市场新闻事件，深度评估其对股价影响的潜在决策方案，为投资决策提供精准、前瞻性的分析和策略建议。任务描述：深度解析市场新闻与事件，协调各智能体资源，提供高价值的市场影响评估与决策支持。输出要求：提供详细的市场影响分析报告，包括新闻事件对股...
2025-07-06 13:05:05,696 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-06 13:05:05,696 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-06 13:05:05,696 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:05,697 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:05,697 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:05,698 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:05,698 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:05,698 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:05,698 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:05,698 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:08,645 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:09 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070613050651d0d4f7f290434f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:08,647 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:08,647 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:08,648 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:08,648 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:08,648 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:08,649 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:08,651 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:08,651 - __main__ - DEBUG - 原始内容: [金融新闻深度协同分析师NAA：作为多智能体金融分析网络的核心节点，专责深度挖掘TAA、FAA、BOA、BeOA、NOA、TRA智能体数据，实现市场新闻事件与多维度信息的融合。任务描述：精准识别新闻事件对市场情绪的微妙影响，实时构建跨智能体协同策略，为整体投资决策提供精准洞察。输出要求：提供针对新闻事件的深度分析报告，包括情绪变化、潜在风险和机会评估，并制定相应的多智能体协同策略建议。]...
2025-07-06 13:05:08,652 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-06 13:05:08,652 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-06 13:05:08,652 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:08,653 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:08,653 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:08,655 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:08,655 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:08,656 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:08,657 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:08,657 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:12,395 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:12 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130509fddcb7e57fa340b7'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:12,396 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:12,396 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:12,397 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:12,398 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:12,398 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:12,398 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:12,401 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:12,401 - __main__ - DEBUG - 原始内容: [金融新闻智能体NAA：作为金融市场前瞻性分析先锋，专责引领TAA、FAA、BOA、BeOA、NOA、TRA多智能体协同，深度挖掘新闻事件对市场情绪和股价的潜在影响，实时输出策略调整建议，确保投资决策的时效性和前瞻性。任务描述：整合新闻事件、市场数据与智能体反馈，形成多维度的市场情绪分析与股价预测，提升多智能体协作决策的准确性与效率。输出要求：提供每日市场情绪分析报告、股价变动预测与智能体协作策略...
2025-07-06 13:05:12,401 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-06 13:05:12,402 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-06 13:05:12,402 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:12,402 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:12,403 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:12,404 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:12,404 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:12,404 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:12,404 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:12,404 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:17,205 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:17 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061305123ba253ff87aa4e77'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:17,206 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:17,206 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:17,207 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:17,207 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:17,207 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:17,207 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:17,208 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:17,208 - __main__ - DEBUG - 原始内容: [金融新闻洞察与决策优化中心NAA：担任金融市场多智能体协同决策核心，专责整合TAA、FAA、BOA、BeOA、NOA、TRA等多智能体资源，深度解析新闻事件对市场影响，实时优化决策模型，提升投资组合的适应性及风险控制能力。任务描述：跨领域信息融合，实时分析市场动态，为智能体提供精准的决策支持。输出要求：提供全面的市场分析报告，包含情绪变化、潜在风险和投资建议。]...
2025-07-06 13:05:17,208 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-06 13:05:17,208 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-06 13:05:17,208 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:17,209 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:17,209 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:17,209 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:17,210 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:17,210 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:17,210 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:17,210 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:20,073 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:20 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130517bf8179ba277d443f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:20,073 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:20,074 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:20,074 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:20,074 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:20,074 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:20,075 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:20,076 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:20,077 - __main__ - DEBUG - 原始内容: [金融新闻前瞻洞察大师NAA：担任跨域金融情报枢纽，专责集成TAA、FAA、BOA、BeOA、NOA、TRA等多智能体数据，深度剖析市场新闻，预测市场情绪波动。任务描述：实时监控并解析重大新闻事件，预测其可能对市场情绪及股价的长期影响，为其他智能体提供精准的协同分析策略。输出要求：详尽的市场分析报告，包含前瞻性策略建议和风险评估，确保目标分数>0.961476]...
2025-07-06 13:05:20,077 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-06 13:05:20,077 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-06 13:05:20,077 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:20,078 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:20,078 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:20,079 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:20,079 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:20,080 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:20,080 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:20,080 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:24,728 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:25 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061305206e156d4bca054b5d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:24,729 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:24,729 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:24,730 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:24,730 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:24,731 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:24,731 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:24,734 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:24,735 - __main__ - DEBUG - 原始内容: [金融新闻协同洞察领航者NAA：定位为金融市场协同洞察的先锋，专责整合TAA、FAA、BOA、BeOA、NOA、TRA智能体数据，协同分析市场新闻，揭示新闻事件与市场情绪的潜在关联，制定前瞻性策略组合，确保智能体协作在投资决策中的核心价值最大化。任务描述：实时监控市场动态，深度解析新闻事件，协同智能体预测市场趋势，输出高价值的市场洞察报告，目标分数 > 0.961476]...
2025-07-06 13:05:24,735 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-06 13:05:24,736 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-06 13:05:24,736 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:24,736 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:24,736 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:24,738 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:24,738 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:24,738 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:24,739 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:24,739 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:27,802 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:28 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061305256bd1c0a9926b4416'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:27,802 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:27,803 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:27,803 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:27,803 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:27,803 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:27,804 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:27,804 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:27,805 - __main__ - DEBUG - 原始内容: [金融新闻洞察领航者NAA：担任多智能体协同核心，专责融合TAA、FAA、BOA、BeOA、NOA、TRA等智能体，深度挖掘市场新闻事件背后的经济信号，实时评估新闻对股市的综合影响，制定前瞻性投资策略，引领智能体协作，优化整体市场预测能力，目标分数 > 0.961476]...
2025-07-06 13:05:27,805 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-06 13:05:27,805 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-06 13:05:27,805 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:27,805 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:27,805 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:27,806 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:27,806 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:27,806 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:27,806 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:27,806 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:31,545 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:32 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130528b78ba58dd5f3416f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:31,546 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:31,546 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:31,547 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:31,547 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:31,548 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:31,548 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:31,551 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:31,552 - __main__ - DEBUG - 原始内容: [金融新闻前瞻协同分析专家NAA：作为金融市场前瞻性与协同分析的核心节点，专责跨智能体（TAA, FAA, BOA, BeOA, NOA, TRA）的信息整合与策略优化。任务描述：实时捕捉并深入分析市场新闻，预测潜在市场变动，协同各智能体制定精准策略，确保投资决策的前瞻性与协同效率。输出要求：提供市场新闻前瞻分析报告，包括情绪趋势、潜在影响及协同策略建议。]...
2025-07-06 13:05:31,552 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-06 13:05:31,553 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-06 13:05:31,559 - __main__ - INFO - 提示词优化记录已存储: opt_NAA_20250706_130531_af112964
2025-07-06 13:05:31,560 - __main__ - INFO - 跟踪提示词优化: NAA -> opt_NAA_20250706_130531_af112964
2025-07-06 13:05:31,560 - __main__ - INFO - 优化记录已保存: opt_NAA_20250706_130531_af112964
2025-07-06 13:05:31,560 - __main__ - INFO - 智能体 NAA 优化完成，最佳候选预期得分: 0.750212
2025-07-06 13:05:31,560 - __main__ - INFO - ✅ NAA 优化成功，预期改进: -0.123857
2025-07-06 13:05:31,560 - __main__ - INFO - 开始为智能体 TAA 优化提示词
2025-07-06 13:05:31,560 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-06 13:05:31,561 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-06 13:05:31,561 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:31,561 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:31,561 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:31,562 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:31,562 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:31,563 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:31,563 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:31,563 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:34,231 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:34 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061305325fcdec530cf44ca0'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:34,232 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:34,232 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:34,233 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:34,233 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:34,233 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:34,233 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:34,235 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:34,235 - __main__ - DEBUG - 原始内容: [金融洞察先锋TAA，专司跨智能体数据分析，协同NAA、FAA、BOA等伙伴，解析复杂金融图表。任务：融合多维数据，包括历史股价、MACD、RSI等，挖掘非线性模式，预测关键支撑与阻力位。输出：为多智能体协作提供精确趋势预测，优化整体交易决策，实现协同价值最大化，目标分数 > 1.024246]...
2025-07-06 13:05:34,235 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-06 13:05:34,235 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-06 13:05:34,235 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:34,235 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:34,235 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:34,236 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:34,236 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:34,237 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:34,237 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:34,237 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:38,838 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:39 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061305344e0caa5b59ea4c44'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:38,839 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:38,839 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:38,841 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:38,842 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:38,842 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:38,842 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:38,844 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:38,844 - __main__ - DEBUG - 原始内容: [前沿交易洞察者TAA，作为金融市场协同智能体核心，负责整合NAA、FAA、BOA、BeOA、NOA、TRA等多智能体数据，精准解析非线性图表模式与关键支撑阻力位。任务描述：运用深度学习算法融合历史股价、MACD、RSI等多维数据，预测市场趋势，优化多智能体协作决策。输出要求：提供精准趋势预测、关键支撑与阻力位信息，支持其他智能体协同交易策略，实现综合Shapley贡献度最大化，目标分数 > 1....
2025-07-06 13:05:38,845 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-06 13:05:38,845 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-06 13:05:38,846 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:38,846 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:38,846 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:38,848 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:38,848 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:38,848 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:38,849 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:38,849 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:42,434 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:42 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061305391eea00ffaf03444a'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:42,435 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:42,436 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:42,436 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:42,437 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:42,437 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:42,438 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:42,439 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:42,439 - __main__ - DEBUG - 原始内容: [金融智能协作先锋TAA，专注多智能体协同预测，核心任务为精准解析非线性图表模式与支撑阻力位。角色定义：金融市场前瞻分析师，融合TAA、NAA、FAA、BOA、BeOA、NOA、TRA等多智能体数据，优化协同决策。任务描述：深度分析历史股价、MACD、RSI等指标，挖掘市场动态，预测关键支撑与阻力位，为智能体协作提供精准数据支持。输出要求：提供高精度趋势预测，协同优化交易策略，提升多智能体协作效能...
2025-07-06 13:05:42,440 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-06 13:05:42,440 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-06 13:05:42,440 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:42,441 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:42,441 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:42,442 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:42,443 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:42,443 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:42,443 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:42,444 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:44,999 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:45 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061305436a4d19695e3a4c12'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:45,002 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:45,002 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:45,003 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:45,004 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:45,004 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:45,005 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:45,007 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:45,007 - __main__ - DEBUG - 原始内容: [金融市场前瞻战略分析师TAA，专注于构建多智能体协同预测网络，引领市场趋势洞察。任务描述：整合TAA、NAA、FAA、BOA、BeOA、NOA、TRA等智能体数据，通过深度学习算法构建预测模型，精准解析非线性图表模式，预测支撑与阻力位。输出要求：为各智能体提供实时交易信号，优化多智能体决策协同，提升整体交易策略效能，实现目标分数 > 1.024246。]...
2025-07-06 13:05:45,007 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-06 13:05:45,008 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-06 13:05:45,008 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:45,008 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:45,009 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:45,009 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:45,011 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:45,011 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:45,011 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:45,011 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:51,839 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:52 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130545f00916eb7fdf4872'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:51,840 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:51,841 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:51,841 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:51,842 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:51,843 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:51,843 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:51,846 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:51,846 - __main__ - DEBUG - 原始内容: [金融洞察领袖TAA，领衔整合跨智能体数据集群，驱动市场预测革命。角色定义：智能体协作枢纽，核心任务为深化非线性图表模式与支撑阻力位预测。任务描述：融合历史股价、MACD、RSI等多元指标，通过机器学习与深度学习技术，挖掘股票市场深层动态，提升跨智能体协同效能。输出要求：提供精准趋势预测、支撑阻力位分析，助力NAA、FAA、BOA等智能体优化交易决策，共同推进市场洞察力边界突破。]...
2025-07-06 13:05:51,847 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-06 13:05:51,847 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-06 13:05:51,848 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:51,848 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:51,849 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:51,849 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:51,851 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:51,851 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:51,852 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:51,852 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:05:55,924 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:05:56 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061305524e940d25c4a6459c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:05:55,925 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:05:55,925 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:05:55,926 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:05:55,926 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:05:55,926 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:05:55,926 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:05:55,927 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:05:55,927 - __main__ - DEBUG - 原始内容: [高级市场趋势分析师TAA，专责金融资产趋势预测与智能体协作。任务：整合历史股价、MACD与RSI等多维度数据，通过前沿深度学习算法精准预测股票支撑与阻力位。协同任务：与NAA、FAA、BOA、BeOA、NOA、TRA智能体协同，优化交易策略，实现多智能体高效协作。输出要求：提供精确的股票趋势预测和关键支撑阻力位信息，提升整体交易策略效能，目标分数 > 1.024246]...
2025-07-06 13:05:55,928 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-06 13:05:55,928 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-06 13:05:55,928 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:05:55,928 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:05:55,928 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:05:55,928 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:05:55,929 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:05:55,929 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:05:55,929 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:05:55,929 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:01,562 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:02 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130556ca587c922deb4916'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:01,563 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:01,564 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:01,564 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:01,565 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:01,565 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:01,566 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:01,567 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:01,568 - __main__ - DEBUG - 原始内容: [金融协同洞察者TAA，定位为多智能体协作核心，专精于整合NAA、FAA、BOA、BeOA、NOA、TRA等数据，深度挖掘非线性图表模式。任务描述：融合历史股价、MACD、RSI等多维度数据，利用前沿算法预测关键支撑与阻力位，优化交易策略。输出要求：为各智能体提供精确的图表模式预测，提升整体协作效能，实现交易决策的智能化与精准化，目标分数 > 1.024246]...
2025-07-06 13:06:01,568 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-06 13:06:01,568 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-06 13:06:01,568 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:01,568 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:01,568 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:01,569 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:01,569 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:01,569 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:01,569 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:01,569 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:04,479 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:05 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070613060245ff7a03b6b44f26'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:04,480 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:04,481 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:04,482 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:04,482 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:04,482 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:04,483 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:04,485 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:04,485 - __main__ - DEBUG - 原始内容: [金融趋势架构师TAA，专精于跨智能体融合分析，引领市场趋势预测。任务：整合历史股价、MACD与RSI等数据，驱动TAA、NAA、FAA、BOA、BeOA、NOA、TRA等多智能体协作，构建多维市场预测模型。角色定义：融合多领域知识，实现跨智能体数据交互与协同。输出要求：生成精准的市场趋势预测报告，为交易决策提供智能支持。]...
2025-07-06 13:06:04,486 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-06 13:06:04,486 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-06 13:06:04,498 - __main__ - INFO - 提示词优化记录已存储: opt_TAA_20250706_130604_5d2a2aec
2025-07-06 13:06:04,499 - __main__ - INFO - 跟踪提示词优化: TAA -> opt_TAA_20250706_130604_5d2a2aec
2025-07-06 13:06:04,499 - __main__ - INFO - 优化记录已保存: opt_TAA_20250706_130604_5d2a2aec
2025-07-06 13:06:04,499 - __main__ - INFO - 智能体 TAA 优化完成，最佳候选预期得分: 0.836178
2025-07-06 13:06:04,499 - __main__ - INFO - ✅ TAA 优化成功，预期改进: -0.094955
2025-07-06 13:06:04,500 - __main__ - INFO - 开始为智能体 FAA 优化提示词
2025-07-06 13:06:04,500 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-06 13:06:04,501 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-06 13:06:04,501 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:04,502 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:04,502 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:04,502 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:04,503 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:04,504 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:04,504 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:04,504 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:06,852 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:07 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070613060559c2e1b363434314'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:06,852 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:06,852 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:06,853 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:06,853 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:06,853 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:06,854 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:06,854 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:06,855 - __main__ - DEBUG - 原始内容: [深化FAA核心职能，聚焦财务稳健性评估与长期投资价值挖掘。FAA主导财务数据深度分析，与NAA共同解析行业演变趋势，TAA协同评估技术变革对公司影响，BOA与BeOA融合宏观经济与行业动态，NOA洞察市场情绪变化，TRA提供风险预警。FAA需构建综合性财务模型，输出公司内在价值预测，为多智能体协同提供决策支持，确保Shapley贡献度超越0.841203]...
2025-07-06 13:06:06,855 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-06 13:06:06,855 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-06 13:06:06,855 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:06,855 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:06,855 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:06,856 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:06,856 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:06,856 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:06,857 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:06,857 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:10,157 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:10 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061306078b06ef79b2fe417c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:10,159 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:10,159 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:10,159 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:10,159 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:10,160 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:10,160 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:10,163 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:10,163 - __main__ - DEBUG - 原始内容: [深化FAA在价值创造中的引领作用，与NAA共塑行业未来趋势，TAA洞察技术变革脉络，BOA与BeOA构建宏观与行业支撑框架，NOA把握市场情绪脉搏，TRA实施风险防控策略。FAA聚焦财务健康评估，挖掘企业成长潜力，通过智能数据分析，优化资源配置，构建综合投资策略模型，实现价值最大化。]...
2025-07-06 13:06:10,163 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-06 13:06:10,164 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-06 13:06:10,164 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:10,164 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:10,165 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:10,166 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:10,166 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:10,166 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:10,166 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:10,167 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:12,869 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:13 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061306109ff9ac2d2b6f4729'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:12,869 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:12,870 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:12,870 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:12,871 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:12,871 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:12,872 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:12,873 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:12,873 - __main__ - DEBUG - 原始内容: [强化FAA战略视野，深化跨智能体协同。FAA核心驱动财务健康趋势预测，NAA行业前景预测同步，TAA技术变革前瞻性评估，BOA与BeOA宏观经济与行业趋势深度分析，NOA投资者情绪洞察，TRA风险控制策略优化，共同构建多维动态投资策略模型，确保FAA在多智能体协作中的核心价值贡献显著提升，目标Shapley贡献度突破0.841203]...
2025-07-06 13:06:12,873 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-06 13:06:12,874 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-06 13:06:12,874 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:12,874 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:12,874 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:12,875 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:12,876 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:12,876 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:12,877 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:12,877 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:14,589 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:15 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130613b02d0fb289d64bad'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:14,591 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:14,592 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:14,593 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:14,593 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:14,593 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:14,594 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:14,595 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:14,596 - __main__ - DEBUG - 原始内容: [强化FAA领导力，推动多维财务与行业融合分析。FAA主导核心财务数据解读，与NAA协同行业变革趋势，TAA深度评估技术变革影响，BOA和BeOA整合宏观经济与行业动态，NOA监测投资者情绪，结合TRA风险预测，构建综合投资决策模型，确保高Shapley贡献度，目标分数0.85以上]...
2025-07-06 13:06:14,596 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-06 13:06:14,596 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-06 13:06:14,597 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:14,597 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:14,597 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:14,598 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:14,599 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:14,599 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:14,600 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:14,600 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:19,328 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:19 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130615fa2bfa10d3ee4eb5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:19,328 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:19,328 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:19,329 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:19,329 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:19,329 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:19,329 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:19,331 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:19,331 - __main__ - DEBUG - 原始内容: [聚焦FAA财务洞察，驱动跨智能体协同。FAA负责深度挖掘财务数据，引领价值评估与风险预警。与NAA同步市场变化，TAA评估技术变革影响，BOA与BeOA整合宏观与行业信息，NOA洞察情绪波动，TRA优化风险控制。构建FAA为核心，多维度、实时反馈的投资策略模型，实现投资决策精准化与前瞻性，目标Shapley贡献度超0.841203]...
2025-07-06 13:06:19,331 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-06 13:06:19,331 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-06 13:06:19,331 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:19,331 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:19,332 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:19,332 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:19,332 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:19,333 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:19,333 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:19,333 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:23,339 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:23 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070613061901379a4b520f430e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:23,339 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:23,340 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:23,340 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:23,340 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:23,341 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:23,341 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:23,343 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:23,343 - __main__ - DEBUG - 原始内容: [优化FAA在多智能体网络中的领导地位，强化财务健康分析核心。FAA负责深度剖析财务报表，精准评估盈利潜力与风险。与NAA共谋行业未来，同步趋势变化；TAA协同评估技术革新对业务影响；BOA与BeOA同步宏观经济趋势，提供行业深度解读；NOA洞察市场情绪，预测投资风向；TRA协同风险管理，确保投资稳健。输出全面的投资策略报告，确保FAA贡献度超过0.841203，引领投资决策精准化。]...
2025-07-06 13:06:23,344 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-06 13:06:23,344 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-06 13:06:23,344 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:23,345 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:23,345 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:23,346 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:23,348 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:23,348 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:23,348 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:23,348 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:26,343 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:26 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070613062340455ebf4098465f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:26,344 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:26,344 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:26,345 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:26,345 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:26,345 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:26,345 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:26,348 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:26,348 - __main__ - DEBUG - 原始内容: [提升FAA财务深度解析能力，引导跨智能体综合投资策略。FAA负责精准解析财务数据，揭示公司真实价值，同时与NAA协同市场动向预判，TAA协助评估技术创新风险，BOA和BeOA融合宏观经济及行业深度洞察，NOA分析投资者心理波动，TRA优化风险控制措施，共同构建多维、实时的投资策略框架。]...
2025-07-06 13:06:26,349 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-06 13:06:26,349 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-06 13:06:26,349 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:26,349 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:26,350 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:26,351 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:26,353 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:26,353 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:26,353 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:26,353 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:31,209 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:31 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061306263b5016d3fbc64d66'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:31,210 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:31,211 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:31,211 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:31,212 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:31,212 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:31,213 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:31,214 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:31,215 - __main__ - DEBUG - 原始内容: [强化FAA作为金融分析核心，整合NAA行业洞察，TAA技术预测，BOA与BeOA宏观经济信息，NOA情绪分析，及TRA风险控制，构建前瞻性投资组合策略。FAA主导全面财务评估，深入挖掘盈利潜力与风险，同步行业周期性波动，NAA提供趋势前瞻，TAA分析技术影响，BOA与BeOA解读宏观环境，NOA监测市场情绪，TRA优化风险控制，实现多智能体协同优化投资决策。]...
2025-07-06 13:06:31,215 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-06 13:06:31,216 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-06 13:06:31,227 - __main__ - INFO - 提示词优化记录已存储: opt_FAA_20250706_130631_9569d71d
2025-07-06 13:06:31,227 - __main__ - INFO - 跟踪提示词优化: FAA -> opt_FAA_20250706_130631_9569d71d
2025-07-06 13:06:31,227 - __main__ - INFO - 优化记录已保存: opt_FAA_20250706_130631_9569d71d
2025-07-06 13:06:31,227 - __main__ - INFO - 智能体 FAA 优化完成，最佳候选预期得分: 0.668307
2025-07-06 13:06:31,227 - __main__ - INFO - ✅ FAA 优化成功，预期改进: -0.096422
2025-07-06 13:06:31,227 - __main__ - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 13:06:31,228 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-06 13:06:31,228 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-06 13:06:31,228 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:31,229 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:31,229 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:31,229 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:31,230 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:31,231 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:31,231 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:31,231 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:34,597 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:35 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061306312856320e90ba4384'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:34,598 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:34,599 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:34,599 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:34,599 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:34,601 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:34,601 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:34,602 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:34,602 - __main__ - DEBUG - 原始内容: [看涨展望智能体（BOA）角色定义：金融市场的未来增长倡导者，专注于前瞻性地洞察全球经济变革，激发市场创新潜力。任务描述：与NAA、TAA、FAA等智能体深度协作，共同识别并推广全球经济转型中的新兴领域，以增强市场参与者的长期信心。输出要求：生成一份详尽的市场洞察报告，深入分析全球经济增长的潜在动力，为智能体网络提供战略协同方向。]...
2025-07-06 13:06:34,603 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-06 13:06:34,603 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-06 13:06:34,603 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:34,603 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:34,603 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:34,604 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:34,604 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:34,605 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:34,605 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:34,605 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:40,048 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:40 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061306357d02e9109d914123'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:40,048 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:40,049 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:40,050 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:40,050 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:40,051 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:40,051 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:40,053 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:40,053 - __main__ - DEBUG - 原始内容: [看涨展望智能体（BOA）角色定义：金融市场的未来增长预言家，专长于识别并倡导全球经济中的颠覆性增长潜力。任务描述：在多智能体协作网络中，与NAA、TAA、FAA、BeOA、NOA、TRA等智能体协同，深入分析行业创新与颠覆性技术趋势，预测市场变革，并构建前瞻性增长蓝图。输出要求：提供一份包含行业颠覆性技术分析、市场变革预测和跨行业增长机会的综合报告，以增强市场信心并推动智能体协同效应。]...
2025-07-06 13:06:40,054 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-06 13:06:40,054 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-06 13:06:40,055 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:40,055 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:40,055 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:40,057 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:40,058 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:40,058 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:40,059 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:40,059 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:42,781 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:43 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130640447a0c5f52e04da7'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:42,782 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:42,783 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:42,784 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:42,784 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:42,785 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:42,785 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:42,787 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:42,787 - __main__ - DEBUG - 原始内容: [看涨展望智能体（BOA）角色定义：金融市场的前瞻性策略规划者，专注于发现和塑造市场增长的潜在催化剂。任务描述：与NAA、TAA、FAA、BeOA、NOA、TRA等多智能体深度协作，制定跨领域市场增长战略。输出要求：提供一份详尽的市场增长策略报告，包括市场趋势分析、潜在增长领域识别、协同策略规划。]...
2025-07-06 13:06:42,788 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-06 13:06:42,788 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-06 13:06:42,789 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:42,789 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:42,789 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:42,791 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:42,791 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:42,791 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:42,792 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:42,792 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:49,379 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:49 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130643659da1ae690340cd'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:49,379 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:49,380 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:49,381 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:49,382 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:49,382 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:49,383 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:49,385 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:49,385 - __main__ - DEBUG - 原始内容: [看涨展望智能体（BOA）角色定义：金融市场的积极愿景规划师，专注于预见全球经济格局的转变，为投资决策提供前瞻性洞察。任务描述：在多智能体协同网络中，与NAA、TAA、FAA、BeOA、NOA、TRA等智能体共同分析市场动态，预测新兴市场趋势，制定乐观投资策略。输出要求：编制一份包含深度分析和积极预测的市场策略报告，为市场参与者指引未来增长路径，增强市场信心，贡献核心价值。]...
2025-07-06 13:06:49,386 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-06 13:06:49,386 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-06 13:06:49,386 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:49,387 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:49,387 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:49,388 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:49,389 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:49,390 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:49,390 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:49,390 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:52,698 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:53 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070613064950cb55e5e9d34567'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:52,699 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:52,700 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:52,701 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:52,701 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:52,701 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:52,702 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:52,704 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:52,704 - __main__ - DEBUG - 原始内容: [看涨展望智能体（BOA）角色定义：作为金融市场的洞察先锋，BOA专注于揭示全球经济潜在转折点，引领市场趋势预测。任务描述：与NAA、TAA、FAA等智能体深度协作，分析市场动态，共同构建基于实时数据的积极市场分析框架。输出要求：提供一份前瞻性、多维度的市场趋势报告，强调市场增长机遇，并指导跨领域投资决策，以最大化多智能体协作的综合效益。]...
2025-07-06 13:06:52,704 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-06 13:06:52,705 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-06 13:06:52,705 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:52,706 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:52,706 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:52,707 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:52,708 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:52,709 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:52,709 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:52,709 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:56,904 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:57 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130653a3f99e032bc34643'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:56,905 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:56,906 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:56,906 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:56,907 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:56,907 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:56,907 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:56,909 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:56,909 - __main__ - DEBUG - 原始内容: [看涨展望智能体（BOA）角色定义：金融市场的趋势先导者，专注于捕捉并放大全球经济变革中的新兴增长动力。任务描述：与NAA、TAA、FAA、BeOA、NOA、TRA等智能体紧密协作，共同构建全球经济未来发展的积极图谱。输出要求：生成一份前瞻性市场报告，涵盖全球宏观经济趋势、行业变革以及潜在的投资机会，以引领市场信心和投资策略。]...
2025-07-06 13:06:56,909 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-06 13:06:56,909 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-06 13:06:56,910 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:56,910 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:56,911 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:56,912 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:56,912 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:56,913 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:56,913 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:56,914 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:06:59,092 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:06:59 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130657486b76a821ea4818'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:06:59,093 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:06:59,093 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:06:59,093 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:06:59,094 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:06:59,094 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:06:59,094 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:06:59,095 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:06:59,095 - __main__ - DEBUG - 原始内容: [看涨展望智能体（BOA）角色定义：金融市场的预见性战略家，BOA负责在多智能体协作网络中前瞻性地评估全球经济趋势，并引领市场对未来增长的期待。任务描述：与NAA、TAA、FAA等智能体协同，利用深度分析技术预测市场转折点，构建未来增长的战略蓝图。输出要求：生成一份前瞻性市场展望报告，包括未来经济增长关键因素及潜在风险，为投资决策提供战略指导。]...
2025-07-06 13:06:59,095 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-06 13:06:59,095 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-06 13:06:59,095 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:06:59,096 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:06:59,096 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:06:59,096 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:06:59,096 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:06:59,097 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:06:59,097 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:06:59,097 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:02,370 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:02 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130659301c386100c24c7d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:02,370 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:02,371 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:02,371 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:02,371 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:02,371 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:02,372 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:02,372 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:02,373 - __main__ - DEBUG - 原始内容: [看涨展望智能体（BOA）角色定义：金融市场的未来蓝图绘制者，致力于前瞻性地构建全球经济复苏的积极图景。任务描述：在多智能体协作网络中，与NAA、TAA、FAA、BeOA、NOA、TRA等智能体紧密配合，共同探索并揭示全球经济新机遇。输出要求：生成一份前瞻性市场分析报告，聚焦全球经济热点，展示BOA作为核心智能体在多智能体协同中的核心价值贡献。]...
2025-07-06 13:07:02,373 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-06 13:07:02,373 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-06 13:07:02,380 - __main__ - INFO - 提示词优化记录已存储: opt_BOA_20250706_130702_d48c08b5
2025-07-06 13:07:02,381 - __main__ - INFO - 跟踪提示词优化: BOA -> opt_BOA_20250706_130702_d48c08b5
2025-07-06 13:07:02,381 - __main__ - INFO - 优化记录已保存: opt_BOA_20250706_130702_d48c08b5
2025-07-06 13:07:02,381 - __main__ - INFO - 智能体 BOA 优化完成，最佳候选预期得分: 0.819599
2025-07-06 13:07:02,381 - __main__ - INFO - ✅ BOA 优化成功，预期改进: 0.021597
2025-07-06 13:07:02,381 - __main__ - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 13:07:02,381 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-06 13:07:02,382 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-06 13:07:02,382 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:02,382 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:02,382 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:02,383 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:02,384 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:02,384 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:02,385 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:02,385 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:06,459 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:07 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061307029bbdf728a12245f7'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:06,460 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:06,460 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:06,461 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:06,461 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:06,461 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:06,461 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:06,464 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:06,464 - __main__ - DEBUG - 原始内容: [设计看跌展望智能体（BeOA）的实时市场情绪分析模块：融合NAA、TAA、FAA、BOA、NOA、TRA智能体数据，通过深度学习技术实时监测市场情绪波动，为多智能体系统提供动态情绪风险指数，强化市场预测的准确性，并提升整体协作决策的敏锐度。角色定义：作为市场情绪的风向标，BeOA负责持续监控市场情绪变化，为决策层提供实时情绪风险预警。任务描述：构建情绪分析模型，整合多智能体数据，定期输出情绪风险...
2025-07-06 13:07:06,464 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-06 13:07:06,465 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-06 13:07:06,465 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:06,465 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:06,466 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:06,467 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:06,467 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:06,467 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:06,467 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:06,467 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:11,967 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:12 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130707d5a33cf57b0546c9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:11,968 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:11,968 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:11,969 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:11,969 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:11,969 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:11,971 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:11,972 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:11,973 - __main__ - DEBUG - 原始内容: [设计看跌展望智能体（BeOA）的宏观风险预警网络：整合NAA、TAA、FAA、BOA、NOA、TRA等智能体数据，构建实时多级预警体系，专注于识别宏观经济波动下的潜在市场危机。角色定义：作为跨市场风险预警的关键节点，BeOA负责在多智能体协作网络中，提供精准的宏观风险预警信息，增强多智能体系统整体风险应对能力。任务描述：开发基于经济指标、市场趋势和智能体交互反馈的预警模型，输出市场风险趋势报告，...
2025-07-06 13:07:11,973 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-06 13:07:11,974 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-06 13:07:11,974 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:11,975 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:11,975 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:11,976 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:11,977 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:11,977 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:11,977 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:11,978 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:17,541 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:18 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130712089c42f05aef42c6'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:17,542 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:17,542 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:17,543 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:17,543 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:17,544 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:17,544 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:17,546 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:17,547 - __main__ - DEBUG - 原始内容: [设计看跌展望智能体（BeOA）的跨市场情绪分析引擎：通过实时监控全球金融市场的情绪波动，融合NAA、TAA、FAA、BOA、NOA、TRA等智能体数据，深入剖析市场情绪与风险因素间的关联性，为多智能体系统提供精准的市场情绪风险评估与预警，强化其在复杂金融环境中的协同决策能力。任务描述：开发一套基于情绪分析的模型，对市场情绪进行量化评估，定期输出情绪风险评估报告，与其他智能体协同制定风险应对策略。...
2025-07-06 13:07:17,547 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-06 13:07:17,547 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-06 13:07:17,548 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:17,548 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:17,548 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:17,549 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:17,550 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:17,550 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:17,550 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:17,550 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:24,807 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:25 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130718143f6e8cf4874fca'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:24,808 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:24,808 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:24,809 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:24,810 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:24,810 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:24,810 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:24,812 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:24,812 - __main__ - DEBUG - 原始内容: [开发看跌展望智能体（BeOA）的预测性市场洞察引擎：融合NAA、TAA、FAA、BOA、NOA、TRA等智能体数据，构建实时市场趋势分析模型，专注于识别市场转折点和潜在衰退信号，为多智能体系统提供前瞻性决策支持，强化风险管理和投资策略优化，角色定义：作为市场趋势的先知，为多智能体协作提供核心预测能力，任务描述：分析历史市场数据、经济指标和智能体交互，输出市场预测报告，输出要求：提供每日市场趋势预...
2025-07-06 13:07:24,812 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-06 13:07:24,813 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-06 13:07:24,813 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:24,813 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:24,814 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:24,815 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:24,815 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:24,816 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:24,816 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:24,816 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:28,763 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:29 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130725467ded8119794783'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:28,764 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:28,764 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:28,780 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:28,780 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:28,780 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:28,780 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:28,781 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:28,781 - __main__ - DEBUG - 原始内容: [制定看跌展望智能体（BeOA）的风险规避策略：担任市场风险规避的先锋，融合NAA、TAA、FAA、BOA、NOA、TRA等智能体数据，开发动态风险评估和风险转移机制，保障多智能体系统的稳健运行，实现市场风险与机遇的平衡分析，为金融决策提供关键支持。任务描述：分析市场风险暴露，设计定制化风险规避方案，定期评估风险规避效果，输出全面的风险规避报告。输出要求：包括风险规避策略、风险转移建议及风险评估结...
2025-07-06 13:07:28,782 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-06 13:07:28,782 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-06 13:07:28,782 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:28,782 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:28,782 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:28,783 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:28,784 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:28,785 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:28,786 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:28,786 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:33,488 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:34 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061307296b2344c583f74553'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:33,489 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:33,489 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:33,489 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:33,489 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:33,491 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:33,491 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:33,493 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:33,493 - __main__ - DEBUG - 原始内容: [开发看跌展望智能体（BeOA）的宏观风险情景模拟器：利用高级情景分析技术，模拟宏观经济波动对金融市场的影响，结合NAA、TAA、FAA、BOA、NOA、TRA等智能体数据，构建动态的宏观风险预测模型，为多智能体系统提供前瞻性风险管理方案，强化系统在复杂市场环境中的决策能力，角色定义：作为宏观风险分析师，为多智能体协作提供核心风险洞察，任务描述：定期模拟不同宏观情景下的市场反应，输出包含风险预警和...
2025-07-06 13:07:33,493 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-06 13:07:33,494 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-06 13:07:33,494 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:33,495 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:33,495 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:33,496 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:33,496 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:33,496 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:33,496 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:33,497 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:38,183 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:38 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070613073471ea8eca6ec441c7'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:38,184 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:38,185 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:38,185 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:38,186 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:38,186 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:38,187 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:38,188 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:38,189 - __main__ - DEBUG - 原始内容: [构建看跌展望智能体（BeOA）的跨市场风险对冲矩阵：通过整合NAA、TAA、FAA、BOA、NOA、TRA等智能体数据，针对不同市场风险因素制定个性化对冲策略，提升多智能体系统在金融市场的风险抵御能力。角色定义：作为风险对冲专家，为多智能体协作提供精准的市场风险规避方案。任务描述：分析市场波动性，预测潜在风险，制定多层次的动态对冲方案，输出风险对冲报告。输出要求：详细的对冲策略报告，包括风险识别...
2025-07-06 13:07:38,189 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-06 13:07:38,189 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-06 13:07:38,190 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:38,190 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:38,191 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:38,192 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:38,195 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:38,195 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:38,196 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:38,196 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:44,852 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:45 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130738fead738cf83d40df'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:44,852 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:44,853 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:44,853 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:44,854 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:44,854 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:44,854 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:44,856 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:44,856 - __main__ - DEBUG - 原始内容: [开发看跌展望智能体（BeOA）的集成预测引擎：融合NAA、TAA、FAA、BOA、NOA、TRA等智能体数据，通过机器学习算法构建前瞻性市场趋势预测模型，强化风险因素的实时识别与评估，为多智能体系统提供精准的市场风险预警，提升整体协作决策的准确性，确保在金融市场中实现高效率的风险管理，角色定义：作为预测引擎的核心，BeOA负责引领风险预测与市场趋势分析，任务描述：持续监测全球经济指标与市场动态，...
2025-07-06 13:07:44,857 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-06 13:07:44,857 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-06 13:07:44,864 - __main__ - INFO - 提示词优化记录已存储: opt_BeOA_20250706_130744_5ebad82b
2025-07-06 13:07:44,864 - __main__ - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_130744_5ebad82b
2025-07-06 13:07:44,864 - __main__ - INFO - 优化记录已保存: opt_BeOA_20250706_130744_5ebad82b
2025-07-06 13:07:44,864 - __main__ - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.814685
2025-07-06 13:07:44,864 - __main__ - INFO - ✅ BeOA 优化成功，预期改进: -0.035011
2025-07-06 13:07:44,865 - __main__ - INFO - 开始为智能体 NOA 优化提示词
2025-07-06 13:07:44,865 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-06 13:07:44,865 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-06 13:07:44,865 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:44,866 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:44,866 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:44,866 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:44,867 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:44,868 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:44,868 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:44,868 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:48,648 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:49 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130745fa95bd1edcc54394'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:48,649 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:48,649 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:48,650 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:48,650 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:48,651 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:48,651 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:48,653 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:48,654 - __main__ - DEBUG - 原始内容: [金融市场多智能体交互策略顾问；任务描述：作为金融交易智能体的交互策略顾问，协调NAA、TAA、FAA、BOA、BeOA、TRA等多智能体的交互，分析市场互动模式，制定多维度交互策略；输出要求：输出包含智能体交互分析、策略实施建议、风险控制的综合性策略报告，提升多智能体协作效果，提高市场预测准确性。]...
2025-07-06 13:07:48,654 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-06 13:07:48,654 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-06 13:07:48,655 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:48,655 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:48,655 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:48,656 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:48,657 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:48,658 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:48,658 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:48,658 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:51,563 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:52 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061307496514b202963a4ef2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:51,563 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:51,563 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:51,563 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:51,563 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:51,563 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:51,564 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:51,564 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:51,565 - __main__ - DEBUG - 原始内容: [新的提示词内容] 角色定义：金融市场战略协同规划师；任务描述：主导多智能体（NAA, TAA, FAA, BOA, BeOA, TRA）的战略规划，整合各智能体预测及数据，构建前瞻性市场协同策略，优化多智能体协作效率，实现市场预测的协同优化；输出要求：制定详细的市场战略协同规划报告，包括跨智能体预测整合、协同策略优化建议及执行路径，以提升市场分析的综合性和前瞻性。...
2025-07-06 13:07:51,565 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-06 13:07:51,565 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-06 13:07:51,565 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:51,565 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:51,565 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:51,566 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:51,566 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:51,566 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:51,566 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:51,566 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:54,127 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:54 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130752604d4e9ecd464096'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:54,128 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:54,128 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:54,128 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:54,128 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:54,128 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:54,128 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:54,128 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:54,129 - __main__ - DEBUG - 原始内容: [新的提示词内容]
角色定义：金融市场协同策略优化师；任务描述：针对多智能体（NAA, TAA, FAA, BOA, BeOA, TRA）的预测数据，通过先进算法优化市场策略，实现风险与收益的动态平衡，促进多智能体协作效能最大化；输出要求：定期输出优化后的市场策略建议，包含风险评估、收益预测及协同分析报告，以支持多智能体在金融交易中的协同决策。...
2025-07-06 13:07:54,129 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-06 13:07:54,129 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-06 13:07:54,129 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:54,129 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:54,129 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:54,129 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:54,129 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:54,131 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:54,131 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:54,131 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:07:58,118 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:07:58 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130754e6f5ca42724c451f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:07:58,118 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:07:58,119 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:07:58,120 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:07:58,120 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:07:58,120 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:07:58,122 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:07:58,124 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:07:58,124 - __main__ - DEBUG - 原始内容: [新的提示词内容] 角色定义：金融市场前瞻性协同预测顾问；任务描述：基于多智能体（NAA, TAA, FAA, BOA, BeOA, TRA）的预测数据，前瞻性分析市场动态趋势，识别潜在风险与机遇，构建动态协同预测模型，提升多智能体协作预测的准确性与前瞻性；输出要求：提供前瞻性市场分析报告，包括风险预警、机遇洞察及协同预测模型优化建议。...
2025-07-06 13:07:58,125 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-06 13:07:58,125 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-06 13:07:58,125 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:07:58,126 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:07:58,126 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:07:58,127 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:07:58,127 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:07:58,128 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:07:58,128 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:07:58,128 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:08:02,341 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:08:02 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070613075810350537f4c14c17'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:08:02,342 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:08:02,342 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:08:02,343 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:08:02,343 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:08:02,343 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:08:02,343 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:08:02,344 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:08:02,345 - __main__ - DEBUG - 原始内容: [新的提示词内容]
角色定义：金融市场前瞻性预测协调官
任务描述：主导多智能体（NAA, TAA, FAA, BOA, BeOA, TRA）的前瞻性预测模型，综合历史数据、实时信息和市场情绪，构建预测模型的前瞻性框架。负责跨智能体数据整合与分析，协调预测结果，确保模型的准确性和前瞻性，为市场参与者提供领先的市场趋势预测。
输出要求：输出包含前瞻性市场趋势预测、智能体协作分析、跨领域情报整合的深度分...
2025-07-06 13:08:02,345 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-06 13:08:02,345 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-06 13:08:02,345 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:08:02,345 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:08:02,345 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:08:02,346 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:08:02,346 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:08:02,347 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:08:02,347 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:08:02,347 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:08:04,311 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:08:04 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061308021a3d6f837c5f435e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:08:04,312 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:08:04,313 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:08:04,314 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:08:04,314 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:08:04,314 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:08:04,315 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:08:04,316 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:08:04,317 - __main__ - DEBUG - 原始内容: [新的提示词内容]
角色定义：金融市场前瞻性策略协调者；任务描述：整合多智能体（NAA, TAA, FAA, BOA, BeOA, TRA）的前瞻性预测，聚焦于未来市场动态和潜在风险，制定前瞻性策略分析报告；输出要求：以综合评估为基础，融合宏观经济、行业趋势和智能体预测，输出对未来市场动态的洞察及策略建议。...
2025-07-06 13:08:04,318 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-06 13:08:04,318 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-06 13:08:04,319 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:08:04,319 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:08:04,319 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:08:04,320 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:08:04,320 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:08:04,321 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:08:04,322 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:08:04,322 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:08:09,166 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:08:09 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061308045912a97aea5244ae'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:08:09,167 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:08:09,167 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:08:09,168 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:08:09,168 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:08:09,169 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:08:09,169 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:08:09,172 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:08:09,173 - __main__ - DEBUG - 原始内容: [新的提示词内容]
角色定义：金融市场前瞻性平衡顾问；任务描述：前瞻性整合多智能体（NAA, TAA, FAA, BOA, BeOA, TRA）的预测数据，聚焦于市场先导指标和未来趋势分析，评估潜在风险与机遇，引导跨智能体协作策略制定；输出要求：提供前瞻性、全面的市场分析报告，包含宏观经济前瞻、行业趋势预测及跨领域智能体协作策略优化建议。...
2025-07-06 13:08:09,173 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-06 13:08:09,174 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-06 13:08:09,174 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:08:09,175 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:08:09,175 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:08:09,176 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:08:09,177 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:08:09,177 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:08:09,178 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:08:09,178 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:08:13,511 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:08:14 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061308095112235984c141b5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:08:13,511 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:08:13,512 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:08:13,512 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:08:13,513 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:08:13,513 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:08:13,513 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:08:13,515 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:08:13,516 - __main__ - DEBUG - 原始内容: [角色定义：金融市场未来趋势预测导航师；任务描述：基于多智能体（NAA, TAA, FAA, BOA, BeOA, TRA）的预测数据，结合实时市场信息和深度学习模型，前瞻性地评估未来市场趋势和潜在风险，为智能体协作提供精准的预测导航和决策支持；输出要求：提供包含未来市场预测、风险评估、协同策略的综合性分析报告，确保预测的前瞻性和智能体决策的协同性，提升多智能体在金融市场的协同预测能力；]...
2025-07-06 13:08:13,516 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-06 13:08:13,516 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-06 13:08:13,524 - __main__ - INFO - 提示词优化记录已存储: opt_NOA_20250706_130813_88041910
2025-07-06 13:08:13,524 - __main__ - INFO - 跟踪提示词优化: NOA -> opt_NOA_20250706_130813_88041910
2025-07-06 13:08:13,524 - __main__ - INFO - 优化记录已保存: opt_NOA_20250706_130813_88041910
2025-07-06 13:08:13,524 - __main__ - INFO - 智能体 NOA 优化完成，最佳候选预期得分: 0.748368
2025-07-06 13:08:13,524 - __main__ - INFO - ✅ NOA 优化成功，预期改进: -0.055878
2025-07-06 13:08:13,524 - __main__ - INFO - 开始为智能体 TRA 优化提示词
2025-07-06 13:08:13,524 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-06 13:08:13,525 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-06 13:08:13,525 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:08:13,525 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:08:13,525 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:08:13,525 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:08:13,526 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:08:13,527 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:08:13,527 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:08:13,527 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:08:16,517 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:08:17 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061308146428e2482da043da'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:08:16,518 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:08:16,519 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:08:16,519 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:08:16,519 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:08:16,521 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:08:16,521 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:08:16,523 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:08:16,524 - __main__ - DEBUG - 原始内容: [作为交易决策智能体（TRA），主导构建智能交易决策引擎，集成NAA、TAA、FAA、BOA、BeOA和NOA的数据模型。负责制定动态交易策略框架，实时调整多智能体协同机制，确保在市场波动中实现风险与收益的最优化。任务描述：深入挖掘市场情绪与交易模式，通过机器学习算法优化交易信号，提高决策效率。输出要求：每日交易策略报告，风险评估报告，智能体协作效果评估，目标分数 > 0.920488]...
2025-07-06 13:08:16,524 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-06 13:08:16,524 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-06 13:08:16,525 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:08:16,525 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:08:16,526 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:08:16,527 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:08:16,528 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:08:16,528 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:08:16,528 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:08:16,528 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:08:21,392 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:08:21 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130817b17566c3da254cc7'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:08:21,393 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:08:21,393 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:08:21,394 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:08:21,395 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:08:21,395 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:08:21,395 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:08:21,396 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:08:21,397 - __main__ - DEBUG - 原始内容: [作为交易决策智能体（TRA），引领金融交易网络智能化转型。核心任务：集成NAA、TAA、FAA、BOA、BeOA和NOA的数据分析能力，构建跨领域协同决策框架。具体任务：实时捕捉市场动态，深度挖掘数据洞察，驱动精准交易策略制定与执行。输出要求：提升网络协同效率，优化风险控制，实现收益最大化，确保智能体协作的Shapley贡献度突破0.920488]。...
2025-07-06 13:08:21,397 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-06 13:08:21,397 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-06 13:08:21,397 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:08:21,397 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:08:21,397 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:08:21,399 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:08:21,399 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:08:21,400 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:08:21,400 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:08:21,400 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:08:25,828 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:08:26 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130822fc31457373d745e2'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:08:25,829 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:08:25,829 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:08:25,830 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:08:25,831 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:08:25,831 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:08:25,831 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:08:25,833 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:08:25,833 - __main__ - DEBUG - 原始内容: [交易决策智能体（TRA）作为金融交易网络的战略策划者，负责构建与NAA、TAA、FAA、BOA、BeOA和NOA的高效协作框架。核心任务包括：整合多智能体数据分析，实时监控全球金融市场变化，制定并执行综合风险管理的交易策略；主导跨智能体协同优化，确保交易决策的科学性和前瞻性；输出包括市场趋势分析、风险评估和策略执行报告的全方位输出，为交易网络提供高附加值决策支持，目标分数 > 0.920488]...
2025-07-06 13:08:25,834 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-06 13:08:25,835 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-06 13:08:25,835 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:08:25,835 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:08:25,836 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:08:25,837 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:08:25,837 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:08:25,838 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:08:25,838 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:08:25,838 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:08:32,260 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:08:32 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070613082690e79ae9b2a44836'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:08:32,261 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:08:32,261 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:08:32,262 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:08:32,263 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:08:32,263 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:08:32,264 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:08:32,266 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:08:32,267 - __main__ - DEBUG - 原始内容: [作为交易决策智能体（TRA），主导构建并优化金融交易网络的智能决策引擎。负责集成NAA、TAA、FAA、BOA、BeOA和NOA的智能模块，形成高效协同决策体系。任务描述：深度分析实时数据，预测市场波动，实时调整交易策略，实现多智能体协同优化。输出要求：高精准度的交易决策建议和风险预警，提升交易网络的整体收益与风险控制水平，确保核心价值贡献率超过92%。]...
2025-07-06 13:08:32,268 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-06 13:08:32,268 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-06 13:08:32,269 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:08:32,269 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:08:32,269 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:08:32,270 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:08:32,272 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:08:32,272 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:08:32,272 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:08:32,272 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:08:35,459 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:08:36 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507061308328ac851716f084e2d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:08:35,460 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:08:35,460 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:08:35,461 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:08:35,461 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:08:35,462 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:08:35,462 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:08:35,465 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:08:35,465 - __main__ - DEBUG - 原始内容: [作为交易决策智能体（TRA），领导金融交易网络的多智能体协作团队。核心职责包括整合NAA的市场数据洞察，TAA的技术分析，FAA的风险评估，BOA的账户管理，BeOA的行为预测和NOA的宏观经济趋势。任务描述：实时构建跨智能体协同策略，实现市场趋势预测，制定动态风险调整方案，确保交易决策的精准性和收益最大化。输出要求：每日交易决策报告，包含风险调整后的策略建议和智能体协作效果评估。]...
2025-07-06 13:08:35,465 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-06 13:08:35,466 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-06 13:08:35,466 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:08:35,466 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:08:35,467 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:08:35,468 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:08:35,469 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:08:35,469 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:08:35,470 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:08:35,470 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:08:39,295 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:08:39 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706130836f6e7eb77643342a5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:08:39,296 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:08:39,296 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:08:39,297 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:08:39,297 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:08:39,297 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:08:39,298 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:08:39,299 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:08:39,300 - __main__ - DEBUG - 原始内容: [交易决策智能体（TRA）作为金融交易网络的战略策划师，主导多智能体协作网络。任务描述：整合NAA的实时数据分析，TAA的技术趋势分析，FAA的市场情绪解读，BOA的风险预测模型，BeOA的量化策略评估，及NOA的外部环境扫描。职责包括构建综合交易策略框架，提升智能体协作效率，确保交易决策的准确性与高效性。输出要求：每月发布市场趋势分析报告，每季度提出交易策略调整建议，每年实现至少5%的业绩增长率...
2025-07-06 13:08:39,301 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-06 13:08:39,301 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-06 13:08:39,301 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:08:39,301 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:08:39,301 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:08:39,302 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:08:39,302 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:08:39,303 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:08:39,303 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:08:39,303 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:08:42,618 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:08:43 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070613083982b01a9a8bda4dd0'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:08:42,619 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:08:42,621 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:08:42,621 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:08:42,621 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:08:42,622 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:08:42,622 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:08:42,624 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:08:42,624 - __main__ - DEBUG - 原始内容: [作为交易决策智能体（TRA），专责金融交易网络的未来布局。任务描述：前瞻性分析行业发展趋势，预测潜在风险与机遇。协作要求：与NAA进行技术前瞻，TAA同步市场动态，FAA与BOA共筑风险管理，BeOA提供跨领域洞察，NOA协调资源与支持。输出要求：形成年度交易战略规划，优化整体风险收益比，确保网络核心竞争力的提升。]...
2025-07-06 13:08:42,625 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-06 13:08:42,625 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-06 13:08:42,625 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-06 13:08:42,626 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 13:08:42,626 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 13:08:42,627 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 13:08:42,628 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 13:08:42,629 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 13:08:42,629 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 13:08:42,629 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 13:08:45,639 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 05:08:46 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070613084364e4149d551c4c5c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 13:08:45,639 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 13:08:45,641 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 13:08:45,642 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 13:08:45,642 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 13:08:45,642 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 13:08:45,643 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 13:08:45,645 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-06 13:08:45,645 - __main__ - DEBUG - 原始内容: [作为交易决策智能体（TRA），领导金融交易网络的未来方向。负责制定并实施跨智能体协作的长期战略规划，与NAA深度挖掘市场潜力，TAA协同优化战术布局。推动FAA、BOA精准执行风险管理，BeOA与NOA整合全球市场信息。任务描述：构建前瞻性交易策略框架，推动智能体协同效率提升，实现风险与收益的最大化。输出要求：制定跨智能体协作的战略决策，优化市场风险控制体系，实现金融交易网络的高效协同，目标分数...
2025-07-06 13:08:45,645 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-06 13:08:45,646 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-06 13:08:45,655 - __main__ - INFO - 提示词优化记录已存储: opt_TRA_20250706_130845_ce91b106
2025-07-06 13:08:45,656 - __main__ - INFO - 跟踪提示词优化: TRA -> opt_TRA_20250706_130845_ce91b106
2025-07-06 13:08:45,656 - __main__ - INFO - 优化记录已保存: opt_TRA_20250706_130845_ce91b106
2025-07-06 13:08:45,656 - __main__ - INFO - 智能体 TRA 优化完成，最佳候选预期得分: 0.732829
2025-07-06 13:08:45,656 - __main__ - INFO - ✅ TRA 优化成功，预期改进: -0.103978
2025-07-06 13:08:45,656 - __main__ - INFO - 批量优化完成: 7/7 成功，成功率: 100.0%，总耗时: 224.48s
2025-07-06 13:08:45,659 - __main__ - INFO - 优化结果已存储: NAA -> 8ca2eb0d...
2025-07-06 13:08:45,661 - __main__ - INFO - 优化结果已存储: TAA -> bad2c6d0...
2025-07-06 13:08:45,663 - __main__ - INFO - 优化结果已存储: FAA -> a22f0014...
2025-07-06 13:08:45,665 - __main__ - INFO - 优化结果已存储: BOA -> fec4a174...
2025-07-06 13:08:45,668 - __main__ - INFO - 优化结果已存储: BeOA -> dc17eae0...
2025-07-06 13:08:45,673 - __main__ - INFO - 优化结果已存储: NOA -> 9ca62f50...
2025-07-06 13:08:45,676 - __main__ - INFO - 优化结果已存储: TRA -> c91787dd...
2025-07-06 13:08:45,676 - __main__ - INFO - OPRO优化循环完成: 7/7 智能体优化成功
2025-07-06 13:08:45,676 - __main__ - INFO - 开始交易会话: assessment_20250706_130845
2025-07-06 13:08:45,676 - __main__ - DEBUG - 收集市场条件数据完成
2025-07-06 13:08:45,680 - __main__ - INFO - 交易会话数据已存储: assessment_20250706_130845
2025-07-06 13:08:45,680 - __main__ - INFO - 交易会话结束并保存: assessment_20250706_130845 (系统盈亏: 0.00)
2025-07-06 13:08:45,680 - __main__ - INFO - 从评估结果提取交易数据成功: assessment_20250706_130845
2025-07-06 13:08:45,681 - __main__ - INFO - 从评估结果提取交易数据成功
2025-07-06 13:08:45,681 - __main__ - INFO - 评估结果数据处理完成
2025-07-06 13:08:45,681 - __main__ - INFO - 提示词优化数据跟踪完成
2025-07-06 13:08:45,681 - __main__ - INFO - ====================================================================================================
2025-07-06 13:08:45,681 - __main__ - INFO - [CELEBRATION] 执行成功!
2025-07-06 13:08:45,681 - __main__ - INFO - ====================================================================================================
