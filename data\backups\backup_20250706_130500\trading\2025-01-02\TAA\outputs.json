[{"timestamp": "2025-07-05T22:10:27.232836", "output_id": "output_20250705_221027_144e7a7f", "input_id": "input_20250705_221022_b783897d", "prompt_id": "prompt_20250705_221022_f9993f49", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "interpretation": "indicating a neutral market condition"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "interpretation": "indicating a slight bullish trend"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 148.0, "interpretation": "50-day MA crossing above 200-day MA, suggesting a potential bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:27.232836", "processing_time": 4.701026, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "interpretation": "indicating a neutral market condition"}, "MACD": {"signal_line": 0.0, "histogram": 0.1, "interpretation": "indicating a slight bullish trend"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 148.0, "interpretation": "50-day MA crossing above 200-day MA, suggesting a potential bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:27.232836", "processing_time": 4.701026, "llm_used": true}, "processing_time": 4.701026, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 10.475756}}, {"timestamp": "2025-07-05T22:10:27.250836", "output_id": "output_20250705_221027_47019db0", "input_id": "input_20250705_221022_9e7c6dc1", "prompt_id": "prompt_20250705_221022_27f63867", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:27.235805", "processing_time": 4.70951, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "bullish crossover"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:27.235805", "processing_time": 4.70951, "llm_used": true}, "processing_time": 4.70951, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 15.185266}}, {"timestamp": "2025-07-05T22:10:27.858808", "output_id": "output_20250705_221027_04b6bdd1", "input_id": "input_20250705_221022_e608819f", "prompt_id": "prompt_20250705_221022_ba0c452b", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "crossover above 50-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:27.858808", "processing_time": 5.382978, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 72, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.03, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "crossover above 50-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:27.858808", "processing_time": 5.382978, "llm_used": true}, "processing_time": 5.382978, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 20.568244}}, {"timestamp": "2025-07-05T22:10:28.741868", "output_id": "output_20250705_221028_bfd49d0c", "input_id": "input_20250705_221022_20fba1f5", "prompt_id": "prompt_20250705_221023_3dc1be92", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "analysis": "MACD signal line is above the zero line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports the bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:28.741868", "processing_time": 6.044687, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "analysis": "MACD signal line is above the zero line and the histogram is positive, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports the bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:28.741868", "processing_time": 6.044687, "llm_used": true}, "processing_time": 6.044687, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 26.612931}}, {"timestamp": "2025-07-05T22:10:29.111270", "output_id": "output_20250705_221029_989b6896", "input_id": "input_20250705_221022_d01b5698", "prompt_id": "prompt_20250705_221023_368dc242", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "price above 50-day MA, bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:29.109921", "processing_time": 6.488768, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "price above 50-day MA, bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:29.109921", "processing_time": 6.488768, "llm_used": true}, "processing_time": 6.488768, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 33.101698999999996}}, {"timestamp": "2025-07-05T22:10:29.986046", "output_id": "output_20250705_221029_180c38c2", "input_id": "input_20250705_221022_4d75dfb7", "prompt_id": "prompt_20250705_221023_2ee10266", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought, suggesting a potential pullback"}, "MACD": {"signal_line": 0.03, "interpretation": "The MACD line is above the signal line, indicating bullish momentum"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 160.0, "interpretation": "Stock price above both 50-day and 200-day moving averages, indicating a long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:29.986046", "processing_time": 7.327835, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "Overbought, suggesting a potential pullback"}, "MACD": {"signal_line": 0.03, "interpretation": "The MACD line is above the signal line, indicating bullish momentum"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 160.0, "interpretation": "Stock price above both 50-day and 200-day moving averages, indicating a long-term bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:29.986046", "processing_time": 7.327835, "llm_used": true}, "processing_time": 7.327835, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 40.429534}}, {"timestamp": "2025-07-05T22:10:30.293048", "output_id": "output_20250705_221030_70e6b5ac", "input_id": "input_20250705_221023_54e3b4dc", "prompt_id": "prompt_20250705_221023_1d8d8093", "raw_response": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 70.2, "overbought": true, "interpretation": "Strong bullish momentum, possible overbought condition."}, "MACD": {"signal_line": 12.3, "histogram": 1.5, "interpretation": "MACD signal line is above zero line with a rising histogram, indicating bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 145.0, "interpretation": "Price is above both the 50-day and 200-day moving averages, indicating long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:30.292043", "processing_time": 7.384975, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 180.0, "indicators": {"RSI": {"current_value": 70.2, "overbought": true, "interpretation": "Strong bullish momentum, possible overbought condition."}, "MACD": {"signal_line": 12.3, "histogram": 1.5, "interpretation": "MACD signal line is above zero line with a rising histogram, indicating bullish trend."}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 145.0, "interpretation": "Price is above both the 50-day and 200-day moving averages, indicating long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:30.292043", "processing_time": 7.384975, "llm_used": true}, "processing_time": 7.384975, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 47.814508999999994}}, {"timestamp": "2025-07-05T22:10:31.617476", "output_id": "output_20250705_221031_003d2562", "input_id": "input_20250705_221022_4da155fe", "prompt_id": "prompt_20250705_221023_3049a76e", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "overbought": true}, "MACD": {"signal_line": 10, "histogram": {"current_value": 0.5, "positive": true}}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "cross": {"50_day_MA_crossing_200_day_MA": false}}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:31.617476", "processing_time": 9.013936, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "overbought": true}, "MACD": {"signal_line": 10, "histogram": {"current_value": 0.5, "positive": true}}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "cross": {"50_day_MA_crossing_200_day_MA": false}}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:31.617476", "processing_time": 9.013936, "llm_used": true}, "processing_time": 9.013936, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 56.828444999999995}}, {"timestamp": "2025-07-05T22:10:32.655931", "output_id": "output_20250705_221032_865a3960", "input_id": "input_20250705_221027_20922414", "prompt_id": "prompt_20250705_221027_d594ccca", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "RSI is above 70, indicating the stock may be overbought, but the upward trend is strong."}, "MACD": {"current_value": 0.03, "interpretation": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50-Day_MA": 160.0, "200-Day_MA": 150.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:32.654931", "processing_time": 4.766315, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "RSI is above 70, indicating the stock may be overbought, but the upward trend is strong."}, "MACD": {"current_value": 0.03, "interpretation": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50-Day_MA": 160.0, "200-Day_MA": 150.0, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:32.654931", "processing_time": 4.766315, "llm_used": true}, "processing_time": 4.766315, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 61.594759999999994}}, {"timestamp": "2025-07-05T22:10:32.868005", "output_id": "output_20250705_221032_f6156275", "input_id": "input_20250705_221027_3765c0cd", "prompt_id": "prompt_20250705_221027_124eff79", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is in the middle of the overbought territory, indicating a potential pullback, but not necessarily a trend reversal."}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "analysis": "The MACD is close to the signal line with a slight bullish crossover, suggesting a potential continuation of the upward trend."}, "Moving_Average": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:32.868005", "processing_time": 5.370772, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is in the middle of the overbought territory, indicating a potential pullback, but not necessarily a trend reversal."}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "analysis": "The MACD is close to the signal line with a slight bullish crossover, suggesting a potential continuation of the upward trend."}, "Moving_Average": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "analysis": "The stock price is above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:32.868005", "processing_time": 5.370772, "llm_used": true}, "processing_time": 5.370772, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 66.965532}}, {"timestamp": "2025-07-05T22:10:33.071560", "output_id": "output_20250705_221033_02672f17", "input_id": "input_20250705_221028_efe385ca", "prompt_id": "prompt_20250705_221028_caf75675", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating that the stock may be overbought, but the trend remains bullish."}, "MACD": {"signal_line": 0.2, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, confirming the bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:33.071560", "processing_time": 4.770579, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating that the stock may be overbought, but the trend remains bullish."}, "MACD": {"signal_line": 0.2, "analysis": "MACD signal line is above the zero line, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, confirming the bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:33.071560", "processing_time": 4.770579, "llm_used": true}, "processing_time": 4.770579, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 71.736111}}, {"timestamp": "2025-07-05T22:10:33.100234", "output_id": "output_20250705_221033_13e20e3d", "input_id": "input_20250705_221027_16f22354", "prompt_id": "prompt_20250705_221027_04564b51", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:33.100234", "processing_time": 6.002112, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:33.100234", "processing_time": 6.002112, "llm_used": true}, "processing_time": 6.002112, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 77.73822299999999}}, {"timestamp": "2025-07-05T22:10:33.315746", "output_id": "output_20250705_221033_6b83415a", "input_id": "input_20250705_221028_2af2a4e1", "prompt_id": "prompt_20250705_221028_6d22616e", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover", "histogram": "positive trend"}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 125.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:33.315746", "processing_time": 4.886023, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover", "histogram": "positive trend"}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 125.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:33.315746", "processing_time": 4.886023, "llm_used": true}, "processing_time": 4.886023, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 82.62424599999999}}, {"timestamp": "2025-07-05T22:10:33.520329", "output_id": "output_20250705_221033_da4b9a4b", "input_id": "input_20250705_221026_7552d10e", "prompt_id": "prompt_20250705_221026_a6703676", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "interpretation": "positive crossover, indicating potential upside momentum"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 130.0, "interpretation": "short-term moving average above long-term moving average, suggesting bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:33.520329", "processing_time": 6.758343, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "interpretation": "positive crossover, indicating potential upside momentum"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 130.0, "interpretation": "short-term moving average above long-term moving average, suggesting bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:33.520329", "processing_time": 6.758343, "llm_used": true}, "processing_time": 6.758343, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 89.38258899999998}}, {"timestamp": "2025-07-05T22:10:34.237748", "output_id": "output_20250705_221034_0cba344e", "input_id": "input_20250705_221027_5cdfefe9", "prompt_id": "prompt_20250705_221027_238d9007", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating the stock may be overbought, but the positive news flow supports the bullish trend."}, "MACD": {"current_value": "positive", "analysis": "The MACD line is above the signal line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:34.237748", "processing_time": 6.992436, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "analysis": "RSI is above 70, indicating the stock may be overbought, but the positive news flow supports the bullish trend."}, "MACD": {"current_value": "positive", "analysis": "The MACD line is above the signal line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:34.237748", "processing_time": 6.992436, "llm_used": true}, "processing_time": 6.992436, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 96.37502499999998}}, {"timestamp": "2025-07-05T22:10:34.607892", "output_id": "output_20250705_221034_1f78cd6f", "input_id": "input_20250705_221027_a451118a", "prompt_id": "prompt_20250705_221027_eec7dfa0", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:34.607892", "processing_time": 7.280252, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:34.607892", "processing_time": 7.280252, "llm_used": true}, "processing_time": 7.280252, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 103.65527699999998}}, {"timestamp": "2025-07-05T22:10:36.708857", "output_id": "output_20250705_221036_86085a55", "input_id": "input_20250705_221028_1ecfefea", "prompt_id": "prompt_20250705_221028_68efe55c", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "short-term MA above long-term MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:36.708857", "processing_time": 8.520922, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "short-term MA above long-term MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:36.708857", "processing_time": 8.520922, "llm_used": true}, "processing_time": 8.520922, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 112.17619899999998}}, {"timestamp": "2025-07-05T22:10:45.133539", "output_id": "output_20250705_221045_882a7475", "input_id": "input_20250705_221030_9af4606b", "prompt_id": "prompt_20250705_221030_1604f157", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:45.133539", "processing_time": 14.863812, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day and 200-day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:45.133539", "processing_time": 14.863812, "llm_used": true}, "processing_time": 14.863812, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 127.04001099999998}}, {"timestamp": "2025-07-05T22:10:46.967782", "output_id": "output_20250705_221046_316f42ab", "input_id": "input_20250705_221042_9422c088", "prompt_id": "prompt_20250705_221042_a497d14e", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:46.967782", "processing_time": 4.918295, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50-day MA and 200-day MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:46.967782", "processing_time": 4.918295, "llm_used": true}, "processing_time": 4.918295, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 131.95830599999996}}, {"timestamp": "2025-07-05T22:10:49.942985", "output_id": "output_20250705_221049_06fbda85", "input_id": "input_20250705_221045_6ee120b0", "prompt_id": "prompt_20250705_221045_c49eec1d", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 72, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "short-term MA above long-term MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:49.942985", "processing_time": 4.002288, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 72, "interpretation": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "short-term MA above long-term MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:49.942985", "processing_time": 4.002288, "llm_used": true}, "processing_time": 4.002288, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 135.96059399999996}}, {"timestamp": "2025-07-05T22:10:53.635619", "output_id": "output_20250705_221053_bd588cf4", "input_id": "input_20250705_221043_1653d37b", "prompt_id": "prompt_20250705_221044_f395534c", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 147.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:53.635619", "processing_time": 9.644353, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.05, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 147.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:53.635619", "processing_time": 9.644353, "llm_used": true}, "processing_time": 9.644353, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 145.60494699999995}}, {"timestamp": "2025-07-05T22:10:54.725495", "output_id": "output_20250705_221054_753aa690", "input_id": "input_20250705_221050_980abeed", "prompt_id": "prompt_20250705_221050_5f134e90", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "signal": "price above 50-Day MA and 200-Day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:54.716984", "processing_time": 4.655934, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "signal": "price above 50-Day MA and 200-Day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:54.716984", "processing_time": 4.655934, "llm_used": true}, "processing_time": 4.655934, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 150.26088099999996}}, {"timestamp": "2025-07-05T22:10:54.821014", "output_id": "output_20250705_221054_e0c70bfd", "input_id": "input_20250705_221049_4a0057ce", "prompt_id": "prompt_20250705_221049_b0b353d6", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 100, "histogram": 0.5, "analysis": "MACD signal line is above the zero line with a positive histogram, suggesting bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:54.797618", "processing_time": 5.760831, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong bullish momentum."}, "MACD": {"signal_line": 100, "histogram": 0.5, "analysis": "MACD signal line is above the zero line with a positive histogram, suggesting bullish trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:54.797618", "processing_time": 5.760831, "llm_used": true}, "processing_time": 5.760831, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 156.02171199999995}}, {"timestamp": "2025-07-05T22:10:57.984061", "output_id": "output_20250705_221057_1687f7d7", "input_id": "input_20250705_221053_a343b6e6", "prompt_id": "prompt_20250705_221053_b707086d", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought, but considering the bullish news and strong trend, this is a positive sign."}, "MACD": {"current_value": "positive crossover", "analysis": "The MACD line has crossed above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 135.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:57.984061", "processing_time": 4.730068, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 70, indicating the stock may be overbought, but considering the bullish news and strong trend, this is a positive sign."}, "MACD": {"current_value": "positive crossover", "analysis": "The MACD line has crossed above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 135.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:57.984061", "processing_time": 4.730068, "llm_used": true}, "processing_time": 4.730068, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 160.75177999999994}}, {"timestamp": "2025-07-05T22:10:58.486521", "output_id": "output_20250705_221058_efb81aaf", "input_id": "input_20250705_221054_1feeb90c", "prompt_id": "prompt_20250705_221054_8cfc23f4", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68.5, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:58.486521", "processing_time": 4.146032, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68.5, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "signal": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:58.486521", "processing_time": 4.146032, "llm_used": true}, "processing_time": 4.146032, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 164.89781199999993}}, {"timestamp": "2025-07-05T22:10:58.887240", "output_id": "output_20250705_221058_cfac914e", "input_id": "input_20250705_221054_545906d5", "prompt_id": "prompt_20250705_221054_72807f2f", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:58.887240", "processing_time": 4.291731, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "above 50-day and 200-day MAs"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:10:58.887240", "processing_time": 4.291731, "llm_used": true}, "processing_time": 4.291731, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 169.18954299999993}}, {"timestamp": "2025-07-05T22:11:01.606633", "output_id": "output_20250705_221101_2f355c8f", "input_id": "input_20250705_221055_11d9887d", "prompt_id": "prompt_20250705_221055_d957d70c", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is above 50, indicating a neutral market condition."}, "MACD": {"signal_line": 0, "analysis": "The MACD line is crossing the signal line at 0, suggesting a lack of strong momentum in either direction."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 150.0, "analysis": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, indicating a short-term bearish but long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:11:01.600117", "processing_time": 5.823256, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 62, "analysis": "The RSI is above 50, indicating a neutral market condition."}, "MACD": {"signal_line": 0, "analysis": "The MACD line is crossing the signal line at 0, suggesting a lack of strong momentum in either direction."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 150.0, "analysis": "The stock is currently trading below its 50-day moving average but above its 200-day moving average, indicating a short-term bearish but long-term bullish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:11:01.600117", "processing_time": 5.823256, "llm_used": true}, "processing_time": 5.823256, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 175.01279899999992}}, {"timestamp": "2025-07-05T22:11:02.599514", "output_id": "output_20250705_221102_ad08d722", "input_id": "input_20250705_221057_35d76b0c", "prompt_id": "prompt_20250705_221057_b939c126", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is in the overbought territory, indicating that the stock might be due for a pullback."}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "interpretation": "The MACD histogram is positive and rising, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 145.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:11:02.599514", "processing_time": 4.800823, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 68, "interpretation": "The RSI is in the overbought territory, indicating that the stock might be due for a pullback."}, "MACD": {"signal_line": 0.0, "histogram": 0.05, "interpretation": "The MACD histogram is positive and rising, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 145.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:11:02.599514", "processing_time": 4.800823, "llm_used": true}, "processing_time": 4.800823, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 179.81362199999992}}, {"timestamp": "2025-07-05T22:11:03.125757", "output_id": "output_20250705_221103_b4aafcba", "input_id": "input_20250705_221054_07e6e167", "prompt_id": "prompt_20250705_221054_d9aac198", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70.5, "overbought": true}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "bullish": true}, "Moving_Averages": {"50_day": 135.0, "200_day": 125.0, "bullish": true}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:11:03.125757", "processing_time": 8.338027, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 70.5, "overbought": true}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "bullish": true}, "Moving_Averages": {"50_day": 135.0, "200_day": 125.0, "bullish": true}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:11:03.125757", "processing_time": 8.338027, "llm_used": true}, "processing_time": 8.338027, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 188.15164899999994}}, {"timestamp": "2025-07-05T22:11:04.086501", "output_id": "output_20250705_221104_d2dc406c", "input_id": "input_20250705_221056_9fce8e6d", "prompt_id": "prompt_20250705_221056_1e25e3ce", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 125.0, "interpretation": "price above long-term and short-term moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:11:04.068330", "processing_time": 7.443479, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 135.0, "200_day_MA": 125.0, "interpretation": "price above long-term and short-term moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:11:04.068330", "processing_time": 7.443479, "llm_used": true}, "processing_time": 7.443479, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 195.59512799999993}}, {"timestamp": "2025-07-05T22:11:04.426464", "output_id": "output_20250705_221104_65633d6b", "input_id": "input_20250705_221057_174500e1", "prompt_id": "prompt_20250705_221057_191bda83", "raw_response": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought, indicating a potential reversal of the uptrend."}, "MACD": {"signal_line": 20, "interpretation": "The MACD is above the signal line, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 160.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, indicating long-term bullishness."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:11:04.426464", "processing_time": 6.94647, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-02", "trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought, indicating a potential reversal of the uptrend."}, "MACD": {"signal_line": 20, "interpretation": "The MACD is above the signal line, indicating bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 160.0, "interpretation": "The stock is trading above its 50-day and 200-day moving averages, indicating long-term bullishness."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:11:04.426464", "processing_time": 6.94647, "llm_used": true}, "processing_time": 6.94647, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 202.54159799999994}}, {"timestamp": "2025-07-05T22:11:06.471604", "output_id": "output_20250705_221106_1fb2fffb", "input_id": "input_20250705_221059_39e1b7c2", "prompt_id": "prompt_20250705_221059_1804b8cf", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "signal": "crossover above 200_day_MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:11:06.471604", "processing_time": 6.581987, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "signal": "crossover above 200_day_MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:11:06.471604", "processing_time": 6.581987, "llm_used": true}, "processing_time": 6.581987, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 209.12358499999993}}, {"timestamp": "2025-07-05T22:12:13.949795", "output_id": "output_20250705_221213_be390c1f", "input_id": "input_20250705_221205_4b5297fb", "prompt_id": "prompt_20250705_221205_52020917", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "MACD line is above the signal line, and the histogram is positive, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 145, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:12:13.949795", "processing_time": 8.695135, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong bullish trend."}, "MACD": {"signal_line": 10, "histogram": 5, "analysis": "MACD line is above the signal line, and the histogram is positive, suggesting upward momentum."}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 145, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:12:13.949795", "processing_time": 8.695135, "llm_used": true}, "processing_time": 8.695135, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 8.695135}}, {"timestamp": "2025-07-05T22:13:01.074999", "output_id": "output_20250705_221301_15423309", "input_id": "input_20250705_221255_5ce3fa52", "prompt_id": "prompt_20250705_221256_26d05b99", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": " bullish crossover"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "interpretation": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:01.065779", "processing_time": 5.643173, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 72, "interpretation": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.02, "interpretation": " bullish crossover"}, "Moving_Averages": {"50-Day_MA": 155.0, "200-Day_MA": 145.0, "interpretation": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:01.065779", "processing_time": 5.643173, "llm_used": true}, "processing_time": 5.643173, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 14.338308000000001}}, {"timestamp": "2025-07-05T22:13:01.446920", "output_id": "output_20250705_221301_979ea4f5", "input_id": "input_20250705_221256_a2998b95", "prompt_id": "prompt_20250705_221257_e58666f0", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.005, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 152.0, "200-Day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:01.445906", "processing_time": 5.867647, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.005, "signal": "neutral"}, "Moving_Averages": {"50-Day_MA": 152.0, "200-Day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:01.445906", "processing_time": 5.867647, "llm_used": true}, "processing_time": 5.867647, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 20.205955000000003}}, {"timestamp": "2025-07-05T22:13:01.717437", "output_id": "output_20250705_221301_41585f08", "input_id": "input_20250705_221256_476cfc83", "prompt_id": "prompt_20250705_221257_5581fcac", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.5, "interpretation": "Signal line above zero, indicating bullish momentum"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Price above 50-day and 200-day moving averages, suggesting long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:01.717437", "processing_time": 6.101111, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "Overbought"}, "MACD": {"signal_line": 0.0, "histogram": 0.5, "interpretation": "Signal line above zero, indicating bullish momentum"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Price above 50-day and 200-day moving averages, suggesting long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:01.717437", "processing_time": 6.101111, "llm_used": true}, "processing_time": 6.101111, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 26.307066000000003}}, {"timestamp": "2025-07-05T22:13:02.768993", "output_id": "output_20250705_221302_b0006f3d", "input_id": "input_20250705_221255_d208c4bb", "prompt_id": "prompt_20250705_221255_751b9ecd", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 135.0, "signal": "50-Day_MA above 200-Day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:02.767653", "processing_time": 7.546506, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal": "bullish crossover"}, "Moving_Averages": {"50-Day_MA": 150.0, "200-Day_MA": 135.0, "signal": "50-Day_MA above 200-Day_MA"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:02.767653", "processing_time": 7.546506, "llm_used": true}, "processing_time": 7.546506, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 33.853572}}, {"timestamp": "2025-07-05T22:13:03.362497", "output_id": "output_20250705_221303_7f321315", "input_id": "input_20250705_221255_e362bcd4", "prompt_id": "prompt_20250705_221256_c4cca8ab", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 145, "interpretation": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:03.362497", "processing_time": 7.919867, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 155, "200_day_MA": 145, "interpretation": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:03.362497", "processing_time": 7.919867, "llm_used": true}, "processing_time": 7.919867, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 41.773438999999996}}, {"timestamp": "2025-07-05T22:13:03.984298", "output_id": "output_20250705_221303_7f840714", "input_id": "input_20250705_221300_836eadd7", "prompt_id": "prompt_20250705_221300_29152c7b", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "50-day MA crossing above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:03.984298", "processing_time": 3.653221, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 69, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "signal": "50-day MA crossing above 200-day MA"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:03.984298", "processing_time": 3.653221, "llm_used": true}, "processing_time": 3.653221, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 45.42666}}, {"timestamp": "2025-07-05T22:13:04.764473", "output_id": "output_20250705_221304_62c5a40b", "input_id": "input_20250705_221255_ebc91ed5", "prompt_id": "prompt_20250705_221256_1552f85b", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 10, "histogram": 0.5, "interpretation": "Positive crossover and increasing histogram"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "Price above both moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:04.764473", "processing_time": 9.279842, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 10, "histogram": 0.5, "interpretation": "Positive crossover and increasing histogram"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 150.0, "interpretation": "Price above both moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:04.764473", "processing_time": 9.279842, "llm_used": true}, "processing_time": 9.279842, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 54.706502}}, {"timestamp": "2025-07-05T22:13:04.886539", "output_id": "output_20250705_221304_f550a7f9", "input_id": "input_20250705_221300_d7c1d264", "prompt_id": "prompt_20250705_221300_f38134ab", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:04.886539", "processing_time": 3.966578, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 130.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:04.886539", "processing_time": 3.966578, "llm_used": true}, "processing_time": 3.966578, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 58.67308}}, {"timestamp": "2025-07-05T22:13:05.154107", "output_id": "output_20250705_221305_58add69b", "input_id": "input_20250705_221259_2acdd608", "prompt_id": "prompt_20250705_221300_98b96991", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.01, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above 50-day and 200-day moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:05.154107", "processing_time": 5.154169, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 69, "interpretation": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.01, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above 50-day and 200-day moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:05.154107", "processing_time": 5.154169, "llm_used": true}, "processing_time": 5.154169, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 63.827248999999995}}, {"timestamp": "2025-07-05T22:13:05.745230", "output_id": "output_20250705_221305_8453ae8b", "input_id": "input_20250705_221255_7776165a", "prompt_id": "prompt_20250705_221255_b94f6fad", "raw_response": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56.7, "trend": "neutral"}, "MACD": {"signal_line": 0.02, "trend": "neutral"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:05.745230", "processing_time": 10.48138, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.15, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56.7, "trend": "neutral"}, "MACD": {"signal_line": 0.02, "trend": "neutral"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:05.745230", "processing_time": 10.48138, "llm_used": true}, "processing_time": 10.48138, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 74.308629}}, {"timestamp": "2025-07-05T22:13:06.739589", "output_id": "output_20250705_221306_a70fd234", "input_id": "input_20250705_221300_48bc1b97", "prompt_id": "prompt_20250705_221301_29e9945c", "raw_response": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 120.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:06.739589", "processing_time": 5.749978, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.9, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.03, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 120.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:06.739589", "processing_time": 5.749978, "llm_used": true}, "processing_time": 5.749978, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 80.058607}}, {"timestamp": "2025-07-05T22:13:06.893387", "output_id": "output_20250705_221306_45c04132", "input_id": "input_20250705_221300_7f5fc318", "prompt_id": "prompt_20250705_221300_aafa81b0", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 0, "histogram": 0.2, "interpretation": "Signal line is close to zero, indicating a neutral trend, but the histogram shows slight bullishness"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "Stock price above both the 50-day and 200-day moving averages, indicating long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:06.893387", "processing_time": 6.743655, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 0, "histogram": 0.2, "interpretation": "Signal line is close to zero, indicating a neutral trend, but the histogram shows slight bullishness"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "Stock price above both the 50-day and 200-day moving averages, indicating long-term bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:06.893387", "processing_time": 6.743655, "llm_used": true}, "processing_time": 6.743655, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 86.802262}}, {"timestamp": "2025-07-05T22:13:06.989547", "output_id": "output_20250705_221306_086a44bd", "input_id": "input_20250705_221255_e29021fe", "prompt_id": "prompt_20250705_221255_541b49b3", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:06.989547", "processing_time": 11.620007, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 140.0, "signal": "bullish"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:06.989547", "processing_time": 11.620007, "llm_used": true}, "processing_time": 11.620007, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 98.422269}}, {"timestamp": "2025-07-05T22:13:07.813239", "output_id": "output_20250705_221307_6ccd2e31", "input_id": "input_20250705_221300_96f6f534", "prompt_id": "prompt_20250705_221300_d36a0cca", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 0, "histogram": 0.5, "interpretation": "Signal line close to zero, positive histogram indicating potential upward trend"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "Price above both 50-day and 200-day moving averages, indicating a strong bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:07.813239", "processing_time": 7.416242, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "Overbought"}, "MACD": {"signal_line": 0, "histogram": 0.5, "interpretation": "Signal line close to zero, positive histogram indicating potential upward trend"}, "Moving_Averages": {"50_day_MA": 160.0, "200_day_MA": 140.0, "interpretation": "Price above both 50-day and 200-day moving averages, indicating a strong bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:07.813239", "processing_time": 7.416242, "llm_used": true}, "processing_time": 7.416242, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 105.838511}}, {"timestamp": "2025-07-05T22:13:09.034803", "output_id": "output_20250705_221309_924fccca", "input_id": "input_20250705_221304_6cacb744", "prompt_id": "prompt_20250705_221304_7c3f8c2b", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 72, "analysis": "RSI is above 70, indicating the stock may be overbought, but the trend remains strong."}, "MACD": {"signal_line": 15, "historical_line": 10, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 135, "200_day_MA": 140, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:08.960997", "processing_time": 4.860079, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 72, "analysis": "RSI is above 70, indicating the stock may be overbought, but the trend remains strong."}, "MACD": {"signal_line": 15, "historical_line": 10, "analysis": "The MACD line is above the signal line, suggesting a bullish trend."}, "Moving_Averages": {"50_day_MA": 135, "200_day_MA": 140, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:08.960997", "processing_time": 4.860079, "llm_used": true}, "processing_time": 4.860079, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 110.69859}}, {"timestamp": "2025-07-05T22:13:10.910402", "output_id": "output_20250705_221310_11be4e7b", "input_id": "input_20250705_221303_8ad5598f", "prompt_id": "prompt_20250705_221303_1057a502", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:10.910402", "processing_time": 7.714369, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.01, "histogram": 0.05, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:10.910402", "processing_time": 7.714369, "llm_used": true}, "processing_time": 7.714369, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 118.412959}}, {"timestamp": "2025-07-05T22:13:11.246312", "output_id": "output_20250705_221311_17adee4e", "input_id": "input_20250705_221304_58c911cd", "prompt_id": "prompt_20250705_221304_6a67d207", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0, "histogram": 0.15, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "signal": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:11.246312", "processing_time": 6.96785, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 130.0, "resistance_level": 150.0, "indicators": {"RSI": {"value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0, "histogram": 0.15, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 140.0, "200_day_MA": 120.0, "signal": "price above all moving averages"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:11.246312", "processing_time": 6.96785, "llm_used": true}, "processing_time": 6.96785, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 125.380809}}, {"timestamp": "2025-07-05T22:13:12.726612", "output_id": "output_20250705_221312_ceed77ea", "input_id": "input_20250705_221302_81bf5b30", "prompt_id": "prompt_20250705_221302_1688e152", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 69, "comment": "RSI接近超买区域，但未超出，表明短期内仍有上升潜力。"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "comment": "MACD线在信号线之上，且柱状图上升，显示上升趋势强劲。"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "comment": "50日移动平均线在200日移动平均线之上，显示长期上升趋势。"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:12.726612", "processing_time": 9.957619, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 69, "comment": "RSI接近超买区域，但未超出，表明短期内仍有上升潜力。"}, "MACD": {"signal_line": 0.02, "histogram": 0.01, "comment": "MACD线在信号线之上，且柱状图上升，显示上升趋势强劲。"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 140.0, "comment": "50日移动平均线在200日移动平均线之上，显示长期上升趋势。"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:12.726612", "processing_time": 9.957619, "llm_used": true}, "processing_time": 9.957619, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 135.338428}}, {"timestamp": "2025-07-05T22:13:21.961413", "output_id": "output_20250705_221321_5ef47d1e", "input_id": "input_20250705_221315_7f811e67", "prompt_id": "prompt_20250705_221315_e7beae63", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 70, indicating that the stock may be overbought, but the positive sentiment from news suggests continued upward momentum."}, "MACD": {"value": "positive crossover", "analysis": "MACD shows a positive crossover, suggesting that the stock is gaining momentum and is likely to continue rising."}, "Moving_Average": {"short_term_MA": 154.5, "long_term_MA": 150.0, "analysis": "The stock price is above both the short-term and long-term moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:21.907085", "processing_time": 6.316761, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 70, indicating that the stock may be overbought, but the positive sentiment from news suggests continued upward momentum."}, "MACD": {"value": "positive crossover", "analysis": "MACD shows a positive crossover, suggesting that the stock is gaining momentum and is likely to continue rising."}, "Moving_Average": {"short_term_MA": 154.5, "long_term_MA": 150.0, "analysis": "The stock price is above both the short-term and long-term moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:21.907085", "processing_time": 6.316761, "llm_used": true}, "processing_time": 6.316761, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 141.655189}}, {"timestamp": "2025-07-05T22:13:23.123043", "output_id": "output_20250705_221323_fb0a8a16", "input_id": "input_20250705_221317_b66f14f9", "prompt_id": "prompt_20250705_221317_7e06fc35", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "interpretation": "bullish crossover"}, "moving_averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "short-term MA above long-term MA, indicating a bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:23.123043", "processing_time": 5.826879, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.05, "interpretation": "bullish crossover"}, "moving_averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "short-term MA above long-term MA, indicating a bullish trend"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:23.123043", "processing_time": 5.826879, "llm_used": true}, "processing_time": 5.826879, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 147.482068}}, {"timestamp": "2025-07-05T22:13:27.836798", "output_id": "output_20250705_221327_ff83e4f1", "input_id": "input_20250705_221318_4d1ea3ba", "prompt_id": "prompt_20250705_221318_4bb1f632", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 72, "comment": "RSI is in the overbought zone, indicating that the stock may be due for a pullback."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "comment": "MACD histogram is rising, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "comment": "Stock price above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:27.834796", "processing_time": 9.799042, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"current_value": 72, "comment": "RSI is in the overbought zone, indicating that the stock may be due for a pullback."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "comment": "MACD histogram is rising, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "comment": "Stock price above both the 50-day and 200-day moving averages, indicating a long-term bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:27.834796", "processing_time": 9.799042, "llm_used": true}, "processing_time": 9.799042, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 157.28111}}, {"timestamp": "2025-07-05T22:13:28.898754", "output_id": "output_20250705_221328_0874bcfe", "input_id": "input_20250705_221323_77994637", "prompt_id": "prompt_20250705_221323_de429ccb", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0, "histogram": 0.5, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:28.890754", "processing_time": 5.05525, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "interpretation": "overbought"}, "MACD": {"signal_line": 0, "histogram": 0.5, "interpretation": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "price above all moving averages"}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:28.890754", "processing_time": 5.05525, "llm_used": true}, "processing_time": 5.05525, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 162.33636}}, {"timestamp": "2025-07-05T22:13:30.742260", "output_id": "output_20250705_221330_c2b75e09", "input_id": "input_20250705_221326_61a6350b", "prompt_id": "prompt_20250705_221326_1b1b16e5", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:30.742260", "processing_time": 4.278833, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 70, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:30.742260", "processing_time": 4.278833, "llm_used": true}, "processing_time": 4.278833, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 166.615193}}, {"timestamp": "2025-07-05T22:13:31.963972", "output_id": "output_20250705_221331_fc03d4cd", "input_id": "input_20250705_221324_d2f0b0dc", "prompt_id": "prompt_20250705_221324_4d91325c", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 70, indicating the stock is overbought and may be due for a pullback."}, "MACD": {"signal_line": 0.2, "analysis": "The MACD is above the zero line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:31.963972", "processing_time": 7.212067, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 68, "analysis": "The RSI is above 70, indicating the stock is overbought and may be due for a pullback."}, "MACD": {"signal_line": 0.2, "analysis": "The MACD is above the zero line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 140.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:31.963972", "processing_time": 7.212067, "llm_used": true}, "processing_time": 7.212067, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 173.82726}}, {"timestamp": "2025-07-05T22:13:32.785911", "output_id": "output_20250705_221332_ebfe480b", "input_id": "input_20250705_221327_5a4ef6d7", "prompt_id": "prompt_20250705_221328_d68c95ef", "raw_response": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:32.785911", "processing_time": 4.807965, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.7, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.1, "histogram": 0.2, "signal": "bullish crossover"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "price above 50 and 200 day MAs"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:32.785911", "processing_time": 4.807965, "llm_used": true}, "processing_time": 4.807965, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 178.635225}}, {"timestamp": "2025-07-05T22:13:34.753145", "output_id": "output_20250705_221334_a77f7d02", "input_id": "input_20250705_221330_c3859282", "prompt_id": "prompt_20250705_221330_8c35ad72", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:34.753145", "processing_time": 4.540667, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 68, "signal": "overbought"}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "signal": "bullish crossover"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "signal": "bullish trend"}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:34.753145", "processing_time": 4.540667, "llm_used": true}, "processing_time": 4.540667, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 183.175892}}, {"timestamp": "2025-07-05T22:13:38.228693", "output_id": "output_20250705_221338_3a5d4c89", "input_id": "input_20250705_221331_2dab762d", "prompt_id": "prompt_20250705_221331_6e287d55", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.0, "analysis": "The MACD line is above the signal line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, supporting the bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:38.227692", "processing_time": 6.68053, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 0.0, "analysis": "The MACD line is above the signal line, suggesting bullish momentum."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is trading above its 50-day and 200-day moving averages, supporting the bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:38.227692", "processing_time": 6.68053, "llm_used": true}, "processing_time": 6.68053, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 189.856422}}, {"timestamp": "2025-07-05T22:13:44.505462", "output_id": "output_20250705_221344_57e4efe2", "input_id": "input_20250705_221338_c212d4a2", "prompt_id": "prompt_20250705_221338_216cbfb3", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "The RSI is above 70, indicating that the stock may be overbought and due for a pullback."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "The MACD is above the signal line and the histogram is positive, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 140, "200_day_MA": 130, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:44.505462", "processing_time": 5.854943, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 135.0, "resistance_level": 150.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "The RSI is above 70, indicating that the stock may be overbought and due for a pullback."}, "MACD": {"signal_line": 0.05, "histogram": 0.02, "interpretation": "The MACD is above the signal line and the histogram is positive, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 140, "200_day_MA": 130, "interpretation": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:44.505462", "processing_time": 5.854943, "llm_used": true}, "processing_time": 5.854943, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 195.711365}}, {"timestamp": "2025-07-05T22:13:44.914332", "output_id": "output_20250705_221344_f55cc0fa", "input_id": "input_20250705_221335_21983bce", "prompt_id": "prompt_20250705_221335_15d755cb", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": -0.02, "trend": "neutral"}, "Moving_Average": {"50-Day_MA": 155.0, "200-Day_MA": 150.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:44.914332", "processing_time": 9.142601, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "trend": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": -0.02, "trend": "neutral"}, "Moving_Average": {"50-Day_MA": 155.0, "200-Day_MA": 150.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:44.914332", "processing_time": 9.142601, "llm_used": true}, "processing_time": 9.142601, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 204.853966}}, {"timestamp": "2025-07-05T22:13:45.484816", "output_id": "output_20250705_221345_50849652", "input_id": "input_20250705_221330_07fe093a", "prompt_id": "prompt_20250705_221330_3662b98f", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 70, indicating the stock is overbought and might be due for a pullback."}, "MACD": {"signal_line": 0.02, "analysis": "MACD is above the zero line with a rising signal line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:45.461742", "processing_time": 15.037812, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 165.0, "indicators": {"RSI": {"value": 68, "analysis": "RSI is above 70, indicating the stock is overbought and might be due for a pullback."}, "MACD": {"signal_line": 0.02, "analysis": "MACD is above the zero line with a rising signal line, suggesting bullish momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:45.461742", "processing_time": 15.037812, "llm_used": true}, "processing_time": 15.037812, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 219.89177800000002}}, {"timestamp": "2025-07-05T22:13:47.126501", "output_id": "output_20250705_221347_b35a9fbb", "input_id": "input_20250705_221340_e66df534", "prompt_id": "prompt_20250705_221340_86cd12bf", "raw_response": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 100, "histogram": 0.5, "analysis": "MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:47.126501", "processing_time": 6.215626, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.6, "trading_signal": "buy", "signal_strength": 0.9, "support_level": 150.0, "resistance_level": 170.0, "indicators": {"RSI": {"current_value": 68, "analysis": "RSI is above 50, indicating a strong uptrend."}, "MACD": {"signal_line": 100, "histogram": 0.5, "analysis": "MACD line is above the signal line and the histogram is positive, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 160.0, "200_day_MA": 150.0, "analysis": "The stock is trading above both the 50-day and 200-day moving averages, which supports a bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:47.126501", "processing_time": 6.215626, "llm_used": true}, "processing_time": 6.215626, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 226.10740400000003}}, {"timestamp": "2025-07-05T22:13:48.139507", "output_id": "output_20250705_221348_bc95b51b", "input_id": "input_20250705_221338_a371b47e", "prompt_id": "prompt_20250705_221338_1b8e128f", "raw_response": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "RSI is above 70, indicating that the stock may be overbought but remains in a strong uptrend."}, "MACD": {"signal_line": 0.5, "histogram": 0.2, "interpretation": "MACD histogram is positive and rising, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:48.139507", "processing_time": 9.336169, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.8, "trading_signal": "buy", "signal_strength": 1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 69, "interpretation": "RSI is above 70, indicating that the stock may be overbought but remains in a strong uptrend."}, "MACD": {"signal_line": 0.5, "histogram": 0.2, "interpretation": "MACD histogram is positive and rising, suggesting a bullish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "The stock price is above both the 50-day and 200-day moving averages, indicating a strong bullish trend."}}, "confidence": 0.95, "agent_id": "TAA", "timestamp": "2025-07-05T22:13:48.139507", "processing_time": 9.336169, "llm_used": true}, "processing_time": 9.336169, "llm_used": true, "confidence": 0.95, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 235.44357300000004}}]