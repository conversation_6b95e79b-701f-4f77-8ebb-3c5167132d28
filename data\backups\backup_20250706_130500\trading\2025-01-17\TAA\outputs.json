[{"timestamp": "2025-07-05T22:45:48.478803", "output_id": "output_20250705_224548_079d942c", "input_id": "input_20250705_224531_837628b3", "prompt_id": "prompt_20250705_224531_f20e0508", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "interpretation": "Neutral - the RSI is not overbought or oversold, indicating a balanced market condition."}, "MACD": {"current_value": 0.0, "interpretation": "The MACD is crossing the signal line, suggesting a lack of momentum in either direction."}, "Moving_Averages": {"50-Day_MA": 153.0, "200-Day_MA": 160.0, "interpretation": "The stock is trading slightly below the 50-day MA but above the 200-day MA, suggesting a potential for long-term stability but with a slight downward trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:45:48.478803", "processing_time": 16.592147, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "interpretation": "Neutral - the RSI is not overbought or oversold, indicating a balanced market condition."}, "MACD": {"current_value": 0.0, "interpretation": "The MACD is crossing the signal line, suggesting a lack of momentum in either direction."}, "Moving_Averages": {"50-Day_MA": 153.0, "200-Day_MA": 160.0, "interpretation": "The stock is trading slightly below the 50-day MA but above the 200-day MA, suggesting a potential for long-term stability but with a slight downward trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:45:48.478803", "processing_time": 16.592147, "llm_used": true}, "processing_time": 16.592147, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 1, "total_processing_time": 16.592147}}, {"timestamp": "2025-07-05T22:46:34.955742", "output_id": "output_20250705_224634_de7fed62", "input_id": "input_20250705_224630_b193246c", "prompt_id": "prompt_20250705_224630_3872125f", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal": "neutral", "cross": {"type": "no_cross", "timeframe": "short_term"}}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 148.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:34.955742", "processing_time": 5.097868, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal": "neutral", "cross": {"type": "no_cross", "timeframe": "short_term"}}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 148.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:34.955742", "processing_time": 5.097868, "llm_used": true}, "processing_time": 5.097868, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 2, "total_processing_time": 21.690015000000002}}, {"timestamp": "2025-07-05T22:46:35.583079", "output_id": "output_20250705_224635_0ea2b16b", "input_id": "input_20250705_224629_7db07bcc", "prompt_id": "prompt_20250705_224629_21f8fdda", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "analysis": "The RSI is in the middle of the range, indicating neither overbought nor oversold conditions."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD signal line is slightly positive, but the histogram is negative, suggesting a neutral trend."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 160.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, which indicates a potential bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:35.570259", "processing_time": 5.978711, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "analysis": "The RSI is in the middle of the range, indicating neither overbought nor oversold conditions."}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "analysis": "The MACD signal line is slightly positive, but the histogram is negative, suggesting a neutral trend."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 160.0, "analysis": "The stock is currently below its 50-day and 200-day moving averages, which indicates a potential bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:35.570259", "processing_time": 5.978711, "llm_used": true}, "processing_time": 5.978711, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 3, "total_processing_time": 27.668726000000003}}, {"timestamp": "2025-07-05T22:46:35.781726", "output_id": "output_20250705_224635_8ed4f69e", "input_id": "input_20250705_224630_1de914e0", "prompt_id": "prompt_20250705_224630_7559b88e", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "interpretation": "indicating a neutral trend"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "signal line close to zero, suggesting a lack of strong trend"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "interpretation": "50-day MA above 200-day MA, but not strongly so, indicating a weak bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:35.781726", "processing_time": 5.921345, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "interpretation": "indicating a neutral trend"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "signal line close to zero, suggesting a lack of strong trend"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 140.0, "interpretation": "50-day MA above 200-day MA, but not strongly so, indicating a weak bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:35.781726", "processing_time": 5.921345, "llm_used": true}, "processing_time": 5.921345, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 4, "total_processing_time": 33.590071}}, {"timestamp": "2025-07-05T22:46:36.550302", "output_id": "output_20250705_224636_bed69e47", "input_id": "input_20250705_224629_8b48ef55", "prompt_id": "prompt_20250705_224630_776924d3", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "interpretation": "slightly above neutral zone, suggesting a possible continuation of the current trend"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "interpretation": "MACD signal line is close to zero and the histogram is negative, indicating a weak trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "interpretation": "price is below both the 50-day and 200-day moving averages, suggesting a bearish trend"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:36.550302", "processing_time": 6.809098, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 58, "interpretation": "slightly above neutral zone, suggesting a possible continuation of the current trend"}, "MACD": {"signal_line": 0.05, "histogram": -0.02, "interpretation": "MACD signal line is close to zero and the histogram is negative, indicating a weak trend"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "interpretation": "price is below both the 50-day and 200-day moving averages, suggesting a bearish trend"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:36.550302", "processing_time": 6.809098, "llm_used": true}, "processing_time": 6.809098, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 5, "total_processing_time": 40.399169}}, {"timestamp": "2025-07-05T22:46:38.293903", "output_id": "output_20250705_224638_9cf64a96", "input_id": "input_20250705_224629_1343fe48", "prompt_id": "prompt_20250705_224629_86443524", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "slightly above neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "indicating a neutral trend"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "interpretation": "short-term moving average slightly below long-term moving average, suggesting a slight downward trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:38.293903", "processing_time": 8.57145, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "interpretation": "slightly above neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "indicating a neutral trend"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "interpretation": "short-term moving average slightly below long-term moving average, suggesting a slight downward trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:38.293903", "processing_time": 8.57145, "llm_used": true}, "processing_time": 8.57145, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 6, "total_processing_time": 48.970619}}, {"timestamp": "2025-07-05T22:46:38.505483", "output_id": "output_20250705_224638_1d69cfb0", "input_id": "input_20250705_224629_a4e01530", "prompt_id": "prompt_20250705_224629_01493410", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "comment": "RSI is neutral, indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0, "histogram": 0, "comment": "MACD is close to zero line, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 148.0, "comment": "The stock is currently between its 50-day and 200-day moving averages, suggesting a consolidation phase."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:38.505483", "processing_time": 8.886863, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "comment": "RSI is neutral, indicating no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0, "histogram": 0, "comment": "MACD is close to zero line, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 148.0, "comment": "The stock is currently between its 50-day and 200-day moving averages, suggesting a consolidation phase."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:38.505483", "processing_time": 8.886863, "llm_used": true}, "processing_time": 8.886863, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 7, "total_processing_time": 57.857482}}, {"timestamp": "2025-07-05T22:46:39.128627", "output_id": "output_20250705_224639_6d89ddfa", "input_id": "input_20250705_224630_9bd5fcce", "prompt_id": "prompt_20250705_224630_1e806e55", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56.7, "analysis": "RSI is above 50, indicating neutral momentum."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "analysis": "MACD signal line is close to zero, suggesting a neutral trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, suggesting a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:39.128627", "processing_time": 9.269751, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 56.7, "analysis": "RSI is above 50, indicating neutral momentum."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "analysis": "MACD signal line is close to zero, suggesting a neutral trend."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, suggesting a neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:39.128627", "processing_time": 9.269751, "llm_used": true}, "processing_time": 9.269751, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 8, "total_processing_time": 67.12723299999999}}, {"timestamp": "2025-07-05T22:46:39.142018", "output_id": "output_20250705_224639_699dd493", "input_id": "input_20250705_224635_f1454481", "prompt_id": "prompt_20250705_224635_181c4ffd", "raw_response": {"analysis_date": "2025-01-17", "trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50, "interpretation": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "interpretation": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:39.142018", "processing_time": 3.666688, "llm_used": true}, "parsed_output": {"analysis_date": "2025-01-17", "trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50, "interpretation": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 160.0, "interpretation": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:39.142018", "processing_time": 3.666688, "llm_used": true}, "processing_time": 3.666688, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 9, "total_processing_time": 70.79392099999998}}, {"timestamp": "2025-07-05T22:46:39.686607", "output_id": "output_20250705_224639_d9726b3a", "input_id": "input_20250705_224629_d3d7e785", "prompt_id": "prompt_20250705_224629_5d9a368d", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52.3, "interpretation": "Neutral - The RSI is neither overbought nor oversold, suggesting that the stock is neither too overvalued nor undervalued."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "Neutral - The MACD line is crossing the signal line at zero, indicating no clear trend direction."}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "Neutral - The stock is trading between its 50-day and 200-day moving averages, which suggests a lack of strong trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:39.686607", "processing_time": 9.985407, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52.3, "interpretation": "Neutral - The RSI is neither overbought nor oversold, suggesting that the stock is neither too overvalued nor undervalued."}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "interpretation": "Neutral - The MACD line is crossing the signal line at zero, indicating no clear trend direction."}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "Neutral - The stock is trading between its 50-day and 200-day moving averages, which suggests a lack of strong trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:39.686607", "processing_time": 9.985407, "llm_used": true}, "processing_time": 9.985407, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 10, "total_processing_time": 80.77932799999998}}, {"timestamp": "2025-07-05T22:46:41.267906", "output_id": "output_20250705_224641_736b7148", "input_id": "input_20250705_224634_86459609", "prompt_id": "prompt_20250705_224634_fda8b1f4", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:41.267906", "processing_time": 6.481885, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:41.267906", "processing_time": 6.481885, "llm_used": true}, "processing_time": 6.481885, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 11, "total_processing_time": 87.26121299999998}}, {"timestamp": "2025-07-05T22:46:42.113544", "output_id": "output_20250705_224642_534a6926", "input_id": "input_20250705_224638_79ef05a7", "prompt_id": "prompt_20250705_224638_c778fbd0", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 54, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:42.112609", "processing_time": 3.707521, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 54, "signal": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": -0.02, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:42.112609", "processing_time": 3.707521, "llm_used": true}, "processing_time": 3.707521, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 12, "total_processing_time": 90.96873399999998}}, {"timestamp": "2025-07-05T22:46:43.505672", "output_id": "output_20250705_224643_cfd26d2c", "input_id": "input_20250705_224635_ea6bca0a", "prompt_id": "prompt_20250705_224635_39b578db", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "interpretation": "indicating a neutral market condition"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "signal line close to zero suggests a stable trend"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "the 50-day MA is slightly above the 200-day MA, indicating a slight bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:43.505672", "processing_time": 7.816534, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 60, "interpretation": "indicating a neutral market condition"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "signal line close to zero suggests a stable trend"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "interpretation": "the 50-day MA is slightly above the 200-day MA, indicating a slight bullish trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:43.505672", "processing_time": 7.816534, "llm_used": true}, "processing_time": 7.816534, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 13, "total_processing_time": 98.78526799999999}}, {"timestamp": "2025-07-05T22:46:43.697185", "output_id": "output_20250705_224643_0c94555e", "input_id": "input_20250705_224638_3e38e3b4", "prompt_id": "prompt_20250705_224638_845e217a", "raw_response": {"trend": "neutral", "technical_score": 0.01, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:43.697185", "processing_time": 5.378346, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.01, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:43.697185", "processing_time": 5.378346, "llm_used": true}, "processing_time": 5.378346, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 14, "total_processing_time": 104.16361399999998}}, {"timestamp": "2025-07-05T22:46:43.826348", "output_id": "output_20250705_224643_bbcbded6", "input_id": "input_20250705_224636_c5f907e7", "prompt_id": "prompt_20250705_224636_b64342a2", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50, "trend": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:43.826348", "processing_time": 7.127521, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50, "trend": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "trend": "neutral"}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 145.0, "trend": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:43.826348", "processing_time": 7.127521, "llm_used": true}, "processing_time": 7.127521, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 15, "total_processing_time": 111.29113499999998}}, {"timestamp": "2025-07-05T22:46:44.282049", "output_id": "output_20250705_224644_7e4a3370", "input_id": "input_20250705_224637_6b18ecbf", "prompt_id": "prompt_20250705_224637_74aaf11e", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 148.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:44.282049", "processing_time": 7.216061, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50, "signal": "neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 148.0, "signal": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:44.282049", "processing_time": 7.216061, "llm_used": true}, "processing_time": 7.216061, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 16, "total_processing_time": 118.50719599999998}}, {"timestamp": "2025-07-05T22:46:44.861252", "output_id": "output_20250705_224644_86b6ebe5", "input_id": "input_20250705_224634_031fb88d", "prompt_id": "prompt_20250705_224634_a2f04ff4", "raw_response": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "overbought": true}, "MACD": {"signal_line_above_zero": true, "histogram_positive": true}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 120.0, "cross_over": true}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:44.861252", "processing_time": 10.05222, "llm_used": true}, "parsed_output": {"trend": "bullish", "technical_score": 0.5, "trading_signal": "buy", "signal_strength": 0.8, "support_level": 150.0, "resistance_level": 175.0, "indicators": {"RSI": {"current_value": 70, "overbought": true}, "MACD": {"signal_line_above_zero": true, "histogram_positive": true}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 120.0, "cross_over": true}}, "confidence": 0.9, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:44.861252", "processing_time": 10.05222, "llm_used": true}, "processing_time": 10.05222, "llm_used": true, "confidence": 0.9, "reasoning": "", "metadata": {"analysis_count": 17, "total_processing_time": 128.55941599999997}}, {"timestamp": "2025-07-05T22:46:44.948543", "output_id": "output_20250705_224644_9b3497c8", "input_id": "input_20250705_224640_1cd92146", "prompt_id": "prompt_20250705_224640_b559332b", "raw_response": {"trend": "neutral", "technical_score": 0.01, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.2, "interpretation": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.005, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "interpretation": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:44.948543", "processing_time": 4.907715, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.01, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.2, "interpretation": "neutral"}, "MACD": {"signal_line": 0.01, "histogram": 0.005, "interpretation": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "interpretation": "neutral"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:44.948543", "processing_time": 4.907715, "llm_used": true}, "processing_time": 4.907715, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 18, "total_processing_time": 133.46713099999997}}, {"timestamp": "2025-07-05T22:46:44.995745", "output_id": "output_20250705_224644_9d8e0ea8", "input_id": "input_20250705_224635_10e262fd", "prompt_id": "prompt_20250705_224635_8cb607ac", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "analysis": "RSI is currently neutral, suggesting no overbought or oversold conditions."}, "MACD": {"signal_line": 0.01, "histogram": -0.03, "analysis": "The MACD is in a neutral state with a slight bearish signal line and a negative histogram, indicating a slight downward trend."}, "Moving_Averages": {"50_day_MA": 153.0, "200_day_MA": 165.0, "analysis": "The 50-day moving average is below the 200-day moving average, suggesting a long-term bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:44.991753", "processing_time": 9.0617, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.05, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "analysis": "RSI is currently neutral, suggesting no overbought or oversold conditions."}, "MACD": {"signal_line": 0.01, "histogram": -0.03, "analysis": "The MACD is in a neutral state with a slight bearish signal line and a negative histogram, indicating a slight downward trend."}, "Moving_Averages": {"50_day_MA": 153.0, "200_day_MA": 165.0, "analysis": "The 50-day moving average is below the 200-day moving average, suggesting a long-term bearish trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:44.991753", "processing_time": 9.0617, "llm_used": true}, "processing_time": 9.0617, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 19, "total_processing_time": 142.52883099999997}}, {"timestamp": "2025-07-05T22:46:52.877516", "output_id": "output_20250705_224652_5d2e6468", "input_id": "input_20250705_224647_a27678a2", "prompt_id": "prompt_20250705_224647_49fe038e", "raw_response": {"trend": "neutral", "technical_score": 0.02, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50, "analysis": "RSI is neutral, suggesting no immediate overbought or oversold conditions."}, "MACD": {"current_value": "0,0", "analysis": "MACD is close to zero, indicating a lack of strong momentum in either direction."}, "Moving_Average": {"50_day_MA": 153.5, "200_day_MA": 160.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, suggesting a neutral trend."}}, "confidence": 0.6, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:52.840894", "processing_time": 5.597809, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.02, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50, "analysis": "RSI is neutral, suggesting no immediate overbought or oversold conditions."}, "MACD": {"current_value": "0,0", "analysis": "MACD is close to zero, indicating a lack of strong momentum in either direction."}, "Moving_Average": {"50_day_MA": 153.5, "200_day_MA": 160.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, suggesting a neutral trend."}}, "confidence": 0.6, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:52.840894", "processing_time": 5.597809, "llm_used": true}, "processing_time": 5.597809, "llm_used": true, "confidence": 0.6, "reasoning": "", "metadata": {"analysis_count": 20, "total_processing_time": 148.12663999999998}}, {"timestamp": "2025-07-05T22:46:57.668650", "output_id": "output_20250705_224657_c360df7d", "input_id": "input_20250705_224649_496932e1", "prompt_id": "prompt_20250705_224649_c14049c2", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50, "analysis": "The RSI is close to the neutral zone, indicating no strong bullish or bearish momentum."}, "MACD": {"current_value": "0", "analysis": "The MACD line is close to the signal line, suggesting a lack of strong trend direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently trading above the 50-day MA but below the 200-day MA, indicating a potential for upward momentum but not a strong trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:57.668650", "processing_time": 8.376747, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50, "analysis": "The RSI is close to the neutral zone, indicating no strong bullish or bearish momentum."}, "MACD": {"current_value": "0", "analysis": "The MACD line is close to the signal line, suggesting a lack of strong trend direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently trading above the 50-day MA but below the 200-day MA, indicating a potential for upward momentum but not a strong trend."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:57.668650", "processing_time": 8.376747, "llm_used": true}, "processing_time": 8.376747, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 21, "total_processing_time": 156.50338699999998}}, {"timestamp": "2025-07-05T22:46:58.715372", "output_id": "output_20250705_224658_c1eebf1b", "input_id": "input_20250705_224652_cc2e1f18", "prompt_id": "prompt_20250705_224652_edf6fb2c", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "analysis": "Neutral - RSI is neither overbought nor oversold."}, "MACD": {"signal_line": 0.02, "analysis": "Slight bullish - The MACD line is just above the signal line, indicating a small upward momentum."}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "Slightly bearish - The stock is below both the 50-day and 200-day moving averages, suggesting a long-term downward trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:58.701843", "processing_time": 5.906672, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 57, "analysis": "Neutral - RSI is neither overbought nor oversold."}, "MACD": {"signal_line": 0.02, "analysis": "Slight bullish - The MACD line is just above the signal line, indicating a small upward momentum."}, "Moving_Average": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "Slightly bearish - The stock is below both the 50-day and 200-day moving averages, suggesting a long-term downward trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:46:58.701843", "processing_time": 5.906672, "llm_used": true}, "processing_time": 5.906672, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 22, "total_processing_time": 162.410059}}, {"timestamp": "2025-07-05T22:47:02.203102", "output_id": "output_20250705_224702_7db15691", "input_id": "input_20250705_224657_97925b67", "prompt_id": "prompt_20250705_224657_9db06931", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is neutral, suggesting no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "analysis": "MACD histogram is close to zero, indicating a lack of momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently trading above the 50-day MA but below the 200-day MA, suggesting a short-term bullish trend but a long-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:02.188087", "processing_time": 4.38876, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.1, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is neutral, suggesting no strong overbought or oversold conditions."}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "analysis": "MACD histogram is close to zero, indicating a lack of momentum in either direction."}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 145.0, "analysis": "The stock is currently trading above the 50-day MA but below the 200-day MA, suggesting a short-term bullish trend but a long-term neutral trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:02.188087", "processing_time": 4.38876, "llm_used": true}, "processing_time": 4.38876, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 23, "total_processing_time": 166.79881899999998}}, {"timestamp": "2025-07-05T22:47:02.586313", "output_id": "output_20250705_224702_34471122", "input_id": "input_20250705_224658_eaad4e7c", "prompt_id": "prompt_20250705_224658_f1c02c61", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:02.586313", "processing_time": 3.922063, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 58, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:02.586313", "processing_time": 3.922063, "llm_used": true}, "processing_time": 3.922063, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 24, "total_processing_time": 170.720882}}, {"timestamp": "2025-07-05T22:47:02.900302", "output_id": "output_20250705_224702_519e7c20", "input_id": "input_20250705_224657_df3da60b", "prompt_id": "prompt_20250705_224657_83fc0f0c", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 148.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:02.899303", "processing_time": 5.414115, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 55, "signal": "neutral"}, "MACD": {"signal_line": 0.0, "histogram": 0.0, "signal": "neutral"}, "Moving_Average": {"50_day_MA": 152.5, "200_day_MA": 148.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:02.899303", "processing_time": 5.414115, "llm_used": true}, "processing_time": 5.414115, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 25, "total_processing_time": 176.134997}}, {"timestamp": "2025-07-05T22:47:05.171058", "output_id": "output_20250705_224705_a3ca88c5", "input_id": "input_20250705_224656_ec4e0e0c", "prompt_id": "prompt_20250705_224656_94b12f1c", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal": "neutral", "hypothesis": "MACD line is close to the signal line, indicating no strong trend."}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 160.0, "signal": "neutral", "hypothesis": "Stock is trading between its 50-day and 200-day moving averages, suggesting a lack of strong directional momentum."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:05.171058", "processing_time": 9.094288, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "signal": "neutral"}, "MACD": {"signal": "neutral", "hypothesis": "MACD line is close to the signal line, indicating no strong trend."}, "Moving_Average": {"50_day_MA": 152.0, "200_day_MA": 160.0, "signal": "neutral", "hypothesis": "Stock is trading between its 50-day and 200-day moving averages, suggesting a lack of strong directional momentum."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:05.171058", "processing_time": 9.094288, "llm_used": true}, "processing_time": 9.094288, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 26, "total_processing_time": 185.229285}}, {"timestamp": "2025-07-05T22:47:05.215942", "output_id": "output_20250705_224705_6278a1ca", "input_id": "input_20250705_224658_a2205dcc", "prompt_id": "prompt_20250705_224658_4b5a7e98", "raw_response": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "analysis": "Neutral - RSI is neither overbought nor oversold, indicating a stable trend."}, "MACD": {"signal_line": 0.05, "analysis": "Neutral - The MACD signal line is close to zero, suggesting no strong bullish or bearish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "Neutral - The stock is currently trading between its 50-day and 200-day moving averages, indicating a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:05.215942", "processing_time": 6.686063, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.2, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "analysis": "Neutral - RSI is neither overbought nor oversold, indicating a stable trend."}, "MACD": {"signal_line": 0.05, "analysis": "Neutral - The MACD signal line is close to zero, suggesting no strong bullish or bearish trend."}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "analysis": "Neutral - The stock is currently trading between its 50-day and 200-day moving averages, indicating a sideways trend."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:05.215942", "processing_time": 6.686063, "llm_used": true}, "processing_time": 6.686063, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 27, "total_processing_time": 191.915348}}, {"timestamp": "2025-07-05T22:47:06.367235", "output_id": "output_20250705_224706_6e7024ad", "input_id": "input_20250705_224702_ae84bda6", "prompt_id": "prompt_20250705_224702_0a9f0f0b", "raw_response": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50, "interpretation": "Neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "No clear trend direction"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Stock is currently between the 50-day and 200-day moving averages, indicating a neutral trend"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:06.367235", "processing_time": 4.02916, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.0, "trading_signal": "neutral", "signal_strength": 0.0, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 50, "interpretation": "Neutral"}, "MACD": {"signal_line": 0, "histogram": 0, "interpretation": "No clear trend direction"}, "Moving_Average": {"50_day_MA": 155.0, "200_day_MA": 145.0, "interpretation": "Stock is currently between the 50-day and 200-day moving averages, indicating a neutral trend"}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:06.367235", "processing_time": 4.02916, "llm_used": true}, "processing_time": 4.02916, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 28, "total_processing_time": 195.94450799999998}}, {"timestamp": "2025-07-05T22:47:06.636930", "output_id": "output_20250705_224706_303bd75e", "input_id": "input_20250705_224702_bd155295", "prompt_id": "prompt_20250705_224702_6953a5ce", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 61, "signal": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:06.636930", "processing_time": 4.422269, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.5, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 61, "signal": "neutral"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "signal": "neutral"}, "Moving_Averages": {"50_day_MA": 152.5, "200_day_MA": 160.0, "signal": "neutral"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:06.636930", "processing_time": 4.422269, "llm_used": true}, "processing_time": 4.422269, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 29, "total_processing_time": 200.36677699999998}}, {"timestamp": "2025-07-05T22:47:11.220657", "output_id": "output_20250705_224711_72a477be", "input_id": "input_20250705_224703_a00d28fc", "prompt_id": "prompt_20250705_224703_68b726ad", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "interpretation": "Neutral - neither overbought nor oversold"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "MACD signal line slightly above zero with a small negative histogram, indicating a weak bullish trend"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "interpretation": "The stock is currently above the 50-day MA but below the 200-day MA, suggesting a short-term bullish trend with a long-term neutral trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:11.220657", "processing_time": 7.522613, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 52, "interpretation": "Neutral - neither overbought nor oversold"}, "MACD": {"signal_line": 0.02, "histogram": -0.01, "interpretation": "MACD signal line slightly above zero with a small negative histogram, indicating a weak bullish trend"}, "Moving_Averages": {"50_day_MA": 152.0, "200_day_MA": 145.0, "interpretation": "The stock is currently above the 50-day MA but below the 200-day MA, suggesting a short-term bullish trend with a long-term neutral trend"}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:11.220657", "processing_time": 7.522613, "llm_used": true}, "processing_time": 7.522613, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 30, "total_processing_time": 207.88939}}, {"timestamp": "2025-07-05T22:47:11.714455", "output_id": "output_20250705_224711_13275358", "input_id": "input_20250705_224706_aa841176", "prompt_id": "prompt_20250705_224706_99898330", "raw_response": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is in the middle of the range, indicating neither overbought nor oversold conditions."}, "MACD": {"signal_line": 0.01, "analysis": "MACD signal line is close to the center, suggesting no strong trend direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "analysis": "The stock is currently below the 50-day and 200-day moving averages, indicating a bearish trend in the medium to long term."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:11.714455", "processing_time": 5.624339, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 55, "analysis": "RSI is in the middle of the range, indicating neither overbought nor oversold conditions."}, "MACD": {"signal_line": 0.01, "analysis": "MACD signal line is close to the center, suggesting no strong trend direction."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 170.0, "analysis": "The stock is currently below the 50-day and 200-day moving averages, indicating a bearish trend in the medium to long term."}}, "confidence": 0.7, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:11.714455", "processing_time": 5.624339, "llm_used": true}, "processing_time": 5.624339, "llm_used": true, "confidence": 0.7, "reasoning": "", "metadata": {"analysis_count": 31, "total_processing_time": 213.51372899999998}}, {"timestamp": "2025-07-05T22:47:13.717933", "output_id": "output_20250705_224713_4a592824", "input_id": "input_20250705_224706_7818f98e", "prompt_id": "prompt_20250705_224706_ef07eab2", "raw_response": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 57, "analysis": "RSI is close to the middle of its range, indicating a neutral market condition."}, "MACD": {"current_value": 0, "analysis": "The MACD is close to the zero line, suggesting a lack of strong directional momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "The 50-day moving average is below the 200-day moving average, hinting at a slightly bearish trend in the long term."}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:13.717933", "processing_time": 7.406108, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": -0.05, "trading_signal": "neutral", "signal_strength": 0.2, "support_level": 150.0, "resistance_level": 160.0, "indicators": {"RSI": {"current_value": 57, "analysis": "RSI is close to the middle of its range, indicating a neutral market condition."}, "MACD": {"current_value": 0, "analysis": "The MACD is close to the zero line, suggesting a lack of strong directional momentum."}, "Moving_Averages": {"50_day_MA": 155.0, "200_day_MA": 165.0, "analysis": "The 50-day moving average is below the 200-day moving average, hinting at a slightly bearish trend in the long term."}}, "confidence": 0.75, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:13.717933", "processing_time": 7.406108, "llm_used": true}, "processing_time": 7.406108, "llm_used": true, "confidence": 0.75, "reasoning": "", "metadata": {"analysis_count": 32, "total_processing_time": 220.91983699999997}}, {"timestamp": "2025-07-05T22:47:20.168082", "output_id": "output_20250705_224720_3da07d06", "input_id": "input_20250705_224709_8505e787", "prompt_id": "prompt_20250705_224709_50b6a9bd", "raw_response": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "analysis": "RSI is currently neutral, indicating neither overbought nor oversold conditions."}, "MACD": {"current_value": "0.0", "analysis": "The MACD line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, indicating a possible consolidation phase."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:20.168082", "processing_time": 10.314016, "llm_used": true}, "parsed_output": {"trend": "neutral", "technical_score": 0.1, "trading_signal": "neutral", "signal_strength": 0.3, "support_level": 145.0, "resistance_level": 155.0, "indicators": {"RSI": {"current_value": 50.5, "analysis": "RSI is currently neutral, indicating neither overbought nor oversold conditions."}, "MACD": {"current_value": "0.0", "analysis": "The MACD line is close to zero, suggesting a lack of strong momentum in either direction."}, "Moving_Averages": {"50_day_MA": 150.0, "200_day_MA": 160.0, "analysis": "The stock is currently between its 50-day and 200-day moving averages, indicating a possible consolidation phase."}}, "confidence": 0.8, "agent_id": "TAA", "timestamp": "2025-07-05T22:47:20.168082", "processing_time": 10.314016, "llm_used": true}, "processing_time": 10.314016, "llm_used": true, "confidence": 0.8, "reasoning": "", "metadata": {"analysis_count": 33, "total_processing_time": 231.23385299999998}}]